[

{"activeModifiers":[],"character":"B", "isARepeat":false,"processTimeMs": 669959140.0,"type":"keyDown", "unixTimeMs": 1752906736162.849121 },
{"activeModifiers":[],"character":"B", "isARepeat":false,"processTimeMs": 669959218.0,"type":"keyUp", "unixTimeMs": 1752906736230.062256 },
{"activeModifiers":[],"character":"A", "isARepeat":false,"processTimeMs": 669959218.0,"type":"keyDown", "unixTimeMs": 1752906736238.448486 },
{"activeModifiers":[],"character":"A", "isARepeat":false,"processTimeMs": 669959296.0,"type":"keyUp", "unixTimeMs": 1752906736318.462646 },
{"activeModifiers":[],"character":"N", "isARepeat":false,"processTimeMs": 669959312.0,"type":"keyDown", "unixTimeMs": 1752906736327.491211 },
{"activeModifiers":[],"character":"G", "isARepeat":false,"processTimeMs": 669959390.0,"type":"keyDown", "unixTimeMs": 1752906736406.275146 },
{"activeModifiers":[],"character":"N", "isARepeat":false,"processTimeMs": 669959406.0,"type":"keyUp", "unixTimeMs": 1752906736420.819580 },
{"activeModifiers":[],"character":"G", "isARepeat":false,"processTimeMs": 669959468.0,"type":"keyUp", "unixTimeMs": 1752906736494.082275 },
{"activeModifiers":[],"character":"W", "isARepeat":false,"processTimeMs": 669959546.0,"type":"keyDown", "unixTimeMs": 1752906736571.863770 },
{"activeModifiers":[],"character":"O", "isARepeat":false,"processTimeMs": 669959625.0,"type":"keyDown", "unixTimeMs": 1752906736646.632812 },
{"activeModifiers":[],"character":"W", "isARepeat":false,"processTimeMs": 669959640.0,"type":"keyUp", "unixTimeMs": 1752906736662.708496 },
{"activeModifiers":[],"character":"O", "isARepeat":false,"processTimeMs": 669959750.0,"type":"keyUp", "unixTimeMs": 1752906736761.830566 },
{"activeModifiers":[],"character":"Space", "isARepeat":false,"processTimeMs": 669959750.0,"type":"keyDown", "unixTimeMs": 1752906736771.856445 },
{"activeModifiers":[],"character":"Space", "isARepeat":false,"processTimeMs": 669959859.0,"type":"keyUp", "unixTimeMs": 1752906736880.905762 },
{"activeModifiers":[],"character":"Backspace", "isARepeat":false,"processTimeMs": 669960015.0,"type":"keyDown", "unixTimeMs": 1752906737038.441650 },
{"activeModifiers":[],"character":"Backspace", "isARepeat":false,"processTimeMs": 669960078.0,"type":"keyUp", "unixTimeMs": 1752906737098.672119 },
{"activeModifiers":[],"character":"Backspace", "isARepeat":false,"processTimeMs": 669960171.0,"type":"keyDown", "unixTimeMs": 1752906737187.483887 },
{"activeModifiers":[],"character":"Backspace", "isARepeat":false,"processTimeMs": 669960218.0,"type":"keyUp", "unixTimeMs": 1752906737236.701416 },
{"activeModifiers":[],"character":"Backspace", "isARepeat":false,"processTimeMs": 669960296.0,"type":"keyDown", "unixTimeMs": 1752906737318.111572 },
{"activeModifiers":[],"character":"Backspace", "isARepeat":false,"processTimeMs": 669960359.0,"type":"keyUp", "unixTimeMs": 1752906737379.394287 },
{"activeModifiers":[],"character":"Backspace", "isARepeat":false,"processTimeMs": 669960437.0,"type":"keyDown", "unixTimeMs": 1752906737451.031494 },
{"activeModifiers":[],"character":"Backspace", "isARepeat":false,"processTimeMs": 669960500.0,"type":"keyUp", "unixTimeMs": 1752906737522.052002 },
{"activeModifiers":[],"character":"Backspace", "isARepeat":false,"processTimeMs": 669960578.0,"type":"keyDown", "unixTimeMs": 1752906737597.915527 },
{"activeModifiers":[],"character":"Backspace", "isARepeat":false,"processTimeMs": 669960656.0,"type":"keyUp", "unixTimeMs": 1752906737669.358887 },
{"activeModifiers":[],"character":"Backspace", "isARepeat":false,"processTimeMs": 669960734.0,"type":"keyDown", "unixTimeMs": 1752906737754.728516 },
{"activeModifiers":[],"character":"Backspace", "isARepeat":false,"processTimeMs": 669960812.0,"type":"keyUp", "unixTimeMs": 1752906737825.793213 },
{"activeModifiers":[],"character":"Backspace", "isARepeat":false,"processTimeMs": 669960875.0,"type":"keyDown", "unixTimeMs": 1752906737896.320801 },
{"activeModifiers":[],"character":"Backspace", "isARepeat":false,"processTimeMs": 669960984.0,"type":"keyUp", "unixTimeMs": 1752906738002.513916 },
{"activeModifiers":[],"character":"B", "isARepeat":false,"processTimeMs": 669961171.0,"type":"keyDown", "unixTimeMs": 1752906738188.595703 },
{"activeModifiers":[],"character":"A", "isARepeat":false,"processTimeMs": 669961234.0,"type":"keyDown", "unixTimeMs": 1752906738259.690674 },
{"activeModifiers":[],"character":"B", "isARepeat":false,"processTimeMs": 669961265.0,"type":"keyUp", "unixTimeMs": 1752906738277.378662 },
{"activeModifiers":[],"character":"A", "isARepeat":false,"processTimeMs": 669961343.0,"type":"keyUp", "unixTimeMs": 1752906738366.454346 },
{"activeModifiers":[],"character":"N", "isARepeat":false,"processTimeMs": 669961359.0,"type":"keyDown", "unixTimeMs": 1752906738374.490479 },
{"activeModifiers":[],"character":"N", "isARepeat":false,"processTimeMs": 669961453.0,"type":"keyUp", "unixTimeMs": 1752906738467.611572 },
{"activeModifiers":[],"character":"G", "isARepeat":false,"processTimeMs": 669961468.0,"type":"keyDown", "unixTimeMs": 1752906738486.684814 },
{"activeModifiers":[],"character":"G", "isARepeat":false,"processTimeMs": 669961515.0,"type":"keyUp", "unixTimeMs": 1752906738531.815918 },
{"activeModifiers":[],"character":"W", "isARepeat":false,"processTimeMs": 669961625.0,"type":"keyDown", "unixTimeMs": 1752906738637.474121 },
{"activeModifiers":[],"character":"W", "isARepeat":false,"processTimeMs": 669961671.0,"type":"keyUp", "unixTimeMs": 1752906738696.303711 },
{"activeModifiers":[],"character":"O", "isARepeat":false,"processTimeMs": 669961718.0,"type":"keyDown", "unixTimeMs": 1752906738735.523193 },
{"activeModifiers":[],"character":"O", "isARepeat":false,"processTimeMs": 669961796.0,"type":"keyUp", "unixTimeMs": 1752906738816.665283 },
{"activeModifiers":[],"character":"Space", "isARepeat":false,"processTimeMs": 669961828.0,"type":"keyDown", "unixTimeMs": 1752906738822.413574 },
{"activeModifiers":[],"character":"Space", "isARepeat":false,"processTimeMs": 669961921.0,"type":"keyUp", "unixTimeMs": 1752906738933.083496 },
{"activeModifiers":[],"character":"B", "isARepeat":false,"processTimeMs": 669962218.0,"type":"keyDown", "unixTimeMs": 1752906739236.906494 },
{"activeModifiers":[],"character":"B", "isARepeat":false,"processTimeMs": 669962296.0,"type":"keyUp", "unixTimeMs": 1752906739314.991211 },
{"activeModifiers":[],"character":"I", "isARepeat":false,"processTimeMs": 669962406.0,"type":"keyDown", "unixTimeMs": 1752906739423.196533 },
{"activeModifiers":[],"character":"I", "isARepeat":false,"processTimeMs": 669962453.0,"type":"keyUp", "unixTimeMs": 1752906739468.086426 },
{"activeModifiers":[],"character":"A", "isARepeat":false,"processTimeMs": 669962453.0,"type":"keyDown", "unixTimeMs": 1752906739473.085205 },
{"activeModifiers":[],"character":"A", "isARepeat":false,"processTimeMs": 669962531.0,"type":"keyUp", "unixTimeMs": 1752906739556.427246 },
{"activeModifiers":[],"character":"N", "isARepeat":false,"processTimeMs": 669962546.0,"type":"keyDown", "unixTimeMs": 1752906739570.271973 },
{"activeModifiers":[],"character":"N", "isARepeat":false,"processTimeMs": 669962640.0,"type":"keyUp", "unixTimeMs": 1752906739652.289307 },
{"activeModifiers":[],"character":"X", "isARepeat":false,"processTimeMs": 669962843.0,"type":"keyDown", "unixTimeMs": 1752906739864.504883 },
{"activeModifiers":[],"character":"X", "isARepeat":false,"processTimeMs": 669962921.0,"type":"keyUp", "unixTimeMs": 1752906739936.324951 },
{"activeModifiers":[],"character":"I", "isARepeat":false,"processTimeMs": 669962921.0,"type":"keyDown", "unixTimeMs": 1752906739946.421143 },
{"activeModifiers":[],"character":"I", "isARepeat":false,"processTimeMs": 669963015.0,"type":"keyUp", "unixTimeMs": 1752906740028.914062 },
{"activeModifiers":[],"character":"E", "isARepeat":false,"processTimeMs": 669963015.0,"type":"keyDown", "unixTimeMs": 1752906740036.951660 },
{"activeModifiers":[],"character":"E", "isARepeat":false,"processTimeMs": 669963125.0,"type":"keyUp", "unixTimeMs": 1752906740138.948486 },
{"activeModifiers":[],"character":"Space", "isARepeat":false,"processTimeMs": 669963125.0,"type":"keyDown", "unixTimeMs": 1752906740149.540283 },
{"activeModifiers":[],"character":"Space", "isARepeat":false,"processTimeMs": 669963250.0,"type":"keyUp", "unixTimeMs": 1752906740269.047119 },
{"activeModifiers":[],"character":"Y", "isARepeat":false,"processTimeMs": 669963359.0,"type":"keyDown", "unixTimeMs": 1752906740374.782471 },
{"activeModifiers":[],"character":"Y", "isARepeat":false,"processTimeMs": 669963453.0,"type":"keyUp", "unixTimeMs": 1752906740469.344727 },
{"activeModifiers":[],"character":"I", "isARepeat":false,"processTimeMs": 669963562.0,"type":"keyDown", "unixTimeMs": 1752906740583.914307 },
{"activeModifiers":[],"character":"I", "isARepeat":false,"processTimeMs": 669963625.0,"type":"keyUp", "unixTimeMs": 1752906740649.874512 },
{"activeModifiers":[],"character":"G", "isARepeat":false,"processTimeMs": 669963734.0,"type":"keyDown", "unixTimeMs": 1752906740749.825684 },
{"activeModifiers":[],"character":"E", "isARepeat":false,"processTimeMs": 669963796.0,"type":"keyDown", "unixTimeMs": 1752906740812.371094 },
{"activeModifiers":[],"character":"G", "isARepeat":false,"processTimeMs": 669963812.0,"type":"keyUp", "unixTimeMs": 1752906740827.980469 },
{"activeModifiers":[],"character":"Space", "isARepeat":false,"processTimeMs": 669963875.0,"type":"keyDown", "unixTimeMs": 1752906740888.799561 },
{"activeModifiers":[],"character":"E", "isARepeat":false,"processTimeMs": 669963890.0,"type":"keyUp", "unixTimeMs": 1752906740904.008545 },
{"activeModifiers":[],"character":"Space", "isARepeat":false,"processTimeMs": 669963984.0,"type":"keyUp", "unixTimeMs": 1752906741005.486084 },
{"activeModifiers":[],"character":"P", "isARepeat":false,"processTimeMs": 669964390.0,"type":"keyDown", "unixTimeMs": 1752906741412.261719 },
{"activeModifiers":[],"character":"P", "isARepeat":false,"processTimeMs": 669964468.0,"type":"keyUp", "unixTimeMs": 1752906741485.952881 },
{"activeModifiers":[],"character":"Y", "isARepeat":false,"processTimeMs": 669964812.0,"type":"keyDown", "unixTimeMs": 1752906741827.052490 },
{"activeModifiers":[],"character":"Y", "isARepeat":false,"processTimeMs": 669964875.0,"type":"keyUp", "unixTimeMs": 1752906741896.026367 },
{"activeModifiers":[],"character":"T", "isARepeat":false,"processTimeMs": 669965000.0,"type":"keyDown", "unixTimeMs": 1752906742014.322510 },
{"activeModifiers":[],"character":"T", "isARepeat":false,"processTimeMs": 669965062.0,"type":"keyUp", "unixTimeMs": 1752906742086.243896 },
{"activeModifiers":[],"character":"H", "isARepeat":false,"processTimeMs": 669965171.0,"type":"keyDown", "unixTimeMs": 1752906742187.577393 },
{"activeModifiers":[],"character":"H", "isARepeat":false,"processTimeMs": 669965218.0,"type":"keyUp", "unixTimeMs": 1752906742236.776367 },
{"activeModifiers":[],"character":"O", "isARepeat":false,"processTimeMs": 669965359.0,"type":"keyDown", "unixTimeMs": 1752906742377.915527 },
{"activeModifiers":[],"character":"O", "isARepeat":false,"processTimeMs": 669965421.0,"type":"keyUp", "unixTimeMs": 1752906742442.122803 },
{"activeModifiers":[],"character":"N", "isARepeat":false,"processTimeMs": 669965468.0,"type":"keyDown", "unixTimeMs": 1752906742479.469727 },
{"activeModifiers":[],"character":"N", "isARepeat":false,"processTimeMs": 669965562.0,"type":"keyUp", "unixTimeMs": 1752906742586.921143 },
{"activeModifiers":[],"character":"Enter", "isARepeat":false,"processTimeMs": 669965796.0,"type":"keyDown", "unixTimeMs": 1752906742815.289307 },
{"activeModifiers":[],"character":"Enter", "isARepeat":false,"processTimeMs": 669965859.0,"type":"keyUp", "unixTimeMs": 1752906742873.206299 },
{"activeModifiers":[],"character":"J", "isARepeat":false,"processTimeMs": 669966734.0,"type":"keyDown", "unixTimeMs": 1752906743745.934570 },
{"activeModifiers":[],"character":"J", "isARepeat":false,"processTimeMs": 669966796.0,"type":"keyUp", "unixTimeMs": 1752906743817.593262 },
{"activeModifiers":[],"character":"I", "isARepeat":false,"processTimeMs": 669966890.0,"type":"keyDown", "unixTimeMs": 1752906743906.029541 },
{"activeModifiers":[],"character":"I", "isARepeat":false,"processTimeMs": 669966968.0,"type":"keyUp", "unixTimeMs": 1752906743988.228027 },
{"activeModifiers":[],"character":"A", "isARepeat":false,"processTimeMs": 669967156.0,"type":"keyDown", "unixTimeMs": 1752906744169.318115 },
{"activeModifiers":[],"character":"A", "isARepeat":false,"processTimeMs": 669967234.0,"type":"keyUp", "unixTimeMs": 1752906744246.302002 },
{"activeModifiers":[],"character":"O", "isARepeat":false,"processTimeMs": 669967296.0,"type":"keyDown", "unixTimeMs": 1752906744313.378418 },
{"activeModifiers":[],"character":"O", "isARepeat":false,"processTimeMs": 669967359.0,"type":"keyUp", "unixTimeMs": 1752906744379.880371 },
{"activeModifiers":[],"character":"B", "isARepeat":false,"processTimeMs": 669967515.0,"type":"keyDown", "unixTimeMs": 1752906744528.945312 },
{"activeModifiers":[],"character":"B", "isARepeat":false,"processTimeMs": 669967593.0,"type":"keyUp", "unixTimeMs": 1752906744606.207520 },
{"activeModifiers":[],"character":"E", "isARepeat":false,"processTimeMs": 669967671.0,"type":"keyDown", "unixTimeMs": 1752906744686.382080 },
{"activeModifiers":[],"character":"N", "isARepeat":false,"processTimeMs": 669967687.0,"type":"keyDown", "unixTimeMs": 1752906744700.035156 },
{"activeModifiers":[],"character":"E", "isARepeat":false,"processTimeMs": 669967734.0,"type":"keyUp", "unixTimeMs": 1752906744759.196289 },
{"activeModifiers":[],"character":"N", "isARepeat":false,"processTimeMs": 669967781.0,"type":"keyUp", "unixTimeMs": 1752906744796.791504 },
{"activeModifiers":[],"character":"Space", "isARepeat":false,"processTimeMs": 669967859.0,"type":"keyDown", "unixTimeMs": 1752906744878.595215 },
{"activeModifiers":[],"character":"Space", "isARepeat":false,"processTimeMs": 669967968.0,"type":"keyUp", "unixTimeMs": 1752906744993.229248 },
{"activeModifiers":[],"character":",", "isARepeat":false,"processTimeMs": 669968265.0,"type":"keyDown", "unixTimeMs": 1752906745287.455566 },
{"activeModifiers":[],"character":",", "isARepeat":false,"processTimeMs": 669968312.0,"type":"keyUp", "unixTimeMs": 1752906745332.571289 },
{"activeModifiers":[],"character":"L", "isARepeat":false,"processTimeMs": 669969156.0,"type":"keyDown", "unixTimeMs": 1752906746174.304688 },
{"activeModifiers":[],"character":"L", "isARepeat":false,"processTimeMs": 669969203.0,"type":"keyUp", "unixTimeMs": 1752906746227.949951 },
{"activeModifiers":[],"character":"I", "isARepeat":false,"processTimeMs": 669969312.0,"type":"keyDown", "unixTimeMs": 1752906746327.018799 },
{"activeModifiers":[],"character":"I", "isARepeat":false,"processTimeMs": 669969359.0,"type":"keyUp", "unixTimeMs": 1752906746375.270508 },
{"activeModifiers":[],"character":"M", "isARepeat":false,"processTimeMs": 669969468.0,"type":"keyDown", "unixTimeMs": 1752906746494.366699 },
{"activeModifiers":[],"character":"M", "isARepeat":false,"processTimeMs": 669969531.0,"type":"keyUp", "unixTimeMs": 1752906746555.043945 },
{"activeModifiers":[],"character":"I", "isARepeat":false,"processTimeMs": 669969609.0,"type":"keyDown", "unixTimeMs": 1752906746624.417236 },
{"activeModifiers":[],"character":"I", "isARepeat":false,"processTimeMs": 669969671.0,"type":"keyUp", "unixTimeMs": 1752906746684.350830 },
{"activeModifiers":[],"character":"A", "isARepeat":false,"processTimeMs": 669969671.0,"type":"keyDown", "unixTimeMs": 1752906746694.516113 },
{"activeModifiers":[],"character":"N", "isARepeat":false,"processTimeMs": 669969765.0,"type":"keyDown", "unixTimeMs": 1752906746778.347412 },
{"activeModifiers":[],"character":"A", "isARepeat":false,"processTimeMs": 669969781.0,"type":"keyUp", "unixTimeMs": 1752906746800.486084 },
{"activeModifiers":[],"character":"Space", "isARepeat":false,"processTimeMs": 669969828.0,"type":"keyDown", "unixTimeMs": 1752906746844.594482 },
{"activeModifiers":[],"character":"N", "isARepeat":false,"processTimeMs": 669969843.0,"type":"keyUp", "unixTimeMs": 1752906746856.844727 },
{"activeModifiers":[],"character":"Space", "isARepeat":false,"processTimeMs": 669969921.0,"type":"keyUp", "unixTimeMs": 1752906746940.339844 },
{"activeModifiers":[],"character":"Y", "isARepeat":false,"processTimeMs": 669969968.0,"type":"keyDown", "unixTimeMs": 1752906746993.431152 },
{"activeModifiers":[],"character":"Y", "isARepeat":false,"processTimeMs": 669970062.0,"type":"keyUp", "unixTimeMs": 1752906747084.447266 },
{"activeModifiers":[],"character":"O", "isARepeat":false,"processTimeMs": 669970156.0,"type":"keyDown", "unixTimeMs": 1752906747178.232910 },
{"activeModifiers":[],"character":"O", "isARepeat":false,"processTimeMs": 669970218.0,"type":"keyUp", "unixTimeMs": 1752906747244.288086 },
{"activeModifiers":[],"character":"U", "isARepeat":false,"processTimeMs": 669970312.0,"type":"keyDown", "unixTimeMs": 1752906747324.469482 },
{"activeModifiers":[],"character":"Space", "isARepeat":false,"processTimeMs": 669970375.0,"type":"keyDown", "unixTimeMs": 1752906747393.344482 },
{"activeModifiers":[],"character":"U", "isARepeat":false,"processTimeMs": 669970390.0,"type":"keyUp", "unixTimeMs": 1752906747411.480713 },
{"activeModifiers":[],"character":"Space", "isARepeat":false,"processTimeMs": 669970484.0,"type":"keyUp", "unixTimeMs": 1752906747503.633545 },
{"activeModifiers":[],"character":"L", "isARepeat":false,"processTimeMs": 669971109.0,"type":"keyDown", "unixTimeMs": 1752906748124.504883 },
{"activeModifiers":[],"character":"L", "isARepeat":false,"processTimeMs": 669971171.0,"type":"keyUp", "unixTimeMs": 1752906748190.238281 },
{"activeModifiers":[],"character":"I", "isARepeat":false,"processTimeMs": 669971265.0,"type":"keyDown", "unixTimeMs": 1752906748277.331543 },
{"activeModifiers":[],"character":"A", "isARepeat":false,"processTimeMs": 669971312.0,"type":"keyDown", "unixTimeMs": 1752906748324.776855 },
{"activeModifiers":[],"character":"I", "isARepeat":false,"processTimeMs": 669971328.0,"type":"keyUp", "unixTimeMs": 1752906748340.489502 },
{"activeModifiers":[],"character":"A", "isARepeat":false,"processTimeMs": 669971421.0,"type":"keyUp", "unixTimeMs": 1752906748435.511230 },
{"activeModifiers":[],"character":"N", "isARepeat":false,"processTimeMs": 669971437.0,"type":"keyDown", "unixTimeMs": 1752906748449.154053 },
{"activeModifiers":[],"character":"N", "isARepeat":false,"processTimeMs": 669971515.0,"type":"keyUp", "unixTimeMs": 1752906748529.395508 },
{"activeModifiers":[],"character":"G", "isARepeat":false,"processTimeMs": 669971734.0,"type":"keyDown", "unixTimeMs": 1752906748753.832520 },
{"activeModifiers":[],"character":"G", "isARepeat":false,"processTimeMs": 669971812.0,"type":"keyUp", "unixTimeMs": 1752906748838.225586 },
{"activeModifiers":[],"character":"E", "isARepeat":false,"processTimeMs": 669972437.0,"type":"keyDown", "unixTimeMs": 1752906749448.020264 },
{"activeModifiers":[],"character":"E", "isARepeat":false,"processTimeMs": 669972531.0,"type":"keyUp", "unixTimeMs": 1752906749549.301270 },
{"activeModifiers":[],"character":"Space", "isARepeat":false,"processTimeMs": 669973125.0,"type":"keyDown", "unixTimeMs": 1752906750143.586426 },
{"activeModifiers":[],"character":"Space", "isARepeat":false,"processTimeMs": 669973234.0,"type":"keyUp", "unixTimeMs": 1752906750253.822021 },
{"activeModifiers":[],"character":"Backspace", "isARepeat":false,"processTimeMs": 669973343.0,"type":"keyDown", "unixTimeMs": 1752906750360.668457 },
{"activeModifiers":[],"character":"Backspace", "isARepeat":false,"processTimeMs": 669973406.0,"type":"keyUp", "unixTimeMs": 1752906750431.826416 },
{"activeModifiers":[],"character":"Backspace", "isARepeat":false,"processTimeMs": 669973515.0,"type":"keyDown", "unixTimeMs": 1752906750531.902832 },
{"activeModifiers":[],"character":"Backspace", "isARepeat":false,"processTimeMs": 669973593.0,"type":"keyUp", "unixTimeMs": 1752906750608.480713 },
{"activeModifiers":[],"character":"L", "isARepeat":false,"processTimeMs": 669973781.0,"type":"keyDown", "unixTimeMs": 1752906750791.709961 },
{"activeModifiers":[],"character":"L", "isARepeat":false,"processTimeMs": 669973859.0,"type":"keyUp", "unixTimeMs": 1752906750874.545166 },
{"activeModifiers":[],"character":"I", "isARepeat":false,"processTimeMs": 669973937.0,"type":"keyDown", "unixTimeMs": 1752906750962.839600 },
{"activeModifiers":[],"character":"A", "isARepeat":false,"processTimeMs": 669974000.0,"type":"keyDown", "unixTimeMs": 1752906751019.262451 },
{"activeModifiers":[],"character":"I", "isARepeat":false,"processTimeMs": 669974015.0,"type":"keyUp", "unixTimeMs": 1752906751035.755615 },
{"activeModifiers":[],"character":"A", "isARepeat":false,"processTimeMs": 669974078.0,"type":"keyUp", "unixTimeMs": 1752906751101.565918 },
{"activeModifiers":[],"character":"N", "isARepeat":false,"processTimeMs": 669974093.0,"type":"keyDown", "unixTimeMs": 1752906751118.128418 },
{"activeModifiers":[],"character":"N", "isARepeat":false,"processTimeMs": 669974187.0,"type":"keyUp", "unixTimeMs": 1752906751206.480469 },
{"activeModifiers":[],"character":"G", "isARepeat":false,"processTimeMs": 669974718.0,"type":"keyDown", "unixTimeMs": 1752906751730.786865 },
{"activeModifiers":[],"character":"E", "isARepeat":false,"processTimeMs": 669974781.0,"type":"keyDown", "unixTimeMs": 1752906751805.198486 },
{"activeModifiers":[],"character":"G", "isARepeat":false,"processTimeMs": 669974796.0,"type":"keyUp", "unixTimeMs": 1752906751810.210938 },
{"activeModifiers":[],"character":"E", "isARepeat":false,"processTimeMs": 669974812.0,"type":"keyUp", "unixTimeMs": 1752906751836.854736 },
{"activeModifiers":[],"character":"Z", "isARepeat":false,"processTimeMs": 669975046.0,"type":"keyDown", "unixTimeMs": 1752906752065.404541 },
{"activeModifiers":[],"character":"Z", "isARepeat":false,"processTimeMs": 669975125.0,"type":"keyUp", "unixTimeMs": 1752906752141.964355 },
{"activeModifiers":[],"character":"H", "isARepeat":false,"processTimeMs": 669975140.0,"type":"keyDown", "unixTimeMs": 1752906752153.020020 },
{"activeModifiers":[],"character":"H", "isARepeat":false,"processTimeMs": 669975187.0,"type":"keyUp", "unixTimeMs": 1752906752211.012451 },
{"activeModifiers":[],"character":"I", "isARepeat":false,"processTimeMs": 669975281.0,"type":"keyDown", "unixTimeMs": 1752906752295.614502 },
{"activeModifiers":[],"character":"I", "isARepeat":false,"processTimeMs": 669975421.0,"type":"keyUp", "unixTimeMs": 1752906752417.396240 },
{"activeModifiers":[],"character":"Space", "isARepeat":false,"processTimeMs": 669975843.0,"type":"keyDown", "unixTimeMs": 1752906752855.942871 },
{"activeModifiers":[],"character":"Space", "isARepeat":false,"processTimeMs": 669975937.0,"type":"keyUp", "unixTimeMs": 1752906752959.614990 },
{"activeModifiers":[],"character":"Backspace", "isARepeat":false,"processTimeMs": 669976015.0,"type":"keyDown", "unixTimeMs": 1752906753026.466309 },
{"activeModifiers":[],"character":"Backspace", "isARepeat":false,"processTimeMs": 669976078.0,"type":"keyUp", "unixTimeMs": 1752906753095.402344 },
{"activeModifiers":[],"character":"Backspace", "isARepeat":false,"processTimeMs": 669976156.0,"type":"keyDown", "unixTimeMs": 1752906753181.829102 },
{"activeModifiers":[],"character":"Backspace", "isARepeat":false,"processTimeMs": 669976234.0,"type":"keyUp", "unixTimeMs": 1752906753245.137207 },
{"activeModifiers":[],"character":"Backspace", "isARepeat":false,"processTimeMs": 669976312.0,"type":"keyDown", "unixTimeMs": 1752906753330.586914 },
{"activeModifiers":[],"character":"Backspace", "isARepeat":false,"processTimeMs": 669976406.0,"type":"keyUp", "unixTimeMs": 1752906753416.579590 },
{"activeModifiers":[],"character":"L", "isARepeat":false,"processTimeMs": 669976515.0,"type":"keyDown", "unixTimeMs": 1752906753533.155273 },
{"activeModifiers":[],"character":"L", "isARepeat":false,"processTimeMs": 669976593.0,"type":"keyUp", "unixTimeMs": 1752906753614.103760 },
{"activeModifiers":[],"character":"I", "isARepeat":false,"processTimeMs": 669976656.0,"type":"keyDown", "unixTimeMs": 1752906753676.329102 },
{"activeModifiers":[],"character":"A", "isARepeat":false,"processTimeMs": 669976718.0,"type":"keyDown", "unixTimeMs": 1752906753735.530518 },
{"activeModifiers":[],"character":"I", "isARepeat":false,"processTimeMs": 669976734.0,"type":"keyUp", "unixTimeMs": 1752906753754.896484 },
{"activeModifiers":[],"character":"A", "isARepeat":false,"processTimeMs": 669976796.0,"type":"keyUp", "unixTimeMs": 1752906753821.793457 },
{"activeModifiers":[],"character":"N", "isARepeat":false,"processTimeMs": 669976812.0,"type":"keyDown", "unixTimeMs": 1752906753831.197510 },
{"activeModifiers":[],"character":"G", "isARepeat":false,"processTimeMs": 669976875.0,"type":"keyDown", "unixTimeMs": 1752906753899.717285 },
{"activeModifiers":[],"character":"N", "isARepeat":false,"processTimeMs": 669976906.0,"type":"keyUp", "unixTimeMs": 1752906753924.177490 },
{"activeModifiers":[],"character":"G", "isARepeat":false,"processTimeMs": 669976953.0,"type":"keyUp", "unixTimeMs": 1752906753970.156494 },
{"activeModifiers":[],"character":"G", "isARepeat":false,"processTimeMs": 669977031.0,"type":"keyDown", "unixTimeMs": 1752906754050.906738 },
{"activeModifiers":[],"character":"E", "isARepeat":false,"processTimeMs": 669977078.0,"type":"keyDown", "unixTimeMs": 1752906754101.832031 },
{"activeModifiers":[],"character":"G", "isARepeat":false,"processTimeMs": 669977125.0,"type":"keyUp", "unixTimeMs": 1752906754141.842285 },
{"activeModifiers":[],"character":"E", "isARepeat":false,"processTimeMs": 669977171.0,"type":"keyUp", "unixTimeMs": 1752906754191.991699 },
{"activeModifiers":[],"character":"Space", "isARepeat":false,"processTimeMs": 669977328.0,"type":"keyDown", "unixTimeMs": 1752906754344.839111 },
{"activeModifiers":[],"character":"Space", "isARepeat":false,"processTimeMs": 669977421.0,"type":"keyUp", "unixTimeMs": 1752906754438.570557 },
{"activeModifiers":[],"character":"Z", "isARepeat":false,"processTimeMs": 669977453.0,"type":"keyDown", "unixTimeMs": 1752906754476.348877 },
{"activeModifiers":[],"character":"H", "isARepeat":false,"processTimeMs": 669977531.0,"type":"keyDown", "unixTimeMs": 1752906754546.732910 },
{"activeModifiers":[],"character":"Z", "isARepeat":false,"processTimeMs": 669977546.0,"type":"keyUp", "unixTimeMs": 1752906754570.701904 },
{"activeModifiers":[],"character":"H", "isARepeat":false,"processTimeMs": 669977593.0,"type":"keyUp", "unixTimeMs": 1752906754608.884033 },
{"activeModifiers":[],"character":"I", "isARepeat":false,"processTimeMs": 669977656.0,"type":"keyDown", "unixTimeMs": 1752906754679.585693 },
{"activeModifiers":[],"character":"I", "isARepeat":false,"processTimeMs": 669977781.0,"type":"keyUp", "unixTimeMs": 1752906754804.041992 },
{"activeModifiers":[],"character":"7", "isARepeat":false,"processTimeMs": 669980000.0,"type":"keyDown", "unixTimeMs": 1752906757023.053955 },
{"activeModifiers":[],"character":"7", "isARepeat":false,"processTimeMs": 669980062.0,"type":"keyUp", "unixTimeMs": 1752906757084.187256 },
{"activeModifiers":[],"character":",", "isARepeat":false,"processTimeMs": 669980703.0,"type":"keyDown", "unixTimeMs": 1752906757726.986084 },
{"activeModifiers":[],"character":",", "isARepeat":false,"processTimeMs": 669980765.0,"type":"keyUp", "unixTimeMs": 1752906757782.342041 },
{"activeModifiers":[],"character":"Backspace", "isARepeat":false,"processTimeMs": 669981125.0,"type":"keyDown", "unixTimeMs": 1752906758138.468506 },
{"activeModifiers":[],"character":"Backspace", "isARepeat":false,"processTimeMs": 669981218.0,"type":"keyUp", "unixTimeMs": 1752906758230.973145 },
{"activeModifiers":[],"character":".", "isARepeat":false,"processTimeMs": 669981375.0,"type":"keyDown", "unixTimeMs": 1752906758394.055664 },
{"activeModifiers":[],"character":".", "isARepeat":false,"processTimeMs": 669981437.0,"type":"keyUp", "unixTimeMs": 1752906758453.102051 },
{"activeModifiers":[],"character":"Y", "isARepeat":false,"processTimeMs": 669981984.0,"type":"keyDown", "unixTimeMs": 1752906759007.761719 },
{"activeModifiers":[],"character":"Y", "isARepeat":false,"processTimeMs": 669982078.0,"type":"keyUp", "unixTimeMs": 1752906759092.302002 },
{"activeModifiers":[],"character":"I", "isARepeat":false,"processTimeMs": 669982171.0,"type":"keyDown", "unixTimeMs": 1752906759194.307617 },
{"activeModifiers":[],"character":"I", "isARepeat":false,"processTimeMs": 669982234.0,"type":"keyUp", "unixTimeMs": 1752906759255.805908 },
{"activeModifiers":[],"character":"G", "isARepeat":false,"processTimeMs": 669982343.0,"type":"keyDown", "unixTimeMs": 1752906759362.125732 },
{"activeModifiers":[],"character":"G", "isARepeat":false,"processTimeMs": 669982437.0,"type":"keyUp", "unixTimeMs": 1752906759455.626709 },
{"activeModifiers":[],"character":"E", "isARepeat":false,"processTimeMs": 669982437.0,"type":"keyDown", "unixTimeMs": 1752906759463.154053 },
{"activeModifiers":[],"character":"Space", "isARepeat":false,"processTimeMs": 669982531.0,"type":"keyDown", "unixTimeMs": 1752906759553.199707 },
{"activeModifiers":[],"character":"E", "isARepeat":false,"processTimeMs": 669982562.0,"type":"keyUp", "unixTimeMs": 1752906759580.323975 },
{"activeModifiers":[],"character":"Space", "isARepeat":false,"processTimeMs": 669982640.0,"type":"keyUp", "unixTimeMs": 1752906759657.807861 },
{"activeModifiers":[],"character":"Z", "isARepeat":false,"processTimeMs": 669982703.0,"type":"keyDown", "unixTimeMs": 1752906759724.921631 },
{"activeModifiers":[],"character":"H", "isARepeat":false,"processTimeMs": 669982781.0,"type":"keyDown", "unixTimeMs": 1752906759795.222168 },
{"activeModifiers":[],"character":"Z", "isARepeat":false,"processTimeMs": 669982796.0,"type":"keyUp", "unixTimeMs": 1752906759809.752441 },
{"activeModifiers":[],"character":"H", "isARepeat":false,"processTimeMs": 669982843.0,"type":"keyUp", "unixTimeMs": 1752906759867.955078 },
{"activeModifiers":[],"character":"I", "isARepeat":false,"processTimeMs": 669982921.0,"type":"keyDown", "unixTimeMs": 1752906759938.656738 },
{"activeModifiers":[],"character":"I", "isARepeat":false,"processTimeMs": 669983062.0,"type":"keyUp", "unixTimeMs": 1752906760081.722168 },
{"activeModifiers":[],"character":"Space", "isARepeat":false,"processTimeMs": 669983546.0,"type":"keyDown", "unixTimeMs": 1752906760557.754883 },
{"activeModifiers":[],"character":"Space", "isARepeat":false,"processTimeMs": 669983625.0,"type":"keyUp", "unixTimeMs": 1752906760641.544922 },
{"activeModifiers":[],"character":"S", "isARepeat":false,"processTimeMs": 669983718.0,"type":"keyDown", "unixTimeMs": 1752906760736.047852 },
{"activeModifiers":[],"character":"S", "isARepeat":false,"processTimeMs": 669983765.0,"type":"keyUp", "unixTimeMs": 1752906760788.004883 },
{"activeModifiers":[],"character":"H", "isARepeat":false,"processTimeMs": 669983781.0,"type":"keyDown", "unixTimeMs": 1752906760796.571777 },
{"activeModifiers":[],"character":"H", "isARepeat":false,"processTimeMs": 669983843.0,"type":"keyUp", "unixTimeMs": 1752906760866.729980 },
{"activeModifiers":[],"character":"I", "isARepeat":false,"processTimeMs": 669983921.0,"type":"keyDown", "unixTimeMs": 1752906760939.725098 },
{"activeModifiers":[],"character":"Space", "isARepeat":false,"processTimeMs": 669983984.0,"type":"keyDown", "unixTimeMs": 1752906760997.989502 },
{"activeModifiers":[],"character":"I", "isARepeat":false,"processTimeMs": 669984000.0,"type":"keyUp", "unixTimeMs": 1752906761021.945312 },
{"activeModifiers":[],"character":"Space", "isARepeat":false,"processTimeMs": 669984078.0,"type":"keyUp", "unixTimeMs": 1752906761093.233154 },
{"activeModifiers":[],"character":"Z", "isARepeat":false,"processTimeMs": 669984171.0,"type":"keyDown", "unixTimeMs": 1752906761195.128906 },
{"activeModifiers":[],"character":"Z", "isARepeat":false,"processTimeMs": 669984265.0,"type":"keyUp", "unixTimeMs": 1752906761277.077637 },
{"activeModifiers":[],"character":"O", "isARepeat":false,"processTimeMs": 669984296.0,"type":"keyDown", "unixTimeMs": 1752906761316.074951 },
{"activeModifiers":[],"character":"O", "isARepeat":false,"processTimeMs": 669984359.0,"type":"keyUp", "unixTimeMs": 1752906761372.373535 },
{"activeModifiers":[],"character":"N", "isARepeat":false,"processTimeMs": 669984468.0,"type":"keyDown", "unixTimeMs": 1752906761488.865234 },
{"activeModifiers":[],"character":"G", "isARepeat":false,"processTimeMs": 669984500.0,"type":"keyDown", "unixTimeMs": 1752906761520.752441 },
{"activeModifiers":[],"character":"N", "isARepeat":false,"processTimeMs": 669984546.0,"type":"keyUp", "unixTimeMs": 1752906761560.575928 },
{"activeModifiers":[],"character":"G", "isARepeat":false,"processTimeMs": 669984578.0,"type":"keyUp", "unixTimeMs": 1752906761593.619629 },
{"activeModifiers":[],"character":"C", "isARepeat":false,"processTimeMs": 669984687.0,"type":"keyDown", "unixTimeMs": 1752906761708.001953 },
{"activeModifiers":[],"character":"H", "isARepeat":false,"processTimeMs": 669984765.0,"type":"keyDown", "unixTimeMs": 1752906761791.105957 },
{"activeModifiers":[],"character":"C", "isARepeat":false,"processTimeMs": 669984781.0,"type":"keyUp", "unixTimeMs": 1752906761805.327881 },
{"activeModifiers":[],"character":"A", "isARepeat":false,"processTimeMs": 669984859.0,"type":"keyDown", "unixTimeMs": 1752906761881.152344 },
{"activeModifiers":[],"character":"H", "isARepeat":false,"processTimeMs": 669984890.0,"type":"keyUp", "unixTimeMs": 1752906761904.223633 },
{"activeModifiers":[],"character":"A", "isARepeat":false,"processTimeMs": 669984937.0,"type":"keyUp", "unixTimeMs": 1752906761947.958740 },
{"activeModifiers":[],"character":"N", "isARepeat":false,"processTimeMs": 669984937.0,"type":"keyDown", "unixTimeMs": 1752906761960.974854 },
{"activeModifiers":[],"character":"G", "isARepeat":false,"processTimeMs": 669985031.0,"type":"keyDown", "unixTimeMs": 1752906762048.562256 },
{"activeModifiers":[],"character":"N", "isARepeat":false,"processTimeMs": 669985046.0,"type":"keyUp", "unixTimeMs": 1752906762071.043213 },
{"activeModifiers":[],"character":"G", "isARepeat":false,"processTimeMs": 669985109.0,"type":"keyUp", "unixTimeMs": 1752906762124.296387 },
{"activeModifiers":[],"character":"D", "isARepeat":false,"processTimeMs": 669985218.0,"type":"keyDown", "unixTimeMs": 1752906762234.416504 },
{"activeModifiers":[],"character":"D", "isARepeat":false,"processTimeMs": 669985312.0,"type":"keyUp", "unixTimeMs": 1752906762333.271240 },
{"activeModifiers":[],"character":"U", "isARepeat":false,"processTimeMs": 669985328.0,"type":"keyDown", "unixTimeMs": 1752906762341.300049 },
{"activeModifiers":[],"character":"U", "isARepeat":false,"processTimeMs": 669985406.0,"type":"keyUp", "unixTimeMs": 1752906762426.474365 },
{"activeModifiers":[],"character":"Space", "isARepeat":false,"processTimeMs": 669985453.0,"type":"keyDown", "unixTimeMs": 1752906762475.683594 },
{"activeModifiers":[],"character":"Space", "isARepeat":false,"processTimeMs": 669985562.0,"type":"keyUp", "unixTimeMs": 1752906762586.760010 },
{"activeModifiers":[],"character":",", "isARepeat":false,"processTimeMs": 669986109.0,"type":"keyDown", "unixTimeMs": 1752906763132.712402 },
{"activeModifiers":[],"character":",", "isARepeat":false,"processTimeMs": 669986187.0,"type":"keyUp", "unixTimeMs": 1752906763200.969482 },
{"activeModifiers":[],"character":"L", "isARepeat":false,"processTimeMs": 669986421.0,"type":"keyDown", "unixTimeMs": 1752906763442.600830 },
{"activeModifiers":[],"character":"L", "isARepeat":false,"processTimeMs": 669986484.0,"type":"keyUp", "unixTimeMs": 1752906763506.941650 },
{"activeModifiers":[],"character":"I", "isARepeat":false,"processTimeMs": 669986593.0,"type":"keyDown", "unixTimeMs": 1752906763604.728516 },
{"activeModifiers":[],"character":"I", "isARepeat":false,"processTimeMs": 669986640.0,"type":"keyUp", "unixTimeMs": 1752906763653.478271 },
{"activeModifiers":[],"character":"N", "isARepeat":false,"processTimeMs": 669986734.0,"type":"keyDown", "unixTimeMs": 1752906763745.375977 },
{"activeModifiers":[],"character":"N", "isARepeat":false,"processTimeMs": 669986796.0,"type":"keyUp", "unixTimeMs": 1752906763817.895020 },
{"activeModifiers":[],"character":"G", "isARepeat":false,"processTimeMs": 669986812.0,"type":"keyDown", "unixTimeMs": 1752906763834.533203 },
{"activeModifiers":[],"character":"G", "isARepeat":false,"processTimeMs": 669986875.0,"type":"keyUp", "unixTimeMs": 1752906763894.513184 },
{"activeModifiers":[],"character":"Y", "isARepeat":false,"processTimeMs": 669986953.0,"type":"keyDown", "unixTimeMs": 1752906763971.320068 },
{"activeModifiers":[],"character":"Y", "isARepeat":false,"processTimeMs": 669987015.0,"type":"keyUp", "unixTimeMs": 1752906764040.060791 },
{"activeModifiers":[],"character":"I", "isARepeat":false,"processTimeMs": 669987125.0,"type":"keyDown", "unixTimeMs": 1752906764147.935547 },
{"activeModifiers":[],"character":"I", "isARepeat":false,"processTimeMs": 669987187.0,"type":"keyUp", "unixTimeMs": 1752906764198.847168 },
{"activeModifiers":[],"character":"G", "isARepeat":false,"processTimeMs": 669987750.0,"type":"keyDown", "unixTimeMs": 1752906764765.415527 },
{"activeModifiers":[],"character":"G", "isARepeat":false,"processTimeMs": 669987843.0,"type":"keyUp", "unixTimeMs": 1752906764864.739014 },
{"activeModifiers":[],"character":"E", "isARepeat":false,"processTimeMs": 669987859.0,"type":"keyDown", "unixTimeMs": 1752906764873.780762 },
{"activeModifiers":[],"character":"E", "isARepeat":false,"processTimeMs": 669987921.0,"type":"keyUp", "unixTimeMs": 1752906764945.565918 },
{"activeModifiers":[],"character":"Z", "isARepeat":false,"processTimeMs": 669988156.0,"type":"keyDown", "unixTimeMs": 1752906765171.891113 },
{"activeModifiers":[],"character":"Z", "isARepeat":false,"processTimeMs": 669988265.0,"type":"keyUp", "unixTimeMs": 1752906765280.961182 },
{"activeModifiers":[],"character":"H", "isARepeat":false,"processTimeMs": 669988265.0,"type":"keyDown", "unixTimeMs": 1752906765283.471924 },
{"activeModifiers":[],"character":"H", "isARepeat":false,"processTimeMs": 669988312.0,"type":"keyUp", "unixTimeMs": 1752906765333.773682 },
{"activeModifiers":[],"character":"I", "isARepeat":false,"processTimeMs": 669988390.0,"type":"keyDown", "unixTimeMs": 1752906765411.349121 },
{"activeModifiers":[],"character":"I", "isARepeat":false,"processTimeMs": 669988484.0,"type":"keyUp", "unixTimeMs": 1752906765496.768066 },
{"activeModifiers":[],"character":"Space", "isARepeat":false,"processTimeMs": 669989125.0,"type":"keyDown", "unixTimeMs": 1752906766137.597900 },
{"activeModifiers":[],"character":"Space", "isARepeat":false,"processTimeMs": 669989203.0,"type":"keyUp", "unixTimeMs": 1752906766227.114502 },
{"activeModifiers":[],"character":"S", "isARepeat":false,"processTimeMs": 669989359.0,"type":"keyDown", "unixTimeMs": 1752906766354.025635 },
{"activeModifiers":[],"character":"S", "isARepeat":false,"processTimeMs": 669989375.0,"type":"keyUp", "unixTimeMs": 1752906766398.898682 },
{"activeModifiers":[],"character":"H", "isARepeat":false,"processTimeMs": 669989406.0,"type":"keyDown", "unixTimeMs": 1752906766431.103027 },
{"activeModifiers":[],"character":"H", "isARepeat":false,"processTimeMs": 669989500.0,"type":"keyUp", "unixTimeMs": 1752906766510.457275 },
{"activeModifiers":[],"character":"I", "isARepeat":false,"processTimeMs": 669989578.0,"type":"keyDown", "unixTimeMs": 1752906766591.003174 },
{"activeModifiers":[],"character":"I", "isARepeat":false,"processTimeMs": 669989656.0,"type":"keyUp", "unixTimeMs": 1752906766681.511719 },
{"activeModifiers":[],"character":"Space", "isARepeat":false,"processTimeMs": 669989671.0,"type":"keyDown", "unixTimeMs": 1752906766691.035889 },
{"activeModifiers":[],"character":"Space", "isARepeat":false,"processTimeMs": 669989734.0,"type":"keyUp", "unixTimeMs": 1752906766753.869873 },
{"activeModifiers":[],"character":"M", "isARepeat":false,"processTimeMs": 669992921.0,"type":"keyDown", "unixTimeMs": 1752906769945.017334 },
{"activeModifiers":[],"character":"M", "isARepeat":false,"processTimeMs": 669993015.0,"type":"keyUp", "unixTimeMs": 1752906770028.912354 },
{"activeModifiers":[],"character":"E", "isARepeat":false,"processTimeMs": 669993062.0,"type":"keyDown", "unixTimeMs": 1752906770078.932373 },
{"activeModifiers":[],"character":"I", "isARepeat":false,"processTimeMs": 669993109.0,"type":"keyDown", "unixTimeMs": 1752906770133.932373 },
{"activeModifiers":[],"character":"E", "isARepeat":false,"processTimeMs": 669993125.0,"type":"keyUp", "unixTimeMs": 1752906770149.689941 },
{"activeModifiers":[],"character":"I", "isARepeat":false,"processTimeMs": 669993203.0,"type":"keyUp", "unixTimeMs": 1752906770219.556396 },
{"activeModifiers":[],"character":"D", "isARepeat":false,"processTimeMs": 669993406.0,"type":"keyDown", "unixTimeMs": 1752906770428.467529 },
{"activeModifiers":[],"character":"D", "isARepeat":false,"processTimeMs": 669993484.0,"type":"keyUp", "unixTimeMs": 1752906770502.971924 },
{"activeModifiers":[],"character":"U", "isARepeat":false,"processTimeMs": 669993500.0,"type":"keyDown", "unixTimeMs": 1752906770511.486084 },
{"activeModifiers":[],"character":"U", "isARepeat":false,"processTimeMs": 669993578.0,"type":"keyUp", "unixTimeMs": 1752906770593.140869 },
{"activeModifiers":[],"character":"A", "isARepeat":false,"processTimeMs": 669993593.0,"type":"keyDown", "unixTimeMs": 1752906770610.272705 },
{"activeModifiers":[],"character":"N", "isARepeat":false,"processTimeMs": 669993671.0,"type":"keyDown", "unixTimeMs": 1752906770695.884521 },
{"activeModifiers":[],"character":"A", "isARepeat":false,"processTimeMs": 669993687.0,"type":"keyUp", "unixTimeMs": 1752906770710.535156 },
{"activeModifiers":[],"character":"N", "isARepeat":false,"processTimeMs": 669993765.0,"type":"keyUp", "unixTimeMs": 1752906770777.969482 },
{"activeModifiers":[],"character":"1", "isARepeat":false,"processTimeMs": 669995421.0,"type":"keyDown", "unixTimeMs": 1752906772437.470703 },
{"activeModifiers":[],"character":"1", "isARepeat":false,"processTimeMs": 669995531.0,"type":"keyUp", "unixTimeMs": 1752906772547.437988 },
{"activeModifiers":[],"character":"D", "isARepeat":false,"processTimeMs": 669995640.0,"type":"keyDown", "unixTimeMs": 1752906772661.739990 },
{"activeModifiers":[],"character":"E", "isARepeat":false,"processTimeMs": 669995734.0,"type":"keyDown", "unixTimeMs": 1752906772751.984375 },
{"activeModifiers":[],"character":"D", "isARepeat":false,"processTimeMs": 669995781.0,"type":"keyUp", "unixTimeMs": 1752906772799.609619 },
{"activeModifiers":[],"character":"Space", "isARepeat":false,"processTimeMs": 669995828.0,"type":"keyDown", "unixTimeMs": 1752906772845.202393 },
{"activeModifiers":[],"character":"E", "isARepeat":false,"processTimeMs": 669995843.0,"type":"keyUp", "unixTimeMs": 1752906772857.801514 },
{"activeModifiers":[],"character":"Space", "isARepeat":false,"processTimeMs": 669995937.0,"type":"keyUp", "unixTimeMs": 1752906772951.489746 },
{"activeModifiers":[],"character":"C", "isARepeat":false,"processTimeMs": 669995984.0,"type":"keyDown", "unixTimeMs": 1752906773005.969238 },
{"activeModifiers":[],"character":"C", "isARepeat":false,"processTimeMs": 669996078.0,"type":"keyUp", "unixTimeMs": 1752906773097.735596 },
{"activeModifiers":[],"character":"H", "isARepeat":false,"processTimeMs": 669996125.0,"type":"keyDown", "unixTimeMs": 1752906773136.255127 },
{"activeModifiers":[],"character":"A", "isARepeat":false,"processTimeMs": 669996171.0,"type":"keyDown", "unixTimeMs": 1752906773186.880127 },
{"activeModifiers":[],"character":"H", "isARepeat":false,"processTimeMs": 669996203.0,"type":"keyUp", "unixTimeMs": 1752906773215.319092 },
{"activeModifiers":[],"character":"A", "isARepeat":false,"processTimeMs": 669996265.0,"type":"keyUp", "unixTimeMs": 1752906773277.481201 },
{"activeModifiers":[],"character":"N", "isARepeat":false,"processTimeMs": 669996296.0,"type":"keyDown", "unixTimeMs": 1752906773308.236816 },
{"activeModifiers":[],"character":"G", "isARepeat":false,"processTimeMs": 669996359.0,"type":"keyDown", "unixTimeMs": 1752906773375.854004 },
{"activeModifiers":[],"character":"N", "isARepeat":false,"processTimeMs": 669996375.0,"type":"keyUp", "unixTimeMs": 1752906773397.404297 },
{"activeModifiers":[],"character":"G", "isARepeat":false,"processTimeMs": 669996421.0,"type":"keyUp", "unixTimeMs": 1752906773441.860840 },
{"activeModifiers":[],"character":"D", "isARepeat":false,"processTimeMs": 669996546.0,"type":"keyDown", "unixTimeMs": 1752906773568.229980 },
{"activeModifiers":[],"character":"D", "isARepeat":false,"processTimeMs": 669996640.0,"type":"keyUp", "unixTimeMs": 1752906773664.254883 },
{"activeModifiers":[],"character":"U", "isARepeat":false,"processTimeMs": 669996656.0,"type":"keyDown", "unixTimeMs": 1752906773674.361084 },
{"activeModifiers":[],"character":"U", "isARepeat":false,"processTimeMs": 669996718.0,"type":"keyUp", "unixTimeMs": 1752906773743.359863 },
{"activeModifiers":[],"character":"Space", "isARepeat":false,"processTimeMs": 669996765.0,"type":"keyDown", "unixTimeMs": 1752906773781.940674 },
{"activeModifiers":[],"character":"Space", "isARepeat":false,"processTimeMs": 669996875.0,"type":"keyUp", "unixTimeMs": 1752906773890.451904 },
{"activeModifiers":[],"character":",", "isARepeat":false,"processTimeMs": 669997531.0,"type":"keyDown", "unixTimeMs": 1752906774545.226807 },
{"activeModifiers":[],"character":",", "isARepeat":false,"processTimeMs": 669997578.0,"type":"keyUp", "unixTimeMs": 1752906774596.830566 },
{"activeModifiers":[],"character":"Backspace", "isARepeat":false,"processTimeMs": 669998046.0,"type":"keyDown", "unixTimeMs": 1752906775065.080811 },
{"activeModifiers":[],"character":"Backspace", "isARepeat":false,"processTimeMs": 669998125.0,"type":"keyUp", "unixTimeMs": 1752906775146.418701 },
{"activeModifiers":[],"character":".", "isARepeat":false,"processTimeMs": 669998296.0,"type":"keyDown", "unixTimeMs": 1752906775307.964600 },
{"activeModifiers":[],"character":".", "isARepeat":false,"processTimeMs": 669998343.0,"type":"keyUp", "unixTimeMs": 1752906775366.561523 },
{"activeModifiers":[],"character":"G", "isARepeat":false,"processTimeMs": 669998640.0,"type":"keyDown", "unixTimeMs": 1752906775662.418457 },
{"activeModifiers":[],"character":"G", "isARepeat":false,"processTimeMs": 669998718.0,"type":"keyUp", "unixTimeMs": 1752906775738.286865 },
{"activeModifiers":[],"character":"E", "isARepeat":false,"processTimeMs": 669998750.0,"type":"keyDown", "unixTimeMs": 1752906775775.077881 },
{"activeModifiers":[],"character":"E", "isARepeat":false,"processTimeMs": 669998812.0,"type":"keyUp", "unixTimeMs": 1752906775836.194336 },
{"activeModifiers":[],"character":"N", "isARepeat":false,"processTimeMs": 669998828.0,"type":"keyDown", "unixTimeMs": 1752906775847.755615 },
{"activeModifiers":[],"character":"N", "isARepeat":false,"processTimeMs": 669998890.0,"type":"keyUp", "unixTimeMs": 1752906775906.453613 },
{"activeModifiers":[],"character":"J", "isARepeat":false,"processTimeMs": 669999000.0,"type":"keyDown", "unixTimeMs": 1752906776021.716797 },
{"activeModifiers":[],"character":"J", "isARepeat":false,"processTimeMs": 669999046.0,"type":"keyUp", "unixTimeMs": 1752906776070.582031 },
{"activeModifiers":[],"character":"U", "isARepeat":false,"processTimeMs": 669999125.0,"type":"keyDown", "unixTimeMs": 1752906776139.570312 },
{"activeModifiers":[],"character":"U", "isARepeat":false,"processTimeMs": 669999203.0,"type":"keyUp", "unixTimeMs": 1752906776224.299805 },
{"activeModifiers":[],"character":"Space", "isARepeat":false,"processTimeMs": 669999281.0,"type":"keyDown", "unixTimeMs": 1752906776304.754883 },
{"activeModifiers":[],"character":"Space", "isARepeat":false,"processTimeMs": 669999390.0,"type":"keyUp", "unixTimeMs": 1752906776408.709473 },
{"activeModifiers":[],"character":"M", "isARepeat":false,"processTimeMs": 669999468.0,"type":"keyDown", "unixTimeMs": 1752906776481.749512 },
{"activeModifiers":[],"character":"M", "isARepeat":false,"processTimeMs": 669999546.0,"type":"keyUp", "unixTimeMs": 1752906776570.506348 },
{"activeModifiers":[],"character":"E", "isARepeat":false,"processTimeMs": 669999578.0,"type":"keyDown", "unixTimeMs": 1752906776590.441895 },
{"activeModifiers":[],"character":"E", "isARepeat":false,"processTimeMs": 669999640.0,"type":"keyUp", "unixTimeMs": 1752906776657.320312 },
{"activeModifiers":[],"character":"I", "isARepeat":false,"processTimeMs": 669999656.0,"type":"keyDown", "unixTimeMs": 1752906776674.419922 },
{"activeModifiers":[],"character":"I", "isARepeat":false,"processTimeMs": 669999734.0,"type":"keyUp", "unixTimeMs": 1752906776748.000000 },
{"activeModifiers":[],"character":"D", "isARepeat":false,"processTimeMs": 670000031.0,"type":"keyDown", "unixTimeMs": 1752906777055.450439 },
{"activeModifiers":[],"character":"D", "isARepeat":false,"processTimeMs": 670000125.0,"type":"keyUp", "unixTimeMs": 1752906777146.534912 },
{"activeModifiers":[],"character":"U", "isARepeat":false,"processTimeMs": 670000156.0,"type":"keyDown", "unixTimeMs": 1752906777170.947754 },
{"activeModifiers":[],"character":"U", "isARepeat":false,"processTimeMs": 670000234.0,"type":"keyUp", "unixTimeMs": 1752906777254.598389 },
{"activeModifiers":[],"character":"A", "isARepeat":false,"processTimeMs": 670000250.0,"type":"keyDown", "unixTimeMs": 1752906777273.325928 },
{"activeModifiers":[],"character":"A", "isARepeat":false,"processTimeMs": 670000343.0,"type":"keyUp", "unixTimeMs": 1752906777354.367920 },
{"activeModifiers":[],"character":"N", "isARepeat":false,"processTimeMs": 670000343.0,"type":"keyDown", "unixTimeMs": 1752906777361.666748 },
{"activeModifiers":[],"character":"N", "isARepeat":false,"processTimeMs": 670000437.0,"type":"keyUp", "unixTimeMs": 1752906777432.064697 },
{"activeModifiers":[],"character":"Space", "isARepeat":false,"processTimeMs": 670001031.0,"type":"keyDown", "unixTimeMs": 1752906778051.136719 },
{"activeModifiers":[],"character":"D", "isARepeat":false,"processTimeMs": 670001093.0,"type":"keyDown", "unixTimeMs": 1752906778107.630859 },
{"activeModifiers":[],"character":"Space", "isARepeat":false,"processTimeMs": 670001156.0,"type":"keyUp", "unixTimeMs": 1752906778176.452148 },
{"activeModifiers":[],"character":"E", "isARepeat":false,"processTimeMs": 670001171.0,"type":"keyDown", "unixTimeMs": 1752906778184.618896 },
{"activeModifiers":[],"character":"D", "isARepeat":false,"processTimeMs": 670001234.0,"type":"keyUp", "unixTimeMs": 1752906778244.690918 },
{"activeModifiers":[],"character":"Space", "isARepeat":false,"processTimeMs": 670001265.0,"type":"keyDown", "unixTimeMs": 1752906778290.699219 },
{"activeModifiers":[],"character":"E", "isARepeat":false,"processTimeMs": 670001296.0,"type":"keyUp", "unixTimeMs": 1752906778322.324707 },
{"activeModifiers":[],"character":"Space", "isARepeat":false,"processTimeMs": 670001359.0,"type":"keyUp", "unixTimeMs": 1752906778371.829102 },
{"activeModifiers":[],"character":"C", "isARepeat":false,"processTimeMs": 670001359.0,"type":"keyDown", "unixTimeMs": 1752906778379.104004 },
{"activeModifiers":[],"character":"C", "isARepeat":false,"processTimeMs": 670001468.0,"type":"keyUp", "unixTimeMs": 1752906778483.510742 },
{"activeModifiers":[],"character":"H", "isARepeat":false,"processTimeMs": 670001500.0,"type":"keyDown", "unixTimeMs": 1752906778515.460449 },
{"activeModifiers":[],"character":"A", "isARepeat":false,"processTimeMs": 670001562.0,"type":"keyDown", "unixTimeMs": 1752906778582.124512 },
{"activeModifiers":[],"character":"H", "isARepeat":false,"processTimeMs": 670001578.0,"type":"keyUp", "unixTimeMs": 1752906778600.191162 },
{"activeModifiers":[],"character":"A", "isARepeat":false,"processTimeMs": 670001671.0,"type":"keyUp", "unixTimeMs": 1752906778687.119873 },
{"activeModifiers":[],"character":"N", "isARepeat":false,"processTimeMs": 670001671.0,"type":"keyDown", "unixTimeMs": 1752906778693.654297 },
{"activeModifiers":[],"character":"G", "isARepeat":false,"processTimeMs": 670001750.0,"type":"keyDown", "unixTimeMs": 1752906778775.367676 },
{"activeModifiers":[],"character":"N", "isARepeat":false,"processTimeMs": 670001781.0,"type":"keyUp", "unixTimeMs": 1752906778795.433594 },
{"activeModifiers":[],"character":"G", "isARepeat":false,"processTimeMs": 670001828.0,"type":"keyUp", "unixTimeMs": 1752906778852.163574 },
{"activeModifiers":[],"character":"D", "isARepeat":false,"processTimeMs": 670001937.0,"type":"keyDown", "unixTimeMs": 1752906778959.867188 },
{"activeModifiers":[],"character":"U", "isARepeat":false,"processTimeMs": 670002015.0,"type":"keyDown", "unixTimeMs": 1752906779039.921631 },
{"activeModifiers":[],"character":"D", "isARepeat":false,"processTimeMs": 670002046.0,"type":"keyUp", "unixTimeMs": 1752906779059.227539 },
{"activeModifiers":[],"character":"U", "isARepeat":false,"processTimeMs": 670002093.0,"type":"keyUp", "unixTimeMs": 1752906779106.888916 },
{"activeModifiers":[],"character":"Space", "isARepeat":false,"processTimeMs": 670002125.0,"type":"keyDown", "unixTimeMs": 1752906779143.655518 },
{"activeModifiers":[],"character":"Space", "isARepeat":false,"processTimeMs": 670002218.0,"type":"keyUp", "unixTimeMs": 1752906779238.118408 },
{"activeModifiers":[],"character":",", "isARepeat":false,"processTimeMs": 670006953.0,"type":"keyDown", "unixTimeMs": 1752906783975.584717 },
{"activeModifiers":[],"character":",", "isARepeat":false,"processTimeMs": 670007031.0,"type":"keyUp", "unixTimeMs": 1752906784055.820312 },
{"activeModifiers":[],"character":"S", "isARepeat":false,"processTimeMs": 670007250.0,"type":"keyDown", "unixTimeMs": 1752906784272.622803 },
{"activeModifiers":[],"character":"S", "isARepeat":false,"processTimeMs": 670007328.0,"type":"keyUp", "unixTimeMs": 1752906784353.679688 },
{"activeModifiers":[],"character":"H", "isARepeat":false,"processTimeMs": 670007343.0,"type":"keyDown", "unixTimeMs": 1752906784364.986084 },
{"activeModifiers":[],"character":"H", "isARepeat":false,"processTimeMs": 670007390.0,"type":"keyUp", "unixTimeMs": 1752906784410.169678 },
{"activeModifiers":[],"character":"E", "isARepeat":false,"processTimeMs": 670007421.0,"type":"keyDown", "unixTimeMs": 1752906784445.037598 },
{"activeModifiers":[],"character":"E", "isARepeat":false,"processTimeMs": 670007500.0,"type":"keyUp", "unixTimeMs": 1752906784521.444824 },
{"activeModifiers":[],"character":"N", "isARepeat":false,"processTimeMs": 670007515.0,"type":"keyDown", "unixTimeMs": 1752906784534.980469 },
{"activeModifiers":[],"character":"G", "isARepeat":false,"processTimeMs": 670007562.0,"type":"keyDown", "unixTimeMs": 1752906784579.388672 },
{"activeModifiers":[],"character":"N", "isARepeat":false,"processTimeMs": 670007578.0,"type":"keyUp", "unixTimeMs": 1752906784596.712402 },
{"activeModifiers":[],"character":"G", "isARepeat":false,"processTimeMs": 670007640.0,"type":"keyUp", "unixTimeMs": 1752906784654.146729 },
{"activeModifiers":[],"character":"C", "isARepeat":false,"processTimeMs": 670007765.0,"type":"keyDown", "unixTimeMs": 1752906784785.677979 },
{"activeModifiers":[],"character":"C", "isARepeat":false,"processTimeMs": 670007859.0,"type":"keyUp", "unixTimeMs": 1752906784878.413086 },
{"activeModifiers":[],"character":"H", "isARepeat":false,"processTimeMs": 670007875.0,"type":"keyDown", "unixTimeMs": 1752906784888.295166 },
{"activeModifiers":[],"character":"E", "isARepeat":false,"processTimeMs": 670007937.0,"type":"keyDown", "unixTimeMs": 1752906784957.933105 },
{"activeModifiers":[],"character":"H", "isARepeat":false,"processTimeMs": 670007953.0,"type":"keyUp", "unixTimeMs": 1752906784971.848389 },
{"activeModifiers":[],"character":"E", "isARepeat":false,"processTimeMs": 670008046.0,"type":"keyUp", "unixTimeMs": 1752906785063.582520 },
{"activeModifiers":[],"character":"N", "isARepeat":false,"processTimeMs": 670008046.0,"type":"keyDown", "unixTimeMs": 1752906785072.107910 },
{"activeModifiers":[],"character":"G", "isARepeat":false,"processTimeMs": 670008125.0,"type":"keyDown", "unixTimeMs": 1752906785148.589111 },
{"activeModifiers":[],"character":"N", "isARepeat":false,"processTimeMs": 670008156.0,"type":"keyUp", "unixTimeMs": 1752906785176.722900 },
{"activeModifiers":[],"character":"G", "isARepeat":false,"processTimeMs": 670008234.0,"type":"keyUp", "unixTimeMs": 1752906785254.343262 },
{"activeModifiers":[],"character":"Space", "isARepeat":false,"processTimeMs": 670008265.0,"type":"keyDown", "unixTimeMs": 1752906785277.415527 },
{"activeModifiers":[],"character":"Space", "isARepeat":false,"processTimeMs": 670008375.0,"type":"keyUp", "unixTimeMs": 1752906785386.725098 },
{"activeModifiers":[],"character":"Backspace", "isARepeat":false,"processTimeMs": 670009109.0,"type":"keyDown", "unixTimeMs": 1752906786126.856445 },
{"activeModifiers":[],"character":"Backspace", "isARepeat":false,"processTimeMs": 670009218.0,"type":"keyUp", "unixTimeMs": 1752906786231.505127 },
{"activeModifiers":[],"character":"Backspace", "isARepeat":false,"processTimeMs": 670009312.0,"type":"keyDown", "unixTimeMs": 1752906786325.142822 },
{"activeModifiers":[],"character":"Backspace", "isARepeat":false,"processTimeMs": 670009390.0,"type":"keyUp", "unixTimeMs": 1752906786411.281982 },
{"activeModifiers":[],"character":"Backspace", "isARepeat":false,"processTimeMs": 670009515.0,"type":"keyDown", "unixTimeMs": 1752906786529.489014 },
{"activeModifiers":[],"character":"Backspace", "isARepeat":false,"processTimeMs": 670009609.0,"type":"keyUp", "unixTimeMs": 1752906786620.719482 },
{"activeModifiers":[],"character":"Backspace", "isARepeat":false,"processTimeMs": 670011093.0,"type":"keyDown", "unixTimeMs": 1752906788111.481689 },
{"activeModifiers":[],"character":"Backspace", "isARepeat":false,"processTimeMs": 670011171.0,"type":"keyUp", "unixTimeMs": 1752906788182.687988 },
{"activeModifiers":[],"character":"Backspace", "isARepeat":false,"processTimeMs": 670011265.0,"type":"keyDown", "unixTimeMs": 1752906788286.875000 },
{"activeModifiers":[],"character":"Backspace", "isARepeat":false,"processTimeMs": 670011328.0,"type":"keyUp", "unixTimeMs": 1752906788348.935303 },
{"activeModifiers":[],"character":"Backspace", "isARepeat":false,"processTimeMs": 670011437.0,"type":"keyDown", "unixTimeMs": 1752906788451.719482 },
{"activeModifiers":[],"character":"Backspace", "isARepeat":false,"processTimeMs": 670011500.0,"type":"keyUp", "unixTimeMs": 1752906788523.875488 },
{"activeModifiers":[],"character":"Backspace", "isARepeat":false,"processTimeMs": 670011609.0,"type":"keyDown", "unixTimeMs": 1752906788621.581543 },
{"activeModifiers":[],"character":"Backspace", "isARepeat":false,"processTimeMs": 670011703.0,"type":"keyUp", "unixTimeMs": 1752906788720.511719 },
{"activeModifiers":[],"character":"Backspace", "isARepeat":false,"processTimeMs": 670011984.0,"type":"keyDown", "unixTimeMs": 1752906788997.238281 },
{"activeModifiers":[],"character":"Backspace", "isARepeat":false,"processTimeMs": 670012109.0,"type":"keyUp", "unixTimeMs": 1752906789127.095459 },
{"activeModifiers":[],"character":"M", "isARepeat":false,"processTimeMs": 670012671.0,"type":"keyDown", "unixTimeMs": 1752906789691.621094 },
{"activeModifiers":[],"character":"M", "isARepeat":false,"processTimeMs": 670012765.0,"type":"keyUp", "unixTimeMs": 1752906789776.210449 },
{"activeModifiers":[],"character":"E", "isARepeat":false,"processTimeMs": 670012781.0,"type":"keyDown", "unixTimeMs": 1752906789802.563721 },
{"activeModifiers":[],"character":"E", "isARepeat":false,"processTimeMs": 670012843.0,"type":"keyUp", "unixTimeMs": 1752906789862.351562 },
{"activeModifiers":[],"character":"I", "isARepeat":false,"processTimeMs": 670012875.0,"type":"keyDown", "unixTimeMs": 1752906789896.259521 },
{"activeModifiers":[],"character":"I", "isARepeat":false,"processTimeMs": 670012937.0,"type":"keyUp", "unixTimeMs": 1752906789961.684082 },
{"activeModifiers":[],"character":"D", "isARepeat":false,"processTimeMs": 670013046.0,"type":"keyDown", "unixTimeMs": 1752906790058.796875 },
{"activeModifiers":[],"character":"D", "isARepeat":false,"processTimeMs": 670013125.0,"type":"keyUp", "unixTimeMs": 1752906790148.260010 },
{"activeModifiers":[],"character":"U", "isARepeat":false,"processTimeMs": 670013140.0,"type":"keyDown", "unixTimeMs": 1752906790154.266846 },
{"activeModifiers":[],"character":"U", "isARepeat":false,"processTimeMs": 670013203.0,"type":"keyUp", "unixTimeMs": 1752906790214.892578 },
{"activeModifiers":[],"character":"A", "isARepeat":false,"processTimeMs": 670013218.0,"type":"keyDown", "unixTimeMs": 1752906790240.286865 },
{"activeModifiers":[],"character":"N", "isARepeat":false,"processTimeMs": 670013296.0,"type":"keyDown", "unixTimeMs": 1752906790312.484375 },
{"activeModifiers":[],"character":"A", "isARepeat":false,"processTimeMs": 670013312.0,"type":"keyUp", "unixTimeMs": 1752906790326.892090 },
{"activeModifiers":[],"character":"N", "isARepeat":false,"processTimeMs": 670013375.0,"type":"keyUp", "unixTimeMs": 1752906790394.835205 },
{"activeModifiers":[],"character":"Space", "isARepeat":false,"processTimeMs": 670013687.0,"type":"keyDown", "unixTimeMs": 1752906790710.556396 },
{"activeModifiers":[],"character":"Space", "isARepeat":false,"processTimeMs": 670013812.0,"type":"keyUp", "unixTimeMs": 1752906790830.328613 },
{"activeModifiers":[],"character":"Backspace", "isARepeat":false,"processTimeMs": 670014468.0,"type":"keyDown", "unixTimeMs": 1752906791483.100342 },
{"activeModifiers":[],"character":"Backspace", "isARepeat":false,"processTimeMs": 670014515.0,"type":"keyUp", "unixTimeMs": 1752906791535.062256 },
{"activeModifiers":[],"character":"Backspace", "isARepeat":false,"processTimeMs": 670014656.0,"type":"keyDown", "unixTimeMs": 1752906791670.192383 },
{"activeModifiers":[],"character":"Backspace", "isARepeat":false,"processTimeMs": 670014703.0,"type":"keyUp", "unixTimeMs": 1752906791728.034424 },
{"activeModifiers":[],"character":"Z", "isARepeat":false,"processTimeMs": 670015765.0,"type":"keyDown", "unixTimeMs": 1752906792787.224854 },
{"activeModifiers":[],"character":"Z", "isARepeat":false,"processTimeMs": 670015875.0,"type":"keyUp", "unixTimeMs": 1752906792897.453369 },
{"activeModifiers":[],"character":"O", "isARepeat":false,"processTimeMs": 670016343.0,"type":"keyDown", "unixTimeMs": 1752906793359.952393 },
{"activeModifiers":[],"character":"O", "isARepeat":false,"processTimeMs": 670016421.0,"type":"keyUp", "unixTimeMs": 1752906793441.076660 },
{"activeModifiers":[],"character":"N", "isARepeat":false,"processTimeMs": 670016531.0,"type":"keyDown", "unixTimeMs": 1752906793555.318359 },
{"activeModifiers":[],"character":"N", "isARepeat":false,"processTimeMs": 670016609.0,"type":"keyUp", "unixTimeMs": 1752906793629.379639 },
{"activeModifiers":[],"character":"G", "isARepeat":false,"processTimeMs": 670016640.0,"type":"keyDown", "unixTimeMs": 1752906793657.966064 },
{"activeModifiers":[],"character":"G", "isARepeat":false,"processTimeMs": 670016703.0,"type":"keyUp", "unixTimeMs": 1752906793720.073486 },
{"activeModifiers":[],"character":"C", "isARepeat":false,"processTimeMs": 670016828.0,"type":"keyDown", "unixTimeMs": 1752906793847.239990 },
{"activeModifiers":[],"character":"C", "isARepeat":false,"processTimeMs": 670016890.0,"type":"keyUp", "unixTimeMs": 1752906793915.612305 },
{"activeModifiers":[],"character":"H", "isARepeat":false,"processTimeMs": 670016906.0,"type":"keyDown", "unixTimeMs": 1752906793926.155273 },
{"activeModifiers":[],"character":"H", "isARepeat":false,"processTimeMs": 670016984.0,"type":"keyUp", "unixTimeMs": 1752906793997.300293 },
{"activeModifiers":[],"character":"A", "isARepeat":false,"processTimeMs": 670017015.0,"type":"keyDown", "unixTimeMs": 1752906794029.618896 },
{"activeModifiers":[],"character":"A", "isARepeat":false,"processTimeMs": 670017078.0,"type":"keyUp", "unixTimeMs": 1752906794100.079102 },
{"activeModifiers":[],"character":"N", "isARepeat":false,"processTimeMs": 670017093.0,"type":"keyDown", "unixTimeMs": 1752906794109.117920 },
{"activeModifiers":[],"character":"N", "isARepeat":false,"processTimeMs": 670017156.0,"type":"keyUp", "unixTimeMs": 1752906794168.961670 },
{"activeModifiers":[],"character":"G", "isARepeat":false,"processTimeMs": 670017203.0,"type":"keyDown", "unixTimeMs": 1752906794217.123535 },
{"activeModifiers":[],"character":"G", "isARepeat":false,"processTimeMs": 670017265.0,"type":"keyUp", "unixTimeMs": 1752906794278.968018 },
{"activeModifiers":[],"character":"D", "isARepeat":false,"processTimeMs": 670017375.0,"type":"keyDown", "unixTimeMs": 1752906794391.765625 },
{"activeModifiers":[],"character":"D", "isARepeat":false,"processTimeMs": 670017453.0,"type":"keyUp", "unixTimeMs": 1752906794474.381592 },
{"activeModifiers":[],"character":"U", "isARepeat":false,"processTimeMs": 670017484.0,"type":"keyDown", "unixTimeMs": 1752906794495.465576 },
{"activeModifiers":[],"character":"U", "isARepeat":false,"processTimeMs": 670017562.0,"type":"keyUp", "unixTimeMs": 1752906794573.034424 },
{"activeModifiers":[],"character":"Space", "isARepeat":false,"processTimeMs": 670018015.0,"type":"keyDown", "unixTimeMs": 1752906795038.476074 },
{"activeModifiers":[],"character":"Space", "isARepeat":false,"processTimeMs": 670018109.0,"type":"keyUp", "unixTimeMs": 1752906795133.268555 },
{"activeModifiers":[],"character":"Z", "isARepeat":false,"processTimeMs": 670018578.0,"type":"keyDown", "unixTimeMs": 1752906795592.874512 },
{"activeModifiers":[],"character":"H", "isARepeat":false,"processTimeMs": 670018656.0,"type":"keyDown", "unixTimeMs": 1752906795674.688721 },
{"activeModifiers":[],"character":"Z", "isARepeat":false,"processTimeMs": 670018671.0,"type":"keyUp", "unixTimeMs": 1752906795688.638916 },
{"activeModifiers":[],"character":"H", "isARepeat":false,"processTimeMs": 670018734.0,"type":"keyUp", "unixTimeMs": 1752906795745.580322 },
{"activeModifiers":[],"character":"O", "isARepeat":false,"processTimeMs": 670018828.0,"type":"keyDown", "unixTimeMs": 1752906795852.914307 },
{"activeModifiers":[],"character":"O", "isARepeat":false,"processTimeMs": 670018890.0,"type":"keyUp", "unixTimeMs": 1752906795905.970215 },
{"activeModifiers":[],"character":"N", "isARepeat":false,"processTimeMs": 670018984.0,"type":"keyDown", "unixTimeMs": 1752906796002.591309 },
{"activeModifiers":[],"character":"G", "isARepeat":false,"processTimeMs": 670019031.0,"type":"keyDown", "unixTimeMs": 1752906796054.626953 },
{"activeModifiers":[],"character":"N", "isARepeat":false,"processTimeMs": 670019078.0,"type":"keyUp", "unixTimeMs": 1752906796088.574707 },
{"activeModifiers":[],"character":"Space", "isARepeat":false,"processTimeMs": 670019109.0,"type":"keyDown", "unixTimeMs": 1752906796123.034180 },
{"activeModifiers":[],"character":"G", "isARepeat":false,"processTimeMs": 670019140.0,"type":"keyUp", "unixTimeMs": 1752906796159.021973 },
{"activeModifiers":[],"character":"Space", "isARepeat":false,"processTimeMs": 670019187.0,"type":"keyUp", "unixTimeMs": 1752906796202.999512 },
{"activeModifiers":[],"character":"N", "isARepeat":false,"processTimeMs": 670019281.0,"type":"keyDown", "unixTimeMs": 1752906796297.695312 },
{"activeModifiers":[],"character":"E", "isARepeat":false,"processTimeMs": 670019328.0,"type":"keyDown", "unixTimeMs": 1752906796344.318848 },
{"activeModifiers":[],"character":"N", "isARepeat":false,"processTimeMs": 670019343.0,"type":"keyUp", "unixTimeMs": 1752906796359.143555 },
{"activeModifiers":[],"character":"E", "isARepeat":false,"processTimeMs": 670019406.0,"type":"keyUp", "unixTimeMs": 1752906796426.285889 },
{"activeModifiers":[],"character":"N", "isARepeat":false,"processTimeMs": 670019437.0,"type":"keyDown", "unixTimeMs": 1752906796455.723145 },
{"activeModifiers":[],"character":"G", "isARepeat":false,"processTimeMs": 670019484.0,"type":"keyDown", "unixTimeMs": 1752906796508.969971 },
{"activeModifiers":[],"character":"N", "isARepeat":false,"processTimeMs": 670019515.0,"type":"keyUp", "unixTimeMs": 1752906796532.920410 },
{"activeModifiers":[],"character":"G", "isARepeat":false,"processTimeMs": 670019562.0,"type":"keyUp", "unixTimeMs": 1752906796578.347168 },
{"activeModifiers":[],"character":"Space", "isARepeat":false,"processTimeMs": 670019687.0,"type":"keyDown", "unixTimeMs": 1752906796703.046143 },
{"activeModifiers":[],"character":"Space", "isARepeat":false,"processTimeMs": 670019796.0,"type":"keyUp", "unixTimeMs": 1752906796813.290039 },
{"activeModifiers":[],"character":"H", "isARepeat":false,"processTimeMs": 670020109.0,"type":"keyDown", "unixTimeMs": 1752906797132.657959 },
{"activeModifiers":[],"character":"H", "isARepeat":false,"processTimeMs": 670020171.0,"type":"keyUp", "unixTimeMs": 1752906797192.339600 },
{"activeModifiers":[],"character":"U", "isARepeat":false,"processTimeMs": 670020234.0,"type":"keyDown", "unixTimeMs": 1752906797252.927490 },
{"activeModifiers":[],"character":"A", "isARepeat":false,"processTimeMs": 670020296.0,"type":"keyDown", "unixTimeMs": 1752906797309.254395 },
{"activeModifiers":[],"character":"U", "isARepeat":false,"processTimeMs": 670020312.0,"type":"keyUp", "unixTimeMs": 1752906797335.145508 },
{"activeModifiers":[],"character":"A", "isARepeat":false,"processTimeMs": 670020359.0,"type":"keyUp", "unixTimeMs": 1752906797380.184082 },
{"activeModifiers":[],"character":"F", "isARepeat":false,"processTimeMs": 670020687.0,"type":"keyDown", "unixTimeMs": 1752906797703.209229 },
{"activeModifiers":[],"character":"E", "isARepeat":false,"processTimeMs": 670020796.0,"type":"keyDown", "unixTimeMs": 1752906797808.147705 },
{"activeModifiers":[],"character":"F", "isARepeat":false,"processTimeMs": 670020828.0,"type":"keyUp", "unixTimeMs": 1752906797845.750000 },
{"activeModifiers":[],"character":"E", "isARepeat":false,"processTimeMs": 670020875.0,"type":"keyUp", "unixTimeMs": 1752906797896.741699 },
{"activeModifiers":[],"character":"N", "isARepeat":false,"processTimeMs": 670020968.0,"type":"keyDown", "unixTimeMs": 1752906797981.177246 },
{"activeModifiers":[],"character":"N", "isARepeat":false,"processTimeMs": 670021031.0,"type":"keyUp", "unixTimeMs": 1752906798052.986816 },
{"activeModifiers":[],"character":"Space", "isARepeat":false,"processTimeMs": 670021109.0,"type":"keyDown", "unixTimeMs": 1752906798121.961182 },
{"activeModifiers":[],"character":"Space", "isARepeat":false,"processTimeMs": 670021203.0,"type":"keyUp", "unixTimeMs": 1752906798220.204346 },
{"activeModifiers":[],"character":"C", "isARepeat":false,"processTimeMs": 670021281.0,"type":"keyDown", "unixTimeMs": 1752906798300.180420 },
{"activeModifiers":[],"character":"C", "isARepeat":false,"processTimeMs": 670021359.0,"type":"keyUp", "unixTimeMs": 1752906798384.672363 },
{"activeModifiers":[],"character":"H", "isARepeat":false,"processTimeMs": 670021609.0,"type":"keyDown", "unixTimeMs": 1752906798626.843262 },
{"activeModifiers":[],"character":"H", "isARepeat":false,"processTimeMs": 670021671.0,"type":"keyUp", "unixTimeMs": 1752906798696.455078 },
{"activeModifiers":[],"character":"U", "isARepeat":false,"processTimeMs": 670021750.0,"type":"keyDown", "unixTimeMs": 1752906798771.946777 },
{"activeModifiers":[],"character":"U", "isARepeat":false,"processTimeMs": 670021828.0,"type":"keyUp", "unixTimeMs": 1752906798847.775146 },
{"activeModifiers":[],"character":"Space", "isARepeat":false,"processTimeMs": 670021875.0,"type":"keyDown", "unixTimeMs": 1752906798893.360840 },
{"activeModifiers":[],"character":"Space", "isARepeat":false,"processTimeMs": 670021968.0,"type":"keyUp", "unixTimeMs": 1752906798982.468506 },
{"activeModifiers":[],"character":"D", "isARepeat":false,"processTimeMs": 670022000.0,"type":"keyDown", "unixTimeMs": 1752906799014.882568 },
{"activeModifiers":[],"character":"U", "isARepeat":false,"processTimeMs": 670022078.0,"type":"keyDown", "unixTimeMs": 1752906799101.988037 },
{"activeModifiers":[],"character":"D", "isARepeat":false,"processTimeMs": 670022093.0,"type":"keyUp", "unixTimeMs": 1752906799117.791260 },
{"activeModifiers":[],"character":"U", "isARepeat":false,"processTimeMs": 670022156.0,"type":"keyUp", "unixTimeMs": 1752906799181.628906 },
{"activeModifiers":[],"character":"O", "isARepeat":false,"processTimeMs": 670022265.0,"type":"keyDown", "unixTimeMs": 1752906799277.741699 },
{"activeModifiers":[],"character":"O", "isARepeat":false,"processTimeMs": 670022328.0,"type":"keyUp", "unixTimeMs": 1752906799353.342041 },
{"activeModifiers":[],"character":"S", "isARepeat":false,"processTimeMs": 670022343.0,"type":"keyDown", "unixTimeMs": 1752906799363.372803 },
{"activeModifiers":[],"character":"S", "isARepeat":false,"processTimeMs": 670022421.0,"type":"keyUp", "unixTimeMs": 1752906799439.433105 },
{"activeModifiers":[],"character":"H", "isARepeat":false,"processTimeMs": 670022453.0,"type":"keyDown", "unixTimeMs": 1752906799470.413574 },
{"activeModifiers":[],"character":"H", "isARepeat":false,"processTimeMs": 670022531.0,"type":"keyUp", "unixTimeMs": 1752906799550.401611 },
{"activeModifiers":[],"character":"A", "isARepeat":false,"processTimeMs": 670022546.0,"type":"keyDown", "unixTimeMs": 1752906799572.210693 },
{"activeModifiers":[],"character":"O", "isARepeat":false,"processTimeMs": 670022640.0,"type":"keyDown", "unixTimeMs": 1752906799653.699707 },
{"activeModifiers":[],"character":"A", "isARepeat":false,"processTimeMs": 670022640.0,"type":"keyUp", "unixTimeMs": 1752906799664.587646 },
{"activeModifiers":[],"character":"O", "isARepeat":false,"processTimeMs": 670022687.0,"type":"keyUp", "unixTimeMs": 1752906799708.201904 },
{"activeModifiers":[],"character":"G", "isARepeat":false,"processTimeMs": 670023046.0,"type":"keyDown", "unixTimeMs": 1752906800069.214111 },
{"activeModifiers":[],"character":"E", "isARepeat":false,"processTimeMs": 670023125.0,"type":"keyDown", "unixTimeMs": 1752906800139.869141 },
{"activeModifiers":[],"character":"G", "isARepeat":false,"processTimeMs": 670023140.0,"type":"keyUp", "unixTimeMs": 1752906800154.897705 },
{"activeModifiers":[],"character":"E", "isARepeat":false,"processTimeMs": 670023203.0,"type":"keyUp", "unixTimeMs": 1752906800222.764893 },
{"activeModifiers":[],"character":"Space", "isARepeat":false,"processTimeMs": 670023343.0,"type":"keyDown", "unixTimeMs": 1752906800368.209717 },
{"activeModifiers":[],"character":"Space", "isARepeat":false,"processTimeMs": 670023453.0,"type":"keyUp", "unixTimeMs": 1752906800470.481689 },
{"activeModifiers":[],"character":"M", "isARepeat":false,"processTimeMs": 670023765.0,"type":"keyDown", "unixTimeMs": 1752906800788.467285 },
{"activeModifiers":[],"character":"M", "isARepeat":false,"processTimeMs": 670023859.0,"type":"keyUp", "unixTimeMs": 1752906800873.527100 },
{"activeModifiers":[],"character":"E", "isARepeat":false,"processTimeMs": 670023890.0,"type":"keyDown", "unixTimeMs": 1752906800910.039307 },
{"activeModifiers":[],"character":"E", "isARepeat":false,"processTimeMs": 670023968.0,"type":"keyUp", "unixTimeMs": 1752906800983.870117 },
{"activeModifiers":[],"character":"I", "isARepeat":false,"processTimeMs": 670024000.0,"type":"keyDown", "unixTimeMs": 1752906801024.652832 },
{"activeModifiers":[],"character":"I", "isARepeat":false,"processTimeMs": 670024078.0,"type":"keyUp", "unixTimeMs": 1752906801094.832520 },
{"activeModifiers":[],"character":"D", "isARepeat":false,"processTimeMs": 670024203.0,"type":"keyDown", "unixTimeMs": 1752906801225.013916 },
{"activeModifiers":[],"character":"D", "isARepeat":false,"processTimeMs": 670024296.0,"type":"keyUp", "unixTimeMs": 1752906801315.019043 },
{"activeModifiers":[],"character":"U", "isARepeat":false,"processTimeMs": 670024312.0,"type":"keyDown", "unixTimeMs": 1752906801324.756836 },
{"activeModifiers":[],"character":"U", "isARepeat":false,"processTimeMs": 670024390.0,"type":"keyUp", "unixTimeMs": 1752906801403.927490 },
{"activeModifiers":[],"character":"A", "isARepeat":false,"processTimeMs": 670024390.0,"type":"keyDown", "unixTimeMs": 1752906801413.440430 },
{"activeModifiers":[],"character":"A", "isARepeat":false,"processTimeMs": 670024484.0,"type":"keyUp", "unixTimeMs": 1752906801503.250488 },
{"activeModifiers":[],"character":"N", "isARepeat":false,"processTimeMs": 670024500.0,"type":"keyDown", "unixTimeMs": 1752906801513.223145 },
{"activeModifiers":[],"character":"N", "isARepeat":false,"processTimeMs": 670024562.0,"type":"keyUp", "unixTimeMs": 1752906801577.027588 },
{"activeModifiers":[],"character":"Space", "isARepeat":false,"processTimeMs": 670024906.0,"type":"keyDown", "unixTimeMs": 1752906801926.335938 },
{"activeModifiers":[],"character":"D", "isARepeat":false,"processTimeMs": 670024953.0,"type":"keyDown", "unixTimeMs": 1752906801972.039551 },
{"activeModifiers":[],"character":"Space", "isARepeat":false,"processTimeMs": 670025046.0,"type":"keyUp", "unixTimeMs": 1752906802060.406494 },
{"activeModifiers":[],"character":"E", "isARepeat":false,"processTimeMs": 670025046.0,"type":"keyDown", "unixTimeMs": 1752906802069.300537 },
{"activeModifiers":[],"character":"D", "isARepeat":false,"processTimeMs": 670025125.0,"type":"keyUp", "unixTimeMs": 1752906802139.065918 },
{"activeModifiers":[],"character":"Space", "isARepeat":false,"processTimeMs": 670025156.0,"type":"keyDown", "unixTimeMs": 1752906802175.872314 },
{"activeModifiers":[],"character":"E", "isARepeat":false,"processTimeMs": 670025187.0,"type":"keyUp", "unixTimeMs": 1752906802211.163818 },
{"activeModifiers":[],"character":"Space", "isARepeat":false,"processTimeMs": 670025250.0,"type":"keyUp", "unixTimeMs": 1752906802272.224365 },
{"activeModifiers":[],"character":"C", "isARepeat":false,"processTimeMs": 670025281.0,"type":"keyDown", "unixTimeMs": 1752906802300.342041 },
{"activeModifiers":[],"character":"C", "isARepeat":false,"processTimeMs": 670025375.0,"type":"keyUp", "unixTimeMs": 1752906802397.800049 },
{"activeModifiers":[],"character":"H", "isARepeat":false,"processTimeMs": 670025406.0,"type":"keyDown", "unixTimeMs": 1752906802418.961426 },
{"activeModifiers":[],"character":"A", "isARepeat":false,"processTimeMs": 670025437.0,"type":"keyDown", "unixTimeMs": 1752906802460.410889 },
{"activeModifiers":[],"character":"H", "isARepeat":false,"processTimeMs": 670025484.0,"type":"keyUp", "unixTimeMs": 1752906802479.022705 },
{"activeModifiers":[],"character":"A", "isARepeat":false,"processTimeMs": 670025531.0,"type":"keyUp", "unixTimeMs": 1752906802541.918701 },
{"activeModifiers":[],"character":"N", "isARepeat":false,"processTimeMs": 670025578.0,"type":"keyDown", "unixTimeMs": 1752906802591.489990 },
{"activeModifiers":[],"character":"G", "isARepeat":false,"processTimeMs": 670025640.0,"type":"keyDown", "unixTimeMs": 1752906802653.840332 },
{"activeModifiers":[],"character":"N", "isARepeat":false,"processTimeMs": 670025687.0,"type":"keyUp", "unixTimeMs": 1752906802700.370850 },
{"activeModifiers":[],"character":"G", "isARepeat":false,"processTimeMs": 670025703.0,"type":"keyUp", "unixTimeMs": 1752906802723.164062 },
{"activeModifiers":[],"character":"D", "isARepeat":false,"processTimeMs": 670025812.0,"type":"keyDown", "unixTimeMs": 1752906802836.939453 },
{"activeModifiers":[],"character":"D", "isARepeat":false,"processTimeMs": 670025921.0,"type":"keyUp", "unixTimeMs": 1752906802942.036133 },
{"activeModifiers":[],"character":"U", "isARepeat":false,"processTimeMs": 670025937.0,"type":"keyDown", "unixTimeMs": 1752906802950.694336 },
{"activeModifiers":[],"character":"U", "isARepeat":false,"processTimeMs": 670026000.0,"type":"keyUp", "unixTimeMs": 1752906803011.447998 },
{"activeModifiers":[],"character":"Space", "isARepeat":false,"processTimeMs": 670026031.0,"type":"keyDown", "unixTimeMs": 1752906803045.860107 },
{"activeModifiers":[],"character":"Space", "isARepeat":false,"processTimeMs": 670026125.0,"type":"keyUp", "unixTimeMs": 1752906803138.860107 },
{"activeModifiers":[],"character":",", "isARepeat":false,"processTimeMs": 670026859.0,"type":"keyDown", "unixTimeMs": 1752906803875.735107 },
{"activeModifiers":[],"character":",", "isARepeat":false,"processTimeMs": 670026921.0,"type":"keyUp", "unixTimeMs": 1752906803941.491943 },
{"activeModifiers":[],"character":"S", "isARepeat":false,"processTimeMs": 670027046.0,"type":"keyDown", "unixTimeMs": 1752906804058.867920 },
{"activeModifiers":[],"character":"H", "isARepeat":false,"processTimeMs": 670027109.0,"type":"keyDown", "unixTimeMs": 1752906804122.055664 },
{"activeModifiers":[],"character":"S", "isARepeat":false,"processTimeMs": 670027125.0,"type":"keyUp", "unixTimeMs": 1752906804136.122803 },
{"activeModifiers":[],"character":"H", "isARepeat":false,"processTimeMs": 670027171.0,"type":"keyUp", "unixTimeMs": 1752906804193.565918 },
{"activeModifiers":[],"character":"E", "isARepeat":false,"processTimeMs": 670027218.0,"type":"keyDown", "unixTimeMs": 1752906804242.988037 },
{"activeModifiers":[],"character":"E", "isARepeat":false,"processTimeMs": 670027281.0,"type":"keyUp", "unixTimeMs": 1752906804291.756836 },
{"activeModifiers":[],"character":"N", "isARepeat":false,"processTimeMs": 670027281.0,"type":"keyDown", "unixTimeMs": 1752906804300.177002 },
{"activeModifiers":[],"character":"G", "isARepeat":false,"processTimeMs": 670027312.0,"type":"keyDown", "unixTimeMs": 1752906804337.026611 },
{"activeModifiers":[],"character":"N", "isARepeat":false,"processTimeMs": 670027359.0,"type":"keyUp", "unixTimeMs": 1752906804373.373291 },
{"activeModifiers":[],"character":"G", "isARepeat":false,"processTimeMs": 670027421.0,"type":"keyUp", "unixTimeMs": 1752906804446.924561 },
{"activeModifiers":[],"character":"C", "isARepeat":false,"processTimeMs": 670027515.0,"type":"keyDown", "unixTimeMs": 1752906804529.550781 },
{"activeModifiers":[],"character":"H", "isARepeat":false,"processTimeMs": 670027562.0,"type":"keyDown", "unixTimeMs": 1752906804580.665527 },
{"activeModifiers":[],"character":"E", "isARepeat":false,"processTimeMs": 670027609.0,"type":"keyDown", "unixTimeMs": 1752906804633.672363 },
{"activeModifiers":[],"character":"C", "isARepeat":false,"processTimeMs": 670027625.0,"type":"keyUp", "unixTimeMs": 1752906804645.825195 },
{"activeModifiers":[],"character":"H", "isARepeat":false,"processTimeMs": 670027640.0,"type":"keyUp", "unixTimeMs": 1752906804659.716797 },
{"activeModifiers":[],"character":"E", "isARepeat":false,"processTimeMs": 670027718.0,"type":"keyUp", "unixTimeMs": 1752906804740.944580 },
{"activeModifiers":[],"character":"N", "isARepeat":false,"processTimeMs": 670027734.0,"type":"keyDown", "unixTimeMs": 1752906804752.270752 },
{"activeModifiers":[],"character":"G", "isARepeat":false,"processTimeMs": 670027781.0,"type":"keyDown", "unixTimeMs": 1752906804798.002441 },
{"activeModifiers":[],"character":"N", "isARepeat":false,"processTimeMs": 670027828.0,"type":"keyUp", "unixTimeMs": 1752906804843.804443 },
{"activeModifiers":[],"character":"Space", "isARepeat":false,"processTimeMs": 670027875.0,"type":"keyDown", "unixTimeMs": 1752906804889.209229 },
{"activeModifiers":[],"character":"G", "isARepeat":false,"processTimeMs": 670027890.0,"type":"keyUp", "unixTimeMs": 1752906804912.765625 },
{"activeModifiers":[],"character":"Space", "isARepeat":false,"processTimeMs": 670027968.0,"type":"keyUp", "unixTimeMs": 1752906804992.145996 },
{"activeModifiers":[],"character":"Y", "isARepeat":false,"processTimeMs": 670027984.0,"type":"keyDown", "unixTimeMs": 1752906805007.156738 },
{"activeModifiers":[],"character":"Y", "isARepeat":false,"processTimeMs": 670028078.0,"type":"keyUp", "unixTimeMs": 1752906805073.318359 },
{"activeModifiers":[],"character":"I", "isARepeat":false,"processTimeMs": 670028171.0,"type":"keyDown", "unixTimeMs": 1752906805182.634033 },
{"activeModifiers":[],"character":"I", "isARepeat":false,"processTimeMs": 670028234.0,"type":"keyUp", "unixTimeMs": 1752906805251.027100 },
{"activeModifiers":[],"character":"G", "isARepeat":false,"processTimeMs": 670028343.0,"type":"keyDown", "unixTimeMs": 1752906805366.815674 },
{"activeModifiers":[],"character":"G", "isARepeat":false,"processTimeMs": 670028437.0,"type":"keyUp", "unixTimeMs": 1752906805460.147705 },
{"activeModifiers":[],"character":"E", "isARepeat":false,"processTimeMs": 670028453.0,"type":"keyDown", "unixTimeMs": 1752906805468.655273 },
{"activeModifiers":[],"character":"E", "isARepeat":false,"processTimeMs": 670028546.0,"type":"keyUp", "unixTimeMs": 1752906805567.108643 },
{"activeModifiers":[],"character":"Space", "isARepeat":false,"processTimeMs": 670028578.0,"type":"keyDown", "unixTimeMs": 1752906805594.115479 },
{"activeModifiers":[],"character":"Space", "isARepeat":false,"processTimeMs": 670028687.0,"type":"keyUp", "unixTimeMs": 1752906805702.977539 },
{"activeModifiers":[],"character":"D", "isARepeat":false,"processTimeMs": 670028750.0,"type":"keyDown", "unixTimeMs": 1752906805772.160400 },
{"activeModifiers":[],"character":"D", "isARepeat":false,"processTimeMs": 670028828.0,"type":"keyUp", "unixTimeMs": 1752906805849.595703 },
{"activeModifiers":[],"character":"U", "isARepeat":false,"processTimeMs": 670028875.0,"type":"keyDown", "unixTimeMs": 1752906805895.195557 },
{"activeModifiers":[],"character":"U", "isARepeat":false,"processTimeMs": 670028953.0,"type":"keyUp", "unixTimeMs": 1752906805965.106201 },
{"activeModifiers":[],"character":"I", "isARepeat":false,"processTimeMs": 670029031.0,"type":"keyDown", "unixTimeMs": 1752906806054.258789 },
{"activeModifiers":[],"character":"I", "isARepeat":false,"processTimeMs": 670029109.0,"type":"keyUp", "unixTimeMs": 1752906806119.638916 },
{"activeModifiers":[],"character":"Y", "isARepeat":false,"processTimeMs": 670029250.0,"type":"keyDown", "unixTimeMs": 1752906806268.066406 },
{"activeModifiers":[],"character":"Y", "isARepeat":false,"processTimeMs": 670029328.0,"type":"keyUp", "unixTimeMs": 1752906806349.495605 },
{"activeModifiers":[],"character":"I", "isARepeat":false,"processTimeMs": 670029468.0,"type":"keyDown", "unixTimeMs": 1752906806494.256348 },
{"activeModifiers":[],"character":"I", "isARepeat":false,"processTimeMs": 670029500.0,"type":"keyUp", "unixTimeMs": 1752906806521.072998 },
{"activeModifiers":[],"character":"N", "isARepeat":false,"processTimeMs": 670029578.0,"type":"keyDown", "unixTimeMs": 1752906806600.107910 },
{"activeModifiers":[],"character":"N", "isARepeat":false,"processTimeMs": 670029687.0,"type":"keyUp", "unixTimeMs": 1752906806702.675537 },
{"activeModifiers":[],"character":"G", "isARepeat":false,"processTimeMs": 670029781.0,"type":"keyDown", "unixTimeMs": 1752906806796.904785 },
{"activeModifiers":[],"character":"G", "isARepeat":false,"processTimeMs": 670029875.0,"type":"keyUp", "unixTimeMs": 1752906806890.565918 },
{"activeModifiers":[],"character":"Space", "isARepeat":false,"processTimeMs": 670029984.0,"type":"keyDown", "unixTimeMs": 1752906807008.289062 },
{"activeModifiers":[],"character":"Space", "isARepeat":false,"processTimeMs": 670030093.0,"type":"keyUp", "unixTimeMs": 1752906807117.181396 },
{"activeModifiers":[],"character":"Y", "isARepeat":false,"processTimeMs": 670036031.0,"type":"keyDown", "unixTimeMs": 1752906813043.463135 },
{"activeModifiers":[],"character":"Y", "isARepeat":false,"processTimeMs": 670036109.0,"type":"keyUp", "unixTimeMs": 1752906813130.530762 },
{"activeModifiers":[],"character":"U", "isARepeat":false,"processTimeMs": 670036203.0,"type":"keyDown", "unixTimeMs": 1752906813224.673096 },
{"activeModifiers":[],"character":"U", "isARepeat":false,"processTimeMs": 670036296.0,"type":"keyUp", "unixTimeMs": 1752906813315.689697 },
{"activeModifiers":[],"character":"A", "isARepeat":false,"processTimeMs": 670036375.0,"type":"keyDown", "unixTimeMs": 1752906813391.501465 },
{"activeModifiers":[],"character":"A", "isARepeat":false,"processTimeMs": 670036453.0,"type":"keyUp", "unixTimeMs": 1752906813472.810303 },
{"activeModifiers":[],"character":"N", "isARepeat":false,"processTimeMs": 670036453.0,"type":"keyDown", "unixTimeMs": 1752906813478.847656 },
{"activeModifiers":[],"character":"N", "isARepeat":false,"processTimeMs": 670036531.0,"type":"keyUp", "unixTimeMs": 1752906813551.983154 },
{"activeModifiers":[],"character":"S", "isARepeat":false,"processTimeMs": 670036609.0,"type":"keyDown", "unixTimeMs": 1752906813631.198486 },
{"activeModifiers":[],"character":"S", "isARepeat":false,"processTimeMs": 670036687.0,"type":"keyUp", "unixTimeMs": 1752906813703.240723 },
{"activeModifiers":[],"character":"U", "isARepeat":false,"processTimeMs": 670036718.0,"type":"keyDown", "unixTimeMs": 1752906813730.892334 },
{"activeModifiers":[],"character":"U", "isARepeat":false,"processTimeMs": 670036796.0,"type":"keyUp", "unixTimeMs": 1752906813815.338135 },
{"activeModifiers":[],"character":"Space", "isARepeat":false,"processTimeMs": 670036937.0,"type":"keyDown", "unixTimeMs": 1752906813951.289551 },
{"activeModifiers":[],"character":"Space", "isARepeat":false,"processTimeMs": 670037078.0,"type":"keyUp", "unixTimeMs": 1752906814094.335693 },
{"activeModifiers":[],"character":"S", "isARepeat":false,"processTimeMs": 670037562.0,"type":"keyDown", "unixTimeMs": 1752906814574.304688 },
{"activeModifiers":[],"character":"H", "isARepeat":false,"processTimeMs": 670037640.0,"type":"keyDown", "unixTimeMs": 1752906814653.134521 },
{"activeModifiers":[],"character":"S", "isARepeat":false,"processTimeMs": 670037656.0,"type":"keyUp", "unixTimeMs": 1752906814674.794434 },
{"activeModifiers":[],"character":"H", "isARepeat":false,"processTimeMs": 670037703.0,"type":"keyUp", "unixTimeMs": 1752906814721.222412 },
{"activeModifiers":[],"character":"U", "isARepeat":false,"processTimeMs": 670037781.0,"type":"keyDown", "unixTimeMs": 1752906814806.522705 },
{"activeModifiers":[],"character":"U", "isARepeat":false,"processTimeMs": 670037859.0,"type":"keyUp", "unixTimeMs": 1752906814872.298340 },
{"activeModifiers":[],"character":"L", "isARepeat":false,"processTimeMs": 670038000.0,"type":"keyDown", "unixTimeMs": 1752906815018.952148 },
{"activeModifiers":[],"character":"L", "isARepeat":false,"processTimeMs": 670038062.0,"type":"keyUp", "unixTimeMs": 1752906815074.054688 },
{"activeModifiers":[],"character":"I", "isARepeat":false,"processTimeMs": 670038140.0,"type":"keyDown", "unixTimeMs": 1752906815165.568848 },
{"activeModifiers":[],"character":"A", "isARepeat":false,"processTimeMs": 670038187.0,"type":"keyDown", "unixTimeMs": 1752906815209.314941 },
{"activeModifiers":[],"character":"I", "isARepeat":false,"processTimeMs": 670038218.0,"type":"keyUp", "unixTimeMs": 1752906815236.910889 },
{"activeModifiers":[],"character":"A", "isARepeat":false,"processTimeMs": 670038265.0,"type":"keyUp", "unixTimeMs": 1752906815280.258057 },
{"activeModifiers":[],"character":"N", "isARepeat":false,"processTimeMs": 670038312.0,"type":"keyDown", "unixTimeMs": 1752906815328.446045 },
{"activeModifiers":[],"character":"G", "isARepeat":false,"processTimeMs": 670038390.0,"type":"keyDown", "unixTimeMs": 1752906815408.947998 },
{"activeModifiers":[],"character":"N", "isARepeat":false,"processTimeMs": 670038406.0,"type":"keyUp", "unixTimeMs": 1752906815424.004395 },
{"activeModifiers":[],"character":"G", "isARepeat":false,"processTimeMs": 670038484.0,"type":"keyUp", "unixTimeMs": 1752906815506.897705 },
{"activeModifiers":[],"character":"Space", "isARepeat":false,"processTimeMs": 670038546.0,"type":"keyDown", "unixTimeMs": 1752906815563.084961 },
{"activeModifiers":[],"character":"D", "isARepeat":false,"processTimeMs": 670038671.0,"type":"keyDown", "unixTimeMs": 1752906815686.870361 },
{"activeModifiers":[],"character":"Space", "isARepeat":false,"processTimeMs": 670038687.0,"type":"keyUp", "unixTimeMs": 1752906815710.671387 },
{"activeModifiers":[],"character":"E", "isARepeat":false,"processTimeMs": 670038734.0,"type":"keyDown", "unixTimeMs": 1752906815751.305664 },
{"activeModifiers":[],"character":"D", "isARepeat":false,"processTimeMs": 670038796.0,"type":"keyUp", "unixTimeMs": 1752906815821.325928 },
{"activeModifiers":[],"character":"Space", "isARepeat":false,"processTimeMs": 670038812.0,"type":"keyDown", "unixTimeMs": 1752906815828.847168 },
{"activeModifiers":[],"character":"E", "isARepeat":false,"processTimeMs": 670038843.0,"type":"keyUp", "unixTimeMs": 1752906815869.443115 },
{"activeModifiers":[],"character":"Space", "isARepeat":false,"processTimeMs": 670038937.0,"type":"keyUp", "unixTimeMs": 1752906815931.718262 },
{"activeModifiers":[],"character":"L", "isARepeat":false,"processTimeMs": 670039578.0,"type":"keyDown", "unixTimeMs": 1752906816591.066895 },
{"activeModifiers":[],"character":"L", "isARepeat":false,"processTimeMs": 670039640.0,"type":"keyUp", "unixTimeMs": 1752906816654.305664 },
{"activeModifiers":[],"character":"I", "isARepeat":false,"processTimeMs": 670039718.0,"type":"keyDown", "unixTimeMs": 1752906816739.240723 },
{"activeModifiers":[],"character":"I", "isARepeat":false,"processTimeMs": 670039796.0,"type":"keyUp", "unixTimeMs": 1752906816820.413574 },
{"activeModifiers":[],"character":"E", "isARepeat":false,"processTimeMs": 670039828.0,"type":"keyDown", "unixTimeMs": 1752906816846.597168 },
{"activeModifiers":[],"character":"E", "isARepeat":false,"processTimeMs": 670039890.0,"type":"keyUp", "unixTimeMs": 1752906816902.667969 },
{"activeModifiers":[],"character":"B", "isARepeat":false,"processTimeMs": 670039937.0,"type":"keyDown", "unixTimeMs": 1752906816956.915771 },
{"activeModifiers":[],"character":"B", "isARepeat":false,"processTimeMs": 670040000.0,"type":"keyUp", "unixTimeMs": 1752906817021.539307 },
{"activeModifiers":[],"character":"I", "isARepeat":false,"processTimeMs": 670040078.0,"type":"keyDown", "unixTimeMs": 1752906817101.753174 },
{"activeModifiers":[],"character":"I", "isARepeat":false,"processTimeMs": 670040156.0,"type":"keyUp", "unixTimeMs": 1752906817181.929688 },
{"activeModifiers":[],"character":"A", "isARepeat":false,"processTimeMs": 670040203.0,"type":"keyDown", "unixTimeMs": 1752906817222.804443 },
{"activeModifiers":[],"character":"O", "isARepeat":false,"processTimeMs": 670040265.0,"type":"keyDown", "unixTimeMs": 1752906817281.486816 },
{"activeModifiers":[],"character":"A", "isARepeat":false,"processTimeMs": 670040296.0,"type":"keyUp", "unixTimeMs": 1752906817316.794434 },
{"activeModifiers":[],"character":"O", "isARepeat":false,"processTimeMs": 670040343.0,"type":"keyUp", "unixTimeMs": 1752906817354.676025 },
{"activeModifiers":[],"character":"Space", "isARepeat":false,"processTimeMs": 670040375.0,"type":"keyDown", "unixTimeMs": 1752906817387.801514 },
{"activeModifiers":[],"character":"Space", "isARepeat":false,"processTimeMs": 670040484.0,"type":"keyUp", "unixTimeMs": 1752906817504.752441 },
{"activeModifiers":[],"character":".", "isARepeat":false,"processTimeMs": 670041046.0,"type":"keyDown", "unixTimeMs": 1752906818066.986328 },
{"activeModifiers":[],"character":".", "isARepeat":false,"processTimeMs": 670041093.0,"type":"keyUp", "unixTimeMs": 1752906818105.814697 },
{"activeModifiers":[],"character":"Enter", "isARepeat":false,"processTimeMs": 670041218.0,"type":"keyDown", "unixTimeMs": 1752906818243.605713 },
{"activeModifiers":[],"character":"Enter", "isARepeat":false,"processTimeMs": 670041296.0,"type":"keyUp", "unixTimeMs": 1752906818307.731934 },
]