"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var _a;
var webview = (_a = window.chrome) === null || _a === void 0 ? void 0 : _a.webview;
window.getClientMode = function () {
    return webview ? "webview" : "browser";
};
function evalScript(sequence, code, returnData) {
    var _a;
    try {
        var re = eval(code);
        webview.postMessage("@" + JSON.stringify({
            Type: "evalResult",
            Data: returnData ? JSON.stringify(re === undefined ? null : re) : null,
            Sequence: sequence
        }));
    }
    catch (e) {
        webview.postMessage("@" + JSON.stringify({
            Type: "evalResult",
            Error: { Message: (_a = e === null || e === void 0 ? void 0 : e.message) !== null && _a !== void 0 ? _a : "Unknown Error" },
            Sequence: sequence
        }));
    }
}
function initMessageHandler() {
    if (!webview)
        return;
    webview.addEventListener("message", (e) => {
        if (!e.data)
            return;
        if (e.data[0] != '@')
            return;
        var data = JSON.parse(e.data.substring(1));
        if (data.Type == "eval")
            evalScript(data.Sequence, data.Data.Code, data.Data.ReturnResult);
    });
    webview.postMessage('@' + JSON.stringify({
        Type: 'PageInit',
        Data: {}
    }));
}
initMessageHandler();
function runBlazor() {
    return __awaiter(this, void 0, void 0, function* () {
        var _a;
        // 重试次数
        const maximumRetryCount = 10000;
        // 重试间隔
        const retryIntervalMilliseconds = 10000;
        var _curVisible = false;
        var _timer = null;
        var root = document.getElementById("blazor-network-state-bg");
        var panel = document.getElementById("blazor-network-state-panel");
        var message = panel.firstElementChild;
        var webview = (_a = window.chrome) === null || _a === void 0 ? void 0 : _a.webview;
        function poseEvent(visible, state, retry = 0, data = null) {
            var e = new Event("blazorConnectionStateChanged");
            e.state = state;
            e.data = data;
            e.visible = visible;
            if (webview && webview.postMessage)
                webview.postMessage('@' + JSON.stringify({
                    Type: 'BlazorConnectionStateChanged',
                    Data: {
                        State: state,
                        Retry: retry
                    }
                }));
            document.dispatchEvent(e);
            if (_curVisible != visible) {
                _curVisible = visible;
                if (visible) {
                    if (_timer) {
                        clearTimeout(_timer);
                        _timer = null;
                    }
                    root.className = "visible";
                    panel.className = "visible";
                }
                else {
                    _timer = setTimeout(() => {
                        _timer = null;
                        root.className = "";
                        panel.className = "";
                    }, 5000);
                    root.className = "hide";
                    panel.className = "hide";
                }
            }
            console.log(_curVisible, visible, state, data, _timer);
            var title = "";
            var detail = "";
            switch (state) {
                case "disconnected":
                    title = "网络异常";
                    detail = "等待重新连接";
                    break;
                case "connecting":
                    title = "网络异常";
                    detail = `正在开始连接...(${retry + 1})`;
                    break;
                case "error":
                    title = "网络异常";
                    detail = `连接失败(${retry + 1}): 请检查网络是否正常`;
                    break;
                case "rejected":
                    title = "网络异常";
                    detail = `连接失败(${retry + 1}): 数据错误`;
                    location.reload();
                    break;
                case "connected":
                    title = "网络异常";
                    detail = `正在连接中...(${retry + 1})`;
                    break;
                case "up":
                    title = "网络异常";
                    detail = `已重新连接!`;
                    break;
                case "timeout":
                    title = "网络异常";
                    detail = "连接重新超时!";
                    break;
                default:
                    title = "网络异常";
                    detail = `未知错误!：${state}`;
                    break;
            }
            message.innerHTML = `<div><p class='title'>${title}</p><p class='message'>${detail}</p></div>`;
        }
        const blazor = window.Blazor;
        function delay(interval) {
            return new Promise((resolve, rejest) => {
                setTimeout(() => resolve(true), interval);
            });
        }
        //https://github.com/dotnet/aspnetcore/blob/main/src/Components/Web.JS/src/Boot.Server.ts
        //https://learn.microsoft.com/en-us/aspnet/core/blazor/fundamentals/signalr?view=aspnetcore-8.0
        const startReconnectionProcess = () => {
            poseEvent(true, "disconnected");
            let isCanceled = false;
            let rejected = false;
            (() => __awaiter(this, void 0, void 0, function* () {
                var interval = 200;
                for (let i = 0; i < maximumRetryCount; i++) {
                    //console.log(`试图重新连接: ${i + 1} of ${maximumRetryCount}`)
                    yield delay(interval);
                    interval = Math.min(retryIntervalMilliseconds, interval * 2);
                    if (isCanceled) {
                        return;
                    }
                    try {
                        poseEvent(true, "connecting", i);
                        const result = yield blazor.reconnect();
                        if (result) {
                            // 成功重新连接到服务器。
                            poseEvent(false, "connected", i, result);
                            if (isCanceled)
                                return;
                            yield delay(2000);
                            if (isCanceled)
                                return;
                            yield blazor.disconnect();
                            poseEvent(true, "error", i, "连接超时");
                        }
                        else {
                            // 已到达服务器，但连接被拒绝;重新加载页面。
                            poseEvent(true, "rejected", i);
                            if (!rejected) {
                                rejected = true;
                                interval = 200;
                            }
                        }
                    }
                    catch (err) {
                        poseEvent(true, "error", i, err);
                        //没有到达服务器;再试一次。
                    }
                }
                // 重试次数太多;重新加载页面。
                poseEvent(false, "timeout", maximumRetryCount);
            }))();
            return {
                up: () => {
                    isCanceled = true;
                },
            };
        };
        let currentReconnectionProcess = null;
        yield blazor.start({
            configureSignalR: function (builder) {
                builder.withServerTimeout(5000)
                    .withKeepAliveInterval(5000);
            },
            circuit: {
                reconnectionHandler: {
                    onConnectionDown() {
                        if (!currentReconnectionProcess)
                            currentReconnectionProcess = startReconnectionProcess();
                    },
                    onConnectionUp() {
                        poseEvent(false, "up");
                        if (!currentReconnectionProcess)
                            return;
                        currentReconnectionProcess.up();
                        currentReconnectionProcess = null;
                    }
                },
            }
        });
        poseEvent(false, "up");
    });
}
if (webview)
    window.runBlazor = runBlazor;
else
    runBlazor();
//window.document.addEventListener("load", runBlazor);
