[

{"activeModifiers":[],"character":"G", "isARepeat":false,"processTimeMs": 137555953.0,"type":"keyDown", "unixTimeMs": 1752374337122.731689 },
{"activeModifiers":[],"character":"G", "isARepeat":false,"processTimeMs": 137556062.0,"type":"keyUp", "unixTimeMs": 1752374337238.090332 },
{"activeModifiers":[],"character":"E", "isARepeat":false,"processTimeMs": 137556593.0,"type":"keyDown", "unixTimeMs": 1752374337765.133545 },
{"activeModifiers":[],"character":"E", "isARepeat":false,"processTimeMs": 137556671.0,"type":"keyUp", "unixTimeMs": 1752374337838.739990 },
{"activeModifiers":[],"character":"I", "isARepeat":false,"processTimeMs": 137557015.0,"type":"keyDown", "unixTimeMs": 1752374338177.749268 },
{"activeModifiers":[],"character":"I", "isARepeat":false,"processTimeMs": 137557062.0,"type":"keyUp", "unixTimeMs": 1752374338234.074707 },
{"activeModifiers":[],"character":"Backspace", "isARepeat":false,"processTimeMs": 137557484.0,"type":"keyDown", "unixTimeMs": 1752374338652.437256 },
{"activeModifiers":[],"character":"Backspace", "isARepeat":false,"processTimeMs": 137557562.0,"type":"keyUp", "unixTimeMs": 1752374338728.416748 },
{"activeModifiers":[],"character":"M", "isARepeat":false,"processTimeMs": 137557843.0,"type":"keyDown", "unixTimeMs": 1752374339009.677734 },
{"activeModifiers":[],"character":"M", "isARepeat":false,"processTimeMs": 137557984.0,"type":"keyUp", "unixTimeMs": 1752374339157.446289 },
{"activeModifiers":[],"character":"I", "isARepeat":false,"processTimeMs": 137558031.0,"type":"keyDown", "unixTimeMs": 1752374339205.239258 },
{"activeModifiers":[],"character":"I", "isARepeat":false,"processTimeMs": 137558125.0,"type":"keyUp", "unixTimeMs": 1752374339293.082764 },
{"activeModifiers":[],"character":"N", "isARepeat":false,"processTimeMs": 137558187.0,"type":"keyDown", "unixTimeMs": 1752374339363.468750 },
{"activeModifiers":[],"character":"I", "isARepeat":false,"processTimeMs": 137558265.0,"type":"keyDown", "unixTimeMs": 1752374339441.283936 },
{"activeModifiers":[],"character":"N", "isARepeat":false,"processTimeMs": 137558312.0,"type":"keyUp", "unixTimeMs": 1752374339481.202637 },
{"activeModifiers":[],"character":"I", "isARepeat":false,"processTimeMs": 137558390.0,"type":"keyUp", "unixTimeMs": 1752374339558.730713 },
{"activeModifiers":[],"character":"Enter", "isARepeat":false,"processTimeMs": 137558625.0,"type":"keyDown", "unixTimeMs": 1752374339797.365234 },
{"activeModifiers":[],"character":"Enter", "isARepeat":false,"processTimeMs": 137558671.0,"type":"keyUp", "unixTimeMs": 1752374339846.484375 },
{"activeModifiers":[],"character":"Enter", "isARepeat":false,"processTimeMs": 137558968.0,"type":"keyDown", "unixTimeMs": 1752374340142.718994 },
{"activeModifiers":[],"character":"Enter", "isARepeat":false,"processTimeMs": 137559031.0,"type":"keyUp", "unixTimeMs": 1752374340201.696045 },
{"activeModifiers":[],"character":"B", "isARepeat":false,"processTimeMs": 137567343.0,"type":"keyDown", "unixTimeMs": 1752374348520.575684 },
{"activeModifiers":[],"character":"B", "isARepeat":false,"processTimeMs": 137567437.0,"type":"keyUp", "unixTimeMs": 1752374348604.135498 },
{"activeModifiers":[],"character":"A", "isARepeat":false,"processTimeMs": 137567453.0,"type":"keyDown", "unixTimeMs": 1752374348616.215088 },
{"activeModifiers":[],"character":"A", "isARepeat":false,"processTimeMs": 137567562.0,"type":"keyUp", "unixTimeMs": 1752374348730.241943 },
{"activeModifiers":[],"character":"N", "isARepeat":false,"processTimeMs": 137567625.0,"type":"keyDown", "unixTimeMs": 1752374348789.260742 },
{"activeModifiers":[],"character":"G", "isARepeat":false,"processTimeMs": 137567656.0,"type":"keyDown", "unixTimeMs": 1752374348825.800781 },
{"activeModifiers":[],"character":"N", "isARepeat":false,"processTimeMs": 137567703.0,"type":"keyUp", "unixTimeMs": 1752374348878.164795 },
{"activeModifiers":[],"character":"G", "isARepeat":false,"processTimeMs": 137567750.0,"type":"keyUp", "unixTimeMs": 1752374348914.908447 },
{"activeModifiers":[],"character":"W", "isARepeat":false,"processTimeMs": 137567875.0,"type":"keyDown", "unixTimeMs": 1752374349040.062988 },
{"activeModifiers":[],"character":"W", "isARepeat":false,"processTimeMs": 137567906.0,"type":"keyUp", "unixTimeMs": 1752374349077.764893 },
{"activeModifiers":[],"character":"O", "isARepeat":false,"processTimeMs": 137567937.0,"type":"keyDown", "unixTimeMs": 1752374349103.329102 },
{"activeModifiers":[],"character":"O", "isARepeat":false,"processTimeMs": 137568046.0,"type":"keyUp", "unixTimeMs": 1752374349214.982422 },
{"activeModifiers":[],"character":"Space", "isARepeat":false,"processTimeMs": 137568046.0,"type":"keyDown", "unixTimeMs": 1752374349219.537598 },
{"activeModifiers":[],"character":"Space", "isARepeat":false,"processTimeMs": 137568171.0,"type":"keyUp", "unixTimeMs": 1752374349339.321289 },
{"activeModifiers":[],"character":"P", "isARepeat":false,"processTimeMs": 137568359.0,"type":"keyDown", "unixTimeMs": 1752374349533.610352 },
{"activeModifiers":[],"character":"P", "isARepeat":false,"processTimeMs": 137568468.0,"type":"keyUp", "unixTimeMs": 1752374349639.660400 },
{"activeModifiers":[],"character":"E", "isARepeat":false,"processTimeMs": 137568484.0,"type":"keyDown", "unixTimeMs": 1752374349653.841797 },
{"activeModifiers":[],"character":"E", "isARepeat":false,"processTimeMs": 137568546.0,"type":"keyUp", "unixTimeMs": 1752374349717.867920 },
{"activeModifiers":[],"character":"I", "isARepeat":false,"processTimeMs": 137568593.0,"type":"keyDown", "unixTimeMs": 1752374349767.882080 },
{"activeModifiers":[],"character":"I", "isARepeat":false,"processTimeMs": 137568687.0,"type":"keyUp", "unixTimeMs": 1752374349853.573242 },
{"activeModifiers":[],"character":"Z", "isARepeat":false,"processTimeMs": 137568718.0,"type":"keyDown", "unixTimeMs": 1752374349895.983154 },
{"activeModifiers":[],"character":"Z", "isARepeat":false,"processTimeMs": 137568796.0,"type":"keyUp", "unixTimeMs": 1752374349964.899414 },
{"activeModifiers":[],"character":"H", "isARepeat":false,"processTimeMs": 137568828.0,"type":"keyDown", "unixTimeMs": 1752374350003.310303 },
{"activeModifiers":[],"character":"H", "isARepeat":false,"processTimeMs": 137568906.0,"type":"keyUp", "unixTimeMs": 1752374350070.193115 },
{"activeModifiers":[],"character":"I", "isARepeat":false,"processTimeMs": 137568984.0,"type":"keyDown", "unixTimeMs": 1752374350152.690918 },
{"activeModifiers":[],"character":"Space", "isARepeat":false,"processTimeMs": 137569031.0,"type":"keyDown", "unixTimeMs": 1752374350199.084961 },
{"activeModifiers":[],"character":"I", "isARepeat":false,"processTimeMs": 137569093.0,"type":"keyUp", "unixTimeMs": 1752374350259.006104 },
{"activeModifiers":[],"character":"Space", "isARepeat":false,"processTimeMs": 137569140.0,"type":"keyUp", "unixTimeMs": 1752374350306.927734 },
{"activeModifiers":[],"character":"I", "isARepeat":false,"processTimeMs": 137569406.0,"type":"keyDown", "unixTimeMs": 1752374350583.077881 },
{"activeModifiers":[],"character":"X", "isARepeat":false,"processTimeMs": 137569437.0,"type":"keyDown", "unixTimeMs": 1752374350606.876709 },
{"activeModifiers":[],"character":"X", "isARepeat":false,"processTimeMs": 137569468.0,"type":"keyUp", "unixTimeMs": 1752374350642.273682 },
{"activeModifiers":[],"character":"I", "isARepeat":false,"processTimeMs": 137569484.0,"type":"keyUp", "unixTimeMs": 1752374350659.504883 },
{"activeModifiers":[],"character":"Backspace", "isARepeat":false,"processTimeMs": 137569796.0,"type":"keyDown", "unixTimeMs": 1752374350969.874756 },
{"activeModifiers":[],"character":"Backspace", "isARepeat":false,"processTimeMs": 137569875.0,"type":"keyUp", "unixTimeMs": 1752374351052.565674 },
{"activeModifiers":[],"character":"Backspace", "isARepeat":false,"processTimeMs": 137570234.0,"type":"keyDown", "unixTimeMs": 1752374351406.209961 },
{"activeModifiers":[],"character":"Backspace", "isARepeat":false,"processTimeMs": 137570328.0,"type":"keyUp", "unixTimeMs": 1752374351492.080566 },
{"activeModifiers":[],"character":"Y", "isARepeat":false,"processTimeMs": 137570609.0,"type":"keyDown", "unixTimeMs": 1752374351779.431152 },
{"activeModifiers":[],"character":"Y", "isARepeat":false,"processTimeMs": 137570687.0,"type":"keyUp", "unixTimeMs": 1752374351859.658691 },
{"activeModifiers":[],"character":"X", "isARepeat":false,"processTimeMs": 137570734.0,"type":"keyDown", "unixTimeMs": 1752374351902.034424 },
{"activeModifiers":[],"character":"I", "isARepeat":false,"processTimeMs": 137570796.0,"type":"keyDown", "unixTimeMs": 1752374351961.242920 },
{"activeModifiers":[],"character":"X", "isARepeat":false,"processTimeMs": 137570812.0,"type":"keyUp", "unixTimeMs": 1752374351986.509277 },
{"activeModifiers":[],"character":"I", "isARepeat":false,"processTimeMs": 137570859.0,"type":"keyUp", "unixTimeMs": 1752374352033.187500 },
{"activeModifiers":[],"character":"A", "isARepeat":false,"processTimeMs": 137570953.0,"type":"keyDown", "unixTimeMs": 1752374352099.617920 },
{"activeModifiers":[],"character":"A", "isARepeat":false,"processTimeMs": 137571000.0,"type":"keyUp", "unixTimeMs": 1752374352176.839600 },
{"activeModifiers":[],"character":"Space", "isARepeat":false,"processTimeMs": 137571109.0,"type":"keyDown", "unixTimeMs": 1752374352278.043701 },
{"activeModifiers":[],"character":"Space", "isARepeat":false,"processTimeMs": 137571187.0,"type":"keyUp", "unixTimeMs": 1752374352362.388916 },
{"activeModifiers":[],"character":"M", "isARepeat":false,"processTimeMs": 137572296.0,"type":"keyDown", "unixTimeMs": 1752374353463.275635 },
{"activeModifiers":[],"character":"M", "isARepeat":false,"processTimeMs": 137572375.0,"type":"keyUp", "unixTimeMs": 1752374353541.858398 },
{"activeModifiers":[],"character":"C", "isARepeat":false,"processTimeMs": 137572406.0,"type":"keyDown", "unixTimeMs": 1752374353575.507568 },
{"activeModifiers":[],"character":"C", "isARepeat":false,"processTimeMs": 137572515.0,"type":"keyUp", "unixTimeMs": 1752374353680.229980 },
{"activeModifiers":[],"character":"P", "isARepeat":false,"processTimeMs": 137572750.0,"type":"keyDown", "unixTimeMs": 1752374353919.796387 },
{"activeModifiers":[],"character":"P", "isARepeat":false,"processTimeMs": 137572828.0,"type":"keyUp", "unixTimeMs": 1752374354002.315186 },
{"activeModifiers":[],"character":"Enter", "isARepeat":false,"processTimeMs": 137573421.0,"type":"keyDown", "unixTimeMs": 1752374354587.531982 },
{"activeModifiers":[],"character":"Enter", "isARepeat":false,"processTimeMs": 137573500.0,"type":"keyUp", "unixTimeMs": 1752374354663.169189 },
{"activeModifiers":[],"character":"Backspace", "isARepeat":false,"processTimeMs": 137577625.0,"type":"keyDown", "unixTimeMs": 1752374358787.093506 },
{"activeModifiers":[],"character":"Backspace", "isARepeat":false,"processTimeMs": 137577718.0,"type":"keyUp", "unixTimeMs": 1752374358885.364502 },
{"activeModifiers":[],"character":"Backspace", "isARepeat":false,"processTimeMs": 137577796.0,"type":"keyDown", "unixTimeMs": 1752374358971.414551 },
{"activeModifiers":[],"character":"Backspace", "isARepeat":false,"processTimeMs": 137577875.0,"type":"keyUp", "unixTimeMs": 1752374359051.357910 },
{"activeModifiers":[],"character":"Backspace", "isARepeat":false,"processTimeMs": 137577984.0,"type":"keyDown", "unixTimeMs": 1752374359149.958496 },
{"activeModifiers":[],"character":"Backspace", "isARepeat":false,"processTimeMs": 137578046.0,"type":"keyUp", "unixTimeMs": 1752374359220.203857 },
{"activeModifiers":[],"character":"Backspace", "isARepeat":false,"processTimeMs": 137578156.0,"type":"keyDown", "unixTimeMs": 1752374359325.552734 },
{"activeModifiers":[],"character":"Backspace", "isARepeat":false,"processTimeMs": 137578218.0,"type":"keyUp", "unixTimeMs": 1752374359387.264893 },
{"activeModifiers":[],"character":"Backspace", "isARepeat":false,"processTimeMs": 137578312.0,"type":"keyDown", "unixTimeMs": 1752374359485.622559 },
{"activeModifiers":[],"character":"Backspace", "isARepeat":false,"processTimeMs": 137578390.0,"type":"keyUp", "unixTimeMs": 1752374359555.410156 },
{"activeModifiers":[],"character":"Backspace", "isARepeat":false,"processTimeMs": 137578500.0,"type":"keyDown", "unixTimeMs": 1752374359669.073486 },
{"activeModifiers":[],"character":"Backspace", "isARepeat":false,"processTimeMs": 137578562.0,"type":"keyUp", "unixTimeMs": 1752374359732.469971 },
{"activeModifiers":[],"character":"Backspace", "isARepeat":false,"processTimeMs": 137578703.0,"type":"keyDown", "unixTimeMs": 1752374359869.158691 },
{"activeModifiers":[],"character":"Backspace", "isARepeat":false,"processTimeMs": 137578734.0,"type":"keyUp", "unixTimeMs": 1752374359910.370605 },
{"activeModifiers":[],"character":"Backspace", "isARepeat":false,"processTimeMs": 137578859.0,"type":"keyDown", "unixTimeMs": 1752374360026.830078 },
{"activeModifiers":[],"character":"Backspace", "isARepeat":false,"processTimeMs": 137578937.0,"type":"keyUp", "unixTimeMs": 1752374360109.961670 },
{"activeModifiers":[],"character":"Backspace", "isARepeat":false,"processTimeMs": 137579015.0,"type":"keyDown", "unixTimeMs": 1752374360187.850342 },
{"activeModifiers":[],"character":"Backspace", "isARepeat":false,"processTimeMs": 137579093.0,"type":"keyUp", "unixTimeMs": 1752374360264.639648 },
{"activeModifiers":[],"character":"B", "isARepeat":false,"processTimeMs": 137579578.0,"type":"keyDown", "unixTimeMs": 1752374360750.027344 },
{"activeModifiers":[],"character":"B", "isARepeat":false,"processTimeMs": 137579656.0,"type":"keyUp", "unixTimeMs": 1752374360819.644043 },
{"activeModifiers":[],"character":"A", "isARepeat":false,"processTimeMs": 137579687.0,"type":"keyDown", "unixTimeMs": 1752374360854.093994 },
{"activeModifiers":[],"character":"N", "isARepeat":false,"processTimeMs": 137579750.0,"type":"keyDown", "unixTimeMs": 1752374360921.203613 },
{"activeModifiers":[],"character":"A", "isARepeat":false,"processTimeMs": 137579781.0,"type":"keyUp", "unixTimeMs": 1752374360947.829102 },
{"activeModifiers":[],"character":"N", "isARepeat":false,"processTimeMs": 137579828.0,"type":"keyUp", "unixTimeMs": 1752374361004.362305 },
{"activeModifiers":[],"character":"G", "isARepeat":false,"processTimeMs": 137579843.0,"type":"keyDown", "unixTimeMs": 1752374361016.783691 },
{"activeModifiers":[],"character":"G", "isARepeat":false,"processTimeMs": 137579921.0,"type":"keyUp", "unixTimeMs": 1752374361097.575928 },
{"activeModifiers":[],"character":"W", "isARepeat":false,"processTimeMs": 137580046.0,"type":"keyDown", "unixTimeMs": 1752374361220.167480 },
{"activeModifiers":[],"character":"W", "isARepeat":false,"processTimeMs": 137580125.0,"type":"keyUp", "unixTimeMs": 1752374361300.166504 },
{"activeModifiers":[],"character":"O", "isARepeat":false,"processTimeMs": 137580156.0,"type":"keyDown", "unixTimeMs": 1752374361327.882812 },
{"activeModifiers":[],"character":"O", "isARepeat":false,"processTimeMs": 137580234.0,"type":"keyUp", "unixTimeMs": 1752374361396.727295 },
{"activeModifiers":[],"character":"Space", "isARepeat":false,"processTimeMs": 137580250.0,"type":"keyDown", "unixTimeMs": 1752374361414.271729 },
{"activeModifiers":[],"character":"Space", "isARepeat":false,"processTimeMs": 137580343.0,"type":"keyUp", "unixTimeMs": 1752374361519.616455 },
{"activeModifiers":[],"character":"X", "isARepeat":false,"processTimeMs": 137580609.0,"type":"keyDown", "unixTimeMs": 1752374361782.810791 },
{"activeModifiers":[],"character":"I", "isARepeat":false,"processTimeMs": 137580703.0,"type":"keyDown", "unixTimeMs": 1752374361867.288818 },
{"activeModifiers":[],"character":"X", "isARepeat":false,"processTimeMs": 137580703.0,"type":"keyUp", "unixTimeMs": 1752374361877.994873 },
{"activeModifiers":[],"character":"I", "isARepeat":false,"processTimeMs": 137580781.0,"type":"keyUp", "unixTimeMs": 1752374361944.649658 },
{"activeModifiers":[],"character":"N", "isARepeat":false,"processTimeMs": 137580859.0,"type":"keyDown", "unixTimeMs": 1752374362030.359863 },
{"activeModifiers":[],"character":"Z", "isARepeat":false,"processTimeMs": 137580890.0,"type":"keyDown", "unixTimeMs": 1752374362064.747559 },
{"activeModifiers":[],"character":"N", "isARepeat":false,"processTimeMs": 137580937.0,"type":"keyUp", "unixTimeMs": 1752374362111.269531 },
{"activeModifiers":[],"character":"Z", "isARepeat":false,"processTimeMs": 137580984.0,"type":"keyUp", "unixTimeMs": 1752374362159.461914 },
{"activeModifiers":[],"character":"N", "isARepeat":false,"processTimeMs": 137581078.0,"type":"keyDown", "unixTimeMs": 1752374362241.221680 },
{"activeModifiers":[],"character":"E", "isARepeat":false,"processTimeMs": 137581093.0,"type":"keyDown", "unixTimeMs": 1752374362265.627441 },
{"activeModifiers":[],"character":"N", "isARepeat":false,"processTimeMs": 137581140.0,"type":"keyUp", "unixTimeMs": 1752374362316.053955 },
{"activeModifiers":[],"character":"E", "isARepeat":false,"processTimeMs": 137581203.0,"type":"keyUp", "unixTimeMs": 1752374362366.604736 },
{"activeModifiers":[],"character":"G", "isARepeat":false,"processTimeMs": 137581234.0,"type":"keyDown", "unixTimeMs": 1752374362397.724854 },
{"activeModifiers":[],"character":"Space", "isARepeat":false,"processTimeMs": 137581328.0,"type":"keyDown", "unixTimeMs": 1752374362500.862061 },
{"activeModifiers":[],"character":"G", "isARepeat":false,"processTimeMs": 137581343.0,"type":"keyUp", "unixTimeMs": 1752374362516.831055 },
{"activeModifiers":[],"character":"Space", "isARepeat":false,"processTimeMs": 137581437.0,"type":"keyUp", "unixTimeMs": 1752374362602.876221 },
{"activeModifiers":[],"character":"Y", "isARepeat":false,"processTimeMs": 137581562.0,"type":"keyDown", "unixTimeMs": 1752374362724.793701 },
{"activeModifiers":[],"character":"Y", "isARepeat":false,"processTimeMs": 137581640.0,"type":"keyUp", "unixTimeMs": 1752374362815.034912 },
{"activeModifiers":[],"character":"I", "isARepeat":false,"processTimeMs": 137581750.0,"type":"keyDown", "unixTimeMs": 1752374362922.763672 },
{"activeModifiers":[],"character":"I", "isARepeat":false,"processTimeMs": 137581828.0,"type":"keyUp", "unixTimeMs": 1752374363004.385498 },
{"activeModifiers":[],"character":"G", "isARepeat":false,"processTimeMs": 137582015.0,"type":"keyDown", "unixTimeMs": 1752374363186.663086 },
{"activeModifiers":[],"character":"E", "isARepeat":false,"processTimeMs": 137582093.0,"type":"keyDown", "unixTimeMs": 1752374363264.567627 },
{"activeModifiers":[],"character":"G", "isARepeat":false,"processTimeMs": 137582109.0,"type":"keyUp", "unixTimeMs": 1752374363279.430176 },
{"activeModifiers":[],"character":"Space", "isARepeat":false,"processTimeMs": 137582203.0,"type":"keyDown", "unixTimeMs": 1752374363375.843262 },
{"activeModifiers":[],"character":"E", "isARepeat":false,"processTimeMs": 137582218.0,"type":"keyUp", "unixTimeMs": 1752374363392.811523 },
{"activeModifiers":[],"character":"Space", "isARepeat":false,"processTimeMs": 137582312.0,"type":"keyUp", "unixTimeMs": 1752374363489.030029 },
{"activeModifiers":[],"character":"M", "isARepeat":false,"processTimeMs": 137582734.0,"type":"keyDown", "unixTimeMs": 1752374363911.782715 },
{"activeModifiers":[],"character":"M", "isARepeat":false,"processTimeMs": 137582843.0,"type":"keyUp", "unixTimeMs": 1752374364015.046387 },
{"activeModifiers":[],"character":"C", "isARepeat":false,"processTimeMs": 137582906.0,"type":"keyDown", "unixTimeMs": 1752374364076.087158 },
{"activeModifiers":[],"character":"C", "isARepeat":false,"processTimeMs": 137582984.0,"type":"keyUp", "unixTimeMs": 1752374364151.036621 },
{"activeModifiers":[],"character":"P", "isARepeat":false,"processTimeMs": 137583093.0,"type":"keyDown", "unixTimeMs": 1752374364264.777100 },
{"activeModifiers":[],"character":"P", "isARepeat":false,"processTimeMs": 137583187.0,"type":"keyUp", "unixTimeMs": 1752374364353.476318 },
{"activeModifiers":[],"character":"Enter", "isARepeat":false,"processTimeMs": 137583343.0,"type":"keyDown", "unixTimeMs": 1752374364518.967285 },
{"activeModifiers":[],"character":"Enter", "isARepeat":false,"processTimeMs": 137583406.0,"type":"keyUp", "unixTimeMs": 1752374364582.128662 },
{"activeModifiers":[],"character":"P", "isARepeat":false,"processTimeMs": 137583656.0,"type":"keyDown", "unixTimeMs": 1752374364826.170898 },
{"activeModifiers":[],"character":"P", "isARepeat":false,"processTimeMs": 137583750.0,"type":"keyUp", "unixTimeMs": 1752374364915.903320 },
{"activeModifiers":[],"character":"E", "isARepeat":false,"processTimeMs": 137583796.0,"type":"keyDown", "unixTimeMs": 1752374364967.356201 },
{"activeModifiers":[],"character":"E", "isARepeat":false,"processTimeMs": 137583875.0,"type":"keyUp", "unixTimeMs": 1752374365038.903076 },
{"activeModifiers":[],"character":"I", "isARepeat":false,"processTimeMs": 137583890.0,"type":"keyDown", "unixTimeMs": 1752374365066.922852 },
{"activeModifiers":[],"character":"I", "isARepeat":false,"processTimeMs": 137583968.0,"type":"keyUp", "unixTimeMs": 1752374365141.462402 },
{"activeModifiers":[],"character":"Z", "isARepeat":false,"processTimeMs": 137584015.0,"type":"keyDown", "unixTimeMs": 1752374365192.108398 },
{"activeModifiers":[],"character":"Z", "isARepeat":false,"processTimeMs": 137584093.0,"type":"keyUp", "unixTimeMs": 1752374365264.865723 },
{"activeModifiers":[],"character":"H", "isARepeat":false,"processTimeMs": 137584109.0,"type":"keyDown", "unixTimeMs": 1752374365273.636719 },
{"activeModifiers":[],"character":"H", "isARepeat":false,"processTimeMs": 137584171.0,"type":"keyUp", "unixTimeMs": 1752374365341.285645 },
{"activeModifiers":[],"character":"I", "isARepeat":false,"processTimeMs": 137584234.0,"type":"keyDown", "unixTimeMs": 1752374365410.077637 },
{"activeModifiers":[],"character":"Space", "isARepeat":false,"processTimeMs": 137584312.0,"type":"keyDown", "unixTimeMs": 1752374365485.915527 },
{"activeModifiers":[],"character":"I", "isARepeat":false,"processTimeMs": 137584328.0,"type":"keyUp", "unixTimeMs": 1752374365500.281982 },
{"activeModifiers":[],"character":"Space", "isARepeat":false,"processTimeMs": 137584406.0,"type":"keyUp", "unixTimeMs": 1752374365572.909912 },
{"activeModifiers":[],"character":",", "isARepeat":false,"processTimeMs": 137585062.0,"type":"keyDown", "unixTimeMs": 1752374366228.068359 },
{"activeModifiers":[],"character":",", "isARepeat":false,"processTimeMs": 137585109.0,"type":"keyUp", "unixTimeMs": 1752374366278.540039 },
{"activeModifiers":[],"character":"M", "isARepeat":false,"processTimeMs": 137586609.0,"type":"keyDown", "unixTimeMs": 1752374367786.290283 },
{"activeModifiers":[],"character":"M", "isARepeat":false,"processTimeMs": 137586703.0,"type":"keyUp", "unixTimeMs": 1752374367867.338379 },
{"activeModifiers":[],"character":"C", "isARepeat":false,"processTimeMs": 137586765.0,"type":"keyDown", "unixTimeMs": 1752374367939.744385 },
{"activeModifiers":[],"character":"C", "isARepeat":false,"processTimeMs": 137586859.0,"type":"keyUp", "unixTimeMs": 1752374368036.917969 },
{"activeModifiers":[],"character":"P", "isARepeat":false,"processTimeMs": 137586984.0,"type":"keyDown", "unixTimeMs": 1752374368154.656494 },
{"activeModifiers":[],"character":"P", "isARepeat":false,"processTimeMs": 137587062.0,"type":"keyUp", "unixTimeMs": 1752374368228.637939 },
{"activeModifiers":[],"character":"Enter", "isARepeat":false,"processTimeMs": 137587250.0,"type":"keyDown", "unixTimeMs": 1752374368426.134521 },
{"activeModifiers":[],"character":"Enter", "isARepeat":false,"processTimeMs": 137587343.0,"type":"keyUp", "unixTimeMs": 1752374368510.853027 },
{"activeModifiers":[],"character":"P", "isARepeat":false,"processTimeMs": 137587812.0,"type":"keyDown", "unixTimeMs": 1752374368979.082764 },
{"activeModifiers":[],"character":"P", "isARepeat":false,"processTimeMs": 137587906.0,"type":"keyUp", "unixTimeMs": 1752374369073.258057 },
{"activeModifiers":[],"character":"E", "isARepeat":false,"processTimeMs": 137588281.0,"type":"keyDown", "unixTimeMs": 1752374369453.508789 },
{"activeModifiers":[],"character":"R", "isARepeat":false,"processTimeMs": 137588375.0,"type":"keyDown", "unixTimeMs": 1752374369545.969238 },
{"activeModifiers":[],"character":"E", "isARepeat":false,"processTimeMs": 137588453.0,"type":"keyUp", "unixTimeMs": 1752374369617.677246 },
{"activeModifiers":[],"character":"I", "isARepeat":false,"processTimeMs": 137588468.0,"type":"keyDown", "unixTimeMs": 1752374369638.540771 },
{"activeModifiers":[],"character":"R", "isARepeat":false,"processTimeMs": 137588500.0,"type":"keyUp", "unixTimeMs": 1752374369664.148438 },
{"activeModifiers":[],"character":"I", "isARepeat":false,"processTimeMs": 137588546.0,"type":"keyUp", "unixTimeMs": 1752374369716.916748 },
{"activeModifiers":[],"character":"Z", "isARepeat":false,"processTimeMs": 137588640.0,"type":"keyDown", "unixTimeMs": 1752374369806.112549 },
{"activeModifiers":[],"character":"H", "isARepeat":false,"processTimeMs": 137588687.0,"type":"keyDown", "unixTimeMs": 1752374369834.384521 },
{"activeModifiers":[],"character":"Z", "isARepeat":false,"processTimeMs": 137588718.0,"type":"keyUp", "unixTimeMs": 1752374369884.966064 },
{"activeModifiers":[],"character":"H", "isARepeat":false,"processTimeMs": 137588750.0,"type":"keyUp", "unixTimeMs": 1752374369922.475342 },
{"activeModifiers":[],"character":"I", "isARepeat":false,"processTimeMs": 137588843.0,"type":"keyDown", "unixTimeMs": 1752374370016.343262 },
{"activeModifiers":[],"character":"I", "isARepeat":false,"processTimeMs": 137588937.0,"type":"keyUp", "unixTimeMs": 1752374370110.080078 },
{"activeModifiers":[],"character":"Space", "isARepeat":false,"processTimeMs": 137588953.0,"type":"keyDown", "unixTimeMs": 1752374370123.651855 },
{"activeModifiers":[],"character":"Space", "isARepeat":false,"processTimeMs": 137589046.0,"type":"keyUp", "unixTimeMs": 1752374370217.865967 },
{"activeModifiers":[],"character":"W", "isARepeat":false,"processTimeMs": 137589250.0,"type":"keyDown", "unixTimeMs": 1752374370421.263184 },
{"activeModifiers":[],"character":"E", "isARepeat":false,"processTimeMs": 137589312.0,"type":"keyDown", "unixTimeMs": 1752374370488.543701 },
{"activeModifiers":[],"character":"W", "isARepeat":false,"processTimeMs": 137589375.0,"type":"keyUp", "unixTimeMs": 1752374370537.310059 },
{"activeModifiers":[],"character":"N", "isARepeat":false,"processTimeMs": 137589406.0,"type":"keyDown", "unixTimeMs": 1752374370572.520752 },
{"activeModifiers":[],"character":"E", "isARepeat":false,"processTimeMs": 137589421.0,"type":"keyUp", "unixTimeMs": 1752374370594.151367 },
{"activeModifiers":[],"character":"N", "isARepeat":false,"processTimeMs": 137589484.0,"type":"keyUp", "unixTimeMs": 1752374370656.089111 },
{"activeModifiers":[],"character":"J", "isARepeat":false,"processTimeMs": 137589593.0,"type":"keyDown", "unixTimeMs": 1752374370757.427490 },
{"activeModifiers":[],"character":"J", "isARepeat":false,"processTimeMs": 137589656.0,"type":"keyUp", "unixTimeMs": 1752374370819.967773 },
{"activeModifiers":[],"character":"I", "isARepeat":false,"processTimeMs": 137589718.0,"type":"keyDown", "unixTimeMs": 1752374370886.009521 },
{"activeModifiers":[],"character":"A", "isARepeat":false,"processTimeMs": 137589765.0,"type":"keyDown", "unixTimeMs": 1752374370930.379883 },
{"activeModifiers":[],"character":"I", "isARepeat":false,"processTimeMs": 137589781.0,"type":"keyUp", "unixTimeMs": 1752374370957.859619 },
{"activeModifiers":[],"character":"A", "isARepeat":false,"processTimeMs": 137589859.0,"type":"keyUp", "unixTimeMs": 1752374371033.230713 },
{"activeModifiers":[],"character":"N", "isARepeat":false,"processTimeMs": 137589890.0,"type":"keyDown", "unixTimeMs": 1752374371057.809570 },
{"activeModifiers":[],"character":"Space", "isARepeat":false,"processTimeMs": 137589921.0,"type":"keyDown", "unixTimeMs": 1752374371087.676025 },
{"activeModifiers":[],"character":"N", "isARepeat":false,"processTimeMs": 137589968.0,"type":"keyUp", "unixTimeMs": 1752374371140.515137 },
{"activeModifiers":[],"character":"D", "isARepeat":false,"processTimeMs": 137589984.0,"type":"keyDown", "unixTimeMs": 1752374371150.462402 },
{"activeModifiers":[],"character":"Space", "isARepeat":false,"processTimeMs": 137590000.0,"type":"keyUp", "unixTimeMs": 1752374371176.736084 },
{"activeModifiers":[],"character":"E", "isARepeat":false,"processTimeMs": 137590031.0,"type":"keyDown", "unixTimeMs": 1752374371207.942383 },
{"activeModifiers":[],"character":"D", "isARepeat":false,"processTimeMs": 137590093.0,"type":"keyUp", "unixTimeMs": 1752374371269.236084 },
{"activeModifiers":[],"character":"Space", "isARepeat":false,"processTimeMs": 137590125.0,"type":"keyDown", "unixTimeMs": 1752374371298.301514 },
{"activeModifiers":[],"character":"E", "isARepeat":false,"processTimeMs": 137590171.0,"type":"keyUp", "unixTimeMs": 1752374371338.164062 },
{"activeModifiers":[],"character":"Space", "isARepeat":false,"processTimeMs": 137590234.0,"type":"keyUp", "unixTimeMs": 1752374371409.621582 },
{"activeModifiers":[],"character":"W", "isARepeat":false,"processTimeMs": 137590531.0,"type":"keyDown", "unixTimeMs": 1752374371697.659668 },
{"activeModifiers":[],"character":"E", "isARepeat":false,"processTimeMs": 137590593.0,"type":"keyDown", "unixTimeMs": 1752374371765.530762 },
{"activeModifiers":[],"character":"W", "isARepeat":false,"processTimeMs": 137590640.0,"type":"keyUp", "unixTimeMs": 1752374371807.160889 },
{"activeModifiers":[],"character":"I", "isARepeat":false,"processTimeMs": 137590671.0,"type":"keyDown", "unixTimeMs": 1752374371834.197510 },
{"activeModifiers":[],"character":"E", "isARepeat":false,"processTimeMs": 137590718.0,"type":"keyUp", "unixTimeMs": 1752374371886.595703 },
{"activeModifiers":[],"character":"I", "isARepeat":false,"processTimeMs": 137590750.0,"type":"keyUp", "unixTimeMs": 1752374371919.290771 },
{"activeModifiers":[],"character":"Z", "isARepeat":false,"processTimeMs": 137590859.0,"type":"keyDown", "unixTimeMs": 1752374372025.179932 },
{"activeModifiers":[],"character":"H", "isARepeat":false,"processTimeMs": 137590906.0,"type":"keyDown", "unixTimeMs": 1752374372075.543213 },
{"activeModifiers":[],"character":"Z", "isARepeat":false,"processTimeMs": 137590937.0,"type":"keyUp", "unixTimeMs": 1752374372101.176514 },
{"activeModifiers":[],"character":"H", "isARepeat":false,"processTimeMs": 137590984.0,"type":"keyUp", "unixTimeMs": 1752374372159.091553 },
{"activeModifiers":[],"character":"I", "isARepeat":false,"processTimeMs": 137591093.0,"type":"keyDown", "unixTimeMs": 1752374372259.765137 },
{"activeModifiers":[],"character":"I", "isARepeat":false,"processTimeMs": 137591187.0,"type":"keyUp", "unixTimeMs": 1752374372353.436035 },
{"activeModifiers":[],"character":"Space", "isARepeat":false,"processTimeMs": 137591187.0,"type":"keyDown", "unixTimeMs": 1752374372363.605713 },
{"activeModifiers":[],"character":"Space", "isARepeat":false,"processTimeMs": 137591296.0,"type":"keyUp", "unixTimeMs": 1752374372474.219971 },
{"activeModifiers":[],"character":"D", "isARepeat":false,"processTimeMs": 137591500.0,"type":"keyDown", "unixTimeMs": 1752374372667.587402 },
{"activeModifiers":[],"character":"D", "isARepeat":false,"processTimeMs": 137591593.0,"type":"keyUp", "unixTimeMs": 1752374372765.190918 },
{"activeModifiers":[],"character":"Backspace", "isARepeat":false,"processTimeMs": 137591890.0,"type":"keyDown", "unixTimeMs": 1752374373058.791504 },
{"activeModifiers":[],"character":"Backspace", "isARepeat":false,"processTimeMs": 137591968.0,"type":"keyUp", "unixTimeMs": 1752374373131.821289 },
{"activeModifiers":[],"character":"Z", "isARepeat":false,"processTimeMs": 137591984.0,"type":"keyDown", "unixTimeMs": 1752374373159.958496 },
{"activeModifiers":[],"character":"A", "isARepeat":false,"processTimeMs": 137592125.0,"type":"keyDown", "unixTimeMs": 1752374373299.763916 },
{"activeModifiers":[],"character":"Z", "isARepeat":false,"processTimeMs": 137592140.0,"type":"keyUp", "unixTimeMs": 1752374373316.314453 },
{"activeModifiers":[],"character":"I", "isARepeat":false,"processTimeMs": 137592187.0,"type":"keyDown", "unixTimeMs": 1752374373357.861084 },
{"activeModifiers":[],"character":"A", "isARepeat":false,"processTimeMs": 137592218.0,"type":"keyUp", "unixTimeMs": 1752374373393.704346 },
{"activeModifiers":[],"character":"I", "isARepeat":false,"processTimeMs": 137592265.0,"type":"keyUp", "unixTimeMs": 1752374373430.974609 },
{"activeModifiers":[],"character":"Space", "isARepeat":false,"processTimeMs": 137592312.0,"type":"keyDown", "unixTimeMs": 1752374373481.140137 },
{"activeModifiers":[],"character":"Space", "isARepeat":false,"processTimeMs": 137592406.0,"type":"keyUp", "unixTimeMs": 1752374373578.621582 },
{"activeModifiers":["shift"],"character":"@", "isARepeat":false,"processTimeMs": 137593359.0,"type":"keyDown", "unixTimeMs": 1752374374530.279297 },
{"activeModifiers":["shift"],"character":"@", "isARepeat":false,"processTimeMs": 137593484.0,"type":"keyUp", "unixTimeMs": 1752374374658.943115 },
{"activeModifiers":["shift"],"character":"~", "isARepeat":false,"processTimeMs": 137595187.0,"type":"keyDown", "unixTimeMs": 1752374376358.696045 },
{"activeModifiers":["shift"],"character":"~", "isARepeat":false,"processTimeMs": 137595296.0,"type":"keyUp", "unixTimeMs": 1752374376463.863281 },
{"activeModifiers":[],"character":"\\", "isARepeat":false,"processTimeMs": 137596890.0,"type":"keyDown", "unixTimeMs": 1752374378062.305664 },
{"activeModifiers":[],"character":"\\", "isARepeat":false,"processTimeMs": 137596953.0,"type":"keyUp", "unixTimeMs": 1752374378122.599121 },
{"activeModifiers":[],"character":"Backspace", "isARepeat":false,"processTimeMs": 137597578.0,"type":"keyDown", "unixTimeMs": 1752374378741.420898 },
{"activeModifiers":[],"character":"Backspace", "isARepeat":false,"processTimeMs": 137597656.0,"type":"keyUp", "unixTimeMs": 1752374378833.753662 },
{"activeModifiers":[],"character":"\\", "isARepeat":false,"processTimeMs": 137597953.0,"type":"keyDown", "unixTimeMs": 1752374379120.424316 },
{"activeModifiers":[],"character":"\\", "isARepeat":false,"processTimeMs": 137598015.0,"type":"keyUp", "unixTimeMs": 1752374379189.500244 },
{"activeModifiers":[],"character":".", "isARepeat":false,"processTimeMs": 137598296.0,"type":"keyDown", "unixTimeMs": 1752374379467.586914 },
{"activeModifiers":[],"character":".", "isARepeat":false,"processTimeMs": 137598359.0,"type":"keyUp", "unixTimeMs": 1752374379524.716309 },
{"activeModifiers":[],"character":"G", "isARepeat":false,"processTimeMs": 137599734.0,"type":"keyDown", "unixTimeMs": 1752374380902.500000 },
{"activeModifiers":[],"character":"G", "isARepeat":false,"processTimeMs": 137599828.0,"type":"keyUp", "unixTimeMs": 1752374380992.593506 },
{"activeModifiers":[],"character":"E", "isARepeat":false,"processTimeMs": 137599828.0,"type":"keyDown", "unixTimeMs": 1752374381000.861572 },
{"activeModifiers":[],"character":"E", "isARepeat":false,"processTimeMs": 137599890.0,"type":"keyUp", "unixTimeMs": 1752374381059.045166 },
{"activeModifiers":[],"character":"M", "isARepeat":false,"processTimeMs": 137600406.0,"type":"keyDown", "unixTimeMs": 1752374381572.656738 },
{"activeModifiers":[],"character":"I", "isARepeat":false,"processTimeMs": 137600531.0,"type":"keyDown", "unixTimeMs": 1752374381702.603271 },
{"activeModifiers":[],"character":"M", "isARepeat":false,"processTimeMs": 137600562.0,"type":"keyUp", "unixTimeMs": 1752374381732.323242 },
{"activeModifiers":[],"character":"I", "isARepeat":false,"processTimeMs": 137600640.0,"type":"keyUp", "unixTimeMs": 1752374381804.258057 },
{"activeModifiers":[],"character":"N", "isARepeat":false,"processTimeMs": 137600703.0,"type":"keyDown", "unixTimeMs": 1752374381867.209961 },
{"activeModifiers":[],"character":"I", "isARepeat":false,"processTimeMs": 137600796.0,"type":"keyDown", "unixTimeMs": 1752374381959.168701 },
{"activeModifiers":[],"character":"N", "isARepeat":false,"processTimeMs": 137600828.0,"type":"keyUp", "unixTimeMs": 1752374381999.271240 },
{"activeModifiers":[],"character":"I", "isARepeat":false,"processTimeMs": 137600906.0,"type":"keyUp", "unixTimeMs": 1752374382069.044434 },
{"activeModifiers":[],"character":"\\", "isARepeat":false,"processTimeMs": 137601781.0,"type":"keyDown", "unixTimeMs": 1752374382948.014893 },
{"activeModifiers":[],"character":"\\", "isARepeat":false,"processTimeMs": 137601843.0,"type":"keyUp", "unixTimeMs": 1752374383016.895508 },
{"activeModifiers":[],"character":"S", "isARepeat":false,"processTimeMs": 137602546.0,"type":"keyDown", "unixTimeMs": 1752374383717.325684 },
{"activeModifiers":[],"character":"S", "isARepeat":false,"processTimeMs": 137602671.0,"type":"keyUp", "unixTimeMs": 1752374383835.991211 },
{"activeModifiers":[],"character":"E", "isARepeat":false,"processTimeMs": 137602765.0,"type":"keyDown", "unixTimeMs": 1752374383941.691650 },
{"activeModifiers":[],"character":"E", "isARepeat":false,"processTimeMs": 137602859.0,"type":"keyUp", "unixTimeMs": 1752374384029.302734 },
{"activeModifiers":[],"character":"T", "isARepeat":false,"processTimeMs": 137603046.0,"type":"keyDown", "unixTimeMs": 1752374384219.949463 },
{"activeModifiers":[],"character":"T", "isARepeat":false,"processTimeMs": 137603125.0,"type":"keyUp", "unixTimeMs": 1752374384295.416016 },
{"activeModifiers":[],"character":"T", "isARepeat":false,"processTimeMs": 137603203.0,"type":"keyDown", "unixTimeMs": 1752374384349.502441 },
{"activeModifiers":[],"character":"T", "isARepeat":false,"processTimeMs": 137603281.0,"type":"keyUp", "unixTimeMs": 1752374384457.310059 },
{"activeModifiers":[],"character":"I", "isARepeat":false,"processTimeMs": 137603515.0,"type":"keyDown", "unixTimeMs": 1752374384690.679688 },
{"activeModifiers":[],"character":"N", "isARepeat":false,"processTimeMs": 137603671.0,"type":"keyDown", "unixTimeMs": 1752374384834.391113 },
{"activeModifiers":[],"character":"I", "isARepeat":false,"processTimeMs": 137603703.0,"type":"keyUp", "unixTimeMs": 1752374384875.753174 },
{"activeModifiers":[],"character":"G", "isARepeat":false,"processTimeMs": 137603750.0,"type":"keyDown", "unixTimeMs": 1752374384917.979248 },
{"activeModifiers":[],"character":"N", "isARepeat":false,"processTimeMs": 137603812.0,"type":"keyUp", "unixTimeMs": 1752374384982.560791 },
{"activeModifiers":[],"character":"G", "isARepeat":false,"processTimeMs": 137603859.0,"type":"keyUp", "unixTimeMs": 1752374385027.349121 },
{"activeModifiers":[],"character":",", "isARepeat":false,"processTimeMs": 137604500.0,"type":"keyDown", "unixTimeMs": 1752374385675.284912 },
{"activeModifiers":[],"character":",", "isARepeat":false,"processTimeMs": 137604562.0,"type":"keyUp", "unixTimeMs": 1752374385726.987793 },
{"activeModifiers":[],"character":"J", "isARepeat":false,"processTimeMs": 137604937.0,"type":"keyDown", "unixTimeMs": 1752374386105.085693 },
{"activeModifiers":[],"character":"J", "isARepeat":false,"processTimeMs": 137605000.0,"type":"keyUp", "unixTimeMs": 1752374386168.136475 },
{"activeModifiers":[],"character":"S", "isARepeat":false,"processTimeMs": 137605031.0,"type":"keyDown", "unixTimeMs": 1752374386199.959717 },
{"activeModifiers":[],"character":"S", "isARepeat":false,"processTimeMs": 137605125.0,"type":"keyUp", "unixTimeMs": 1752374386295.785156 },
{"activeModifiers":[],"character":"Backspace", "isARepeat":false,"processTimeMs": 137605406.0,"type":"keyDown", "unixTimeMs": 1752374386580.372803 },
{"activeModifiers":[],"character":"Backspace", "isARepeat":false,"processTimeMs": 137605484.0,"type":"keyUp", "unixTimeMs": 1752374386659.332031 },
{"activeModifiers":[],"character":"Backspace", "isARepeat":false,"processTimeMs": 137605578.0,"type":"keyDown", "unixTimeMs": 1752374386755.627197 },
{"activeModifiers":[],"character":"Backspace", "isARepeat":false,"processTimeMs": 137605656.0,"type":"keyUp", "unixTimeMs": 1752374386830.617676 },
{"activeModifiers":[],"character":"Backspace", "isARepeat":false,"processTimeMs": 137605937.0,"type":"keyDown", "unixTimeMs": 1752374387111.093506 },
{"activeModifiers":[],"character":"Backspace", "isARepeat":false,"processTimeMs": 137606046.0,"type":"keyUp", "unixTimeMs": 1752374387210.256104 },
{"activeModifiers":[],"character":".", "isARepeat":false,"processTimeMs": 137606343.0,"type":"keyDown", "unixTimeMs": 1752374387508.631348 },
{"activeModifiers":[],"character":".", "isARepeat":false,"processTimeMs": 137606390.0,"type":"keyUp", "unixTimeMs": 1752374387557.444824 },
{"activeModifiers":[],"character":"J", "isARepeat":false,"processTimeMs": 137606734.0,"type":"keyDown", "unixTimeMs": 1752374387903.604248 },
{"activeModifiers":[],"character":"J", "isARepeat":false,"processTimeMs": 137606812.0,"type":"keyUp", "unixTimeMs": 1752374387987.693604 },
{"activeModifiers":[],"character":"S", "isARepeat":false,"processTimeMs": 137606859.0,"type":"keyDown", "unixTimeMs": 1752374388022.401123 },
{"activeModifiers":[],"character":"S", "isARepeat":false,"processTimeMs": 137606937.0,"type":"keyUp", "unixTimeMs": 1752374388112.711182 },
{"activeModifiers":[],"character":"O", "isARepeat":false,"processTimeMs": 137606968.0,"type":"keyDown", "unixTimeMs": 1752374388134.680420 },
{"activeModifiers":[],"character":"N", "isARepeat":false,"processTimeMs": 137607062.0,"type":"keyDown", "unixTimeMs": 1752374388208.801270 },
{"activeModifiers":[],"character":"O", "isARepeat":false,"processTimeMs": 137607171.0,"type":"keyUp", "unixTimeMs": 1752374388339.921631 },
{"activeModifiers":[],"character":"N", "isARepeat":false,"processTimeMs": 137607187.0,"type":"keyUp", "unixTimeMs": 1752374388358.963623 },
{"activeModifiers":[],"character":"Space", "isARepeat":false,"processTimeMs": 137615781.0,"type":"keyDown", "unixTimeMs": 1752374396948.064209 },
{"activeModifiers":[],"character":"Space", "isARepeat":false,"processTimeMs": 137615875.0,"type":"keyUp", "unixTimeMs": 1752374397048.498779 },
{"activeModifiers":[],"character":"Backspace", "isARepeat":false,"processTimeMs": 137616531.0,"type":"keyDown", "unixTimeMs": 1752374397699.516357 },
{"activeModifiers":[],"character":"Backspace", "isARepeat":false,"processTimeMs": 137616593.0,"type":"keyUp", "unixTimeMs": 1752374397769.073975 },
{"activeModifiers":[],"character":".", "isARepeat":false,"processTimeMs": 137617015.0,"type":"keyDown", "unixTimeMs": 1752374398189.217285 },
{"activeModifiers":[],"character":".", "isARepeat":false,"processTimeMs": 137617093.0,"type":"keyUp", "unixTimeMs": 1752374398265.783691 },
{"activeModifiers":[],"character":"Backspace", "isARepeat":false,"processTimeMs": 137617921.0,"type":"keyDown", "unixTimeMs": 1752374399099.007080 },
{"activeModifiers":[],"character":"Backspace", "isARepeat":false,"processTimeMs": 137618015.0,"type":"keyUp", "unixTimeMs": 1752374399183.574463 },
{"activeModifiers":[],"character":".", "isARepeat":false,"processTimeMs": 137618468.0,"type":"keyDown", "unixTimeMs": 1752374399640.712891 },
{"activeModifiers":[],"character":".", "isARepeat":false,"processTimeMs": 137618515.0,"type":"keyUp", "unixTimeMs": 1752374399683.727295 },
{"activeModifiers":[],"character":"P", "isARepeat":false,"processTimeMs": 137619515.0,"type":"keyDown", "unixTimeMs": 1752374400686.851807 },
{"activeModifiers":[],"character":"P", "isARepeat":false,"processTimeMs": 137619562.0,"type":"keyUp", "unixTimeMs": 1752374400738.659912 },
{"activeModifiers":[],"character":"E", "isARepeat":false,"processTimeMs": 137619625.0,"type":"keyDown", "unixTimeMs": 1752374400787.077148 },
{"activeModifiers":[],"character":"E", "isARepeat":false,"processTimeMs": 137619687.0,"type":"keyUp", "unixTimeMs": 1752374400860.729248 },
{"activeModifiers":[],"character":"I", "isARepeat":false,"processTimeMs": 137619734.0,"type":"keyDown", "unixTimeMs": 1752374400903.099609 },
{"activeModifiers":[],"character":"I", "isARepeat":false,"processTimeMs": 137619796.0,"type":"keyUp", "unixTimeMs": 1752374400968.050293 },
{"activeModifiers":[],"character":"Z", "isARepeat":false,"processTimeMs": 137619828.0,"type":"keyDown", "unixTimeMs": 1752374400998.303223 },
{"activeModifiers":[],"character":"Z", "isARepeat":false,"processTimeMs": 137619937.0,"type":"keyUp", "unixTimeMs": 1752374401103.926025 },
{"activeModifiers":[],"character":"H", "isARepeat":false,"processTimeMs": 137620000.0,"type":"keyDown", "unixTimeMs": 1752374401164.991699 },
{"activeModifiers":[],"character":"H", "isARepeat":false,"processTimeMs": 137620078.0,"type":"keyUp", "unixTimeMs": 1752374401240.211914 },
{"activeModifiers":[],"character":"Space", "isARepeat":false,"processTimeMs": 137620437.0,"type":"keyDown", "unixTimeMs": 1752374401600.617676 },
{"activeModifiers":[],"character":"Space", "isARepeat":false,"processTimeMs": 137620515.0,"type":"keyUp", "unixTimeMs": 1752374401691.841797 },
{"activeModifiers":[],"character":"Backspace", "isARepeat":false,"processTimeMs": 137620796.0,"type":"keyDown", "unixTimeMs": 1752374401971.150879 },
{"activeModifiers":[],"character":"Backspace", "isARepeat":false,"processTimeMs": 137620906.0,"type":"keyUp", "unixTimeMs": 1752374402070.582031 },
{"activeModifiers":[],"character":"Backspace", "isARepeat":false,"processTimeMs": 137621015.0,"type":"keyDown", "unixTimeMs": 1752374402188.138916 },
{"activeModifiers":[],"character":"Backspace", "isARepeat":false,"processTimeMs": 137621093.0,"type":"keyUp", "unixTimeMs": 1752374402269.306641 },
{"activeModifiers":[],"character":"M", "isARepeat":false,"processTimeMs": 137622718.0,"type":"keyDown", "unixTimeMs": 1752374403888.103516 },
{"activeModifiers":[],"character":"M", "isARepeat":false,"processTimeMs": 137622812.0,"type":"keyUp", "unixTimeMs": 1752374403983.517090 },
{"activeModifiers":[],"character":"C", "isARepeat":false,"processTimeMs": 137622875.0,"type":"keyDown", "unixTimeMs": 1752374404049.676758 },
{"activeModifiers":[],"character":"C", "isARepeat":false,"processTimeMs": 137622984.0,"type":"keyUp", "unixTimeMs": 1752374404161.568359 },
{"activeModifiers":[],"character":"P", "isARepeat":false,"processTimeMs": 137623140.0,"type":"keyDown", "unixTimeMs": 1752374404307.049561 },
{"activeModifiers":[],"character":"P", "isARepeat":false,"processTimeMs": 137623234.0,"type":"keyUp", "unixTimeMs": 1752374404398.269287 },
{"activeModifiers":[],"character":"Enter", "isARepeat":false,"processTimeMs": 137623562.0,"type":"keyDown", "unixTimeMs": 1752374404728.394775 },
{"activeModifiers":[],"character":"Enter", "isARepeat":false,"processTimeMs": 137623671.0,"type":"keyUp", "unixTimeMs": 1752374404837.032715 },
{"activeModifiers":[],"character":"W", "isARepeat":false,"processTimeMs": 137623953.0,"type":"keyDown", "unixTimeMs": 1752374405121.639893 },
{"activeModifiers":[],"character":"E", "isARepeat":false,"processTimeMs": 137624015.0,"type":"keyDown", "unixTimeMs": 1752374405180.967285 },
{"activeModifiers":[],"character":"W", "isARepeat":false,"processTimeMs": 137624078.0,"type":"keyUp", "unixTimeMs": 1752374405246.075928 },
{"activeModifiers":[],"character":"E", "isARepeat":false,"processTimeMs": 137624140.0,"type":"keyUp", "unixTimeMs": 1752374405305.135498 },
{"activeModifiers":[],"character":"Space", "isARepeat":false,"processTimeMs": 137624187.0,"type":"keyDown", "unixTimeMs": 1752374405355.402100 },
{"activeModifiers":[],"character":"Space", "isARepeat":false,"processTimeMs": 137624328.0,"type":"keyUp", "unixTimeMs": 1752374405499.408203 },
{"activeModifiers":[],"character":"P", "isARepeat":false,"processTimeMs": 137624687.0,"type":"keyDown", "unixTimeMs": 1752374405851.570068 },
{"activeModifiers":[],"character":"P", "isARepeat":false,"processTimeMs": 137624750.0,"type":"keyUp", "unixTimeMs": 1752374405922.187500 },
{"activeModifiers":[],"character":"E", "isARepeat":false,"processTimeMs": 137624812.0,"type":"keyDown", "unixTimeMs": 1752374405975.483643 },
{"activeModifiers":[],"character":"E", "isARepeat":false,"processTimeMs": 137624890.0,"type":"keyUp", "unixTimeMs": 1752374406061.484375 },
{"activeModifiers":[],"character":"I", "isARepeat":false,"processTimeMs": 137624937.0,"type":"keyDown", "unixTimeMs": 1752374406102.370605 },
{"activeModifiers":[],"character":"I", "isARepeat":false,"processTimeMs": 137625015.0,"type":"keyUp", "unixTimeMs": 1752374406187.673584 },
{"activeModifiers":[],"character":"Z", "isARepeat":false,"processTimeMs": 137625046.0,"type":"keyDown", "unixTimeMs": 1752374406224.247314 },
{"activeModifiers":[],"character":"Z", "isARepeat":false,"processTimeMs": 137625125.0,"type":"keyUp", "unixTimeMs": 1752374406299.540039 },
{"activeModifiers":[],"character":"H", "isARepeat":false,"processTimeMs": 137625140.0,"type":"keyDown", "unixTimeMs": 1752374406313.918457 },
{"activeModifiers":[],"character":"H", "isARepeat":false,"processTimeMs": 137625218.0,"type":"keyUp", "unixTimeMs": 1752374406390.338623 },
{"activeModifiers":[],"character":"Space", "isARepeat":false,"processTimeMs": 137625375.0,"type":"keyDown", "unixTimeMs": 1752374406541.379395 },
{"activeModifiers":[],"character":"Space", "isARepeat":false,"processTimeMs": 137625453.0,"type":"keyUp", "unixTimeMs": 1752374406629.196045 },
{"activeModifiers":[],"character":"Backspace", "isARepeat":false,"processTimeMs": 137625515.0,"type":"keyDown", "unixTimeMs": 1752374406681.635498 },
{"activeModifiers":[],"character":"Backspace", "isARepeat":false,"processTimeMs": 137625578.0,"type":"keyUp", "unixTimeMs": 1752374406750.999512 },
{"activeModifiers":[],"character":"Backspace", "isARepeat":false,"processTimeMs": 137625687.0,"type":"keyDown", "unixTimeMs": 1752374406859.562500 },
{"activeModifiers":[],"character":"Backspace", "isARepeat":false,"processTimeMs": 137625765.0,"type":"keyUp", "unixTimeMs": 1752374406928.466553 },
{"activeModifiers":[],"character":"Backspace", "isARepeat":false,"processTimeMs": 137625843.0,"type":"keyDown", "unixTimeMs": 1752374407016.746826 },
{"activeModifiers":[],"character":"Backspace", "isARepeat":false,"processTimeMs": 137625921.0,"type":"keyUp", "unixTimeMs": 1752374407096.224121 },
{"activeModifiers":[],"character":"D", "isARepeat":false,"processTimeMs": 137626031.0,"type":"keyDown", "unixTimeMs": 1752374407206.768555 },
{"activeModifiers":[],"character":"E", "isARepeat":false,"processTimeMs": 137626078.0,"type":"keyDown", "unixTimeMs": 1752374407253.330078 },
{"activeModifiers":[],"character":"D", "isARepeat":false,"processTimeMs": 137626125.0,"type":"keyUp", "unixTimeMs": 1752374407301.497314 },
{"activeModifiers":[],"character":"Space", "isARepeat":false,"processTimeMs": 137626156.0,"type":"keyDown", "unixTimeMs": 1752374407331.660400 },
{"activeModifiers":[],"character":"E", "isARepeat":false,"processTimeMs": 137626187.0,"type":"keyUp", "unixTimeMs": 1752374407360.928223 },
{"activeModifiers":[],"character":"Space", "isARepeat":false,"processTimeMs": 137626265.0,"type":"keyUp", "unixTimeMs": 1752374407431.573975 },
{"activeModifiers":[],"character":"P", "isARepeat":false,"processTimeMs": 137626734.0,"type":"keyDown", "unixTimeMs": 1752374407896.983887 },
{"activeModifiers":[],"character":"P", "isARepeat":false,"processTimeMs": 137626812.0,"type":"keyUp", "unixTimeMs": 1752374407979.947266 },
{"activeModifiers":[],"character":"E", "isARepeat":false,"processTimeMs": 137626812.0,"type":"keyDown", "unixTimeMs": 1752374407985.617920 },
{"activeModifiers":[],"character":"E", "isARepeat":false,"processTimeMs": 137626890.0,"type":"keyUp", "unixTimeMs": 1752374408056.368164 },
{"activeModifiers":[],"character":"I", "isARepeat":false,"processTimeMs": 137626953.0,"type":"keyDown", "unixTimeMs": 1752374408123.181885 },
{"activeModifiers":[],"character":"Z", "isARepeat":false,"processTimeMs": 137627015.0,"type":"keyDown", "unixTimeMs": 1752374408188.304688 },
{"activeModifiers":[],"character":"I", "isARepeat":false,"processTimeMs": 137627031.0,"type":"keyUp", "unixTimeMs": 1752374408205.527100 },
{"activeModifiers":[],"character":"Z", "isARepeat":false,"processTimeMs": 137627125.0,"type":"keyUp", "unixTimeMs": 1752374408294.654297 },
{"activeModifiers":[],"character":"H", "isARepeat":false,"processTimeMs": 137627171.0,"type":"keyDown", "unixTimeMs": 1752374408343.934082 },
{"activeModifiers":[],"character":"H", "isARepeat":false,"processTimeMs": 137627234.0,"type":"keyUp", "unixTimeMs": 1752374408408.716064 },
{"activeModifiers":[],"character":"I", "isARepeat":false,"processTimeMs": 137627343.0,"type":"keyDown", "unixTimeMs": 1752374408512.248291 },
{"activeModifiers":[],"character":"Space", "isARepeat":false,"processTimeMs": 137627390.0,"type":"keyDown", "unixTimeMs": 1752374408557.635742 },
{"activeModifiers":[],"character":"I", "isARepeat":false,"processTimeMs": 137627406.0,"type":"keyUp", "unixTimeMs": 1752374408570.209961 },
{"activeModifiers":[],"character":"Space", "isARepeat":false,"processTimeMs": 137627484.0,"type":"keyUp", "unixTimeMs": 1752374408657.027588 },
{"activeModifiers":[],"character":"W", "isARepeat":false,"processTimeMs": 137627531.0,"type":"keyDown", "unixTimeMs": 1752374408697.518311 },
{"activeModifiers":[],"character":"E", "isARepeat":false,"processTimeMs": 137627593.0,"type":"keyDown", "unixTimeMs": 1752374408769.056885 },
{"activeModifiers":[],"character":"W", "isARepeat":false,"processTimeMs": 137627656.0,"type":"keyUp", "unixTimeMs": 1752374408819.136719 },
{"activeModifiers":[],"character":"E", "isARepeat":false,"processTimeMs": 137627703.0,"type":"keyUp", "unixTimeMs": 1752374408876.934570 },
{"activeModifiers":[],"character":"I", "isARepeat":false,"processTimeMs": 137627734.0,"type":"keyDown", "unixTimeMs": 1752374408898.547607 },
{"activeModifiers":[],"character":"I", "isARepeat":false,"processTimeMs": 137627812.0,"type":"keyUp", "unixTimeMs": 1752374408978.755615 },
{"activeModifiers":[],"character":"Space", "isARepeat":false,"processTimeMs": 137627828.0,"type":"keyDown", "unixTimeMs": 1752374409001.830811 },
{"activeModifiers":[],"character":"Space", "isARepeat":false,"processTimeMs": 137627921.0,"type":"keyUp", "unixTimeMs": 1752374409088.438721 },
{"activeModifiers":["shift"],"character":":", "isARepeat":false,"processTimeMs": 137628953.0,"type":"keyDown", "unixTimeMs": 1752374410125.814941 },
{"activeModifiers":["shift"],"character":":", "isARepeat":false,"processTimeMs": 137629015.0,"type":"keyUp", "unixTimeMs": 1752374410184.737305 },
{"activeModifiers":["control"],"character":"V", "isARepeat":false,"processTimeMs": 137629421.0,"type":"keyDown", "unixTimeMs": 1752374410592.999512 },
{"activeModifiers":["control"],"character":"V", "isARepeat":false,"processTimeMs": 137629531.0,"type":"keyUp", "unixTimeMs": 1752374410706.601807 },
{"activeModifiers":[],"character":"Backspace", "isARepeat":false,"processTimeMs": 137645375.0,"type":"keyDown", "unixTimeMs": 1752374426547.718506 },
{"activeModifiers":[],"character":"Backspace", "isARepeat":false,"processTimeMs": 137645453.0,"type":"keyUp", "unixTimeMs": 1752374426628.754883 },
{"activeModifiers":["control"],"character":"V", "isARepeat":false,"processTimeMs": 137646031.0,"type":"keyDown", "unixTimeMs": 1752374427194.950684 },
{"activeModifiers":["control"],"character":"V", "isARepeat":false,"processTimeMs": 137646125.0,"type":"keyUp", "unixTimeMs": 1752374427291.151123 },
{"activeModifiers":[],"character":"Enter", "isARepeat":false,"processTimeMs": 137647484.0,"type":"keyDown", "unixTimeMs": 1752374428655.381104 },
{"activeModifiers":[],"character":"Enter", "isARepeat":false,"processTimeMs": 137647546.0,"type":"keyUp", "unixTimeMs": 1752374428723.353516 },
{"activeModifiers":[],"character":"Enter", "isARepeat":false,"processTimeMs": 137670500.0,"type":"keyDown", "unixTimeMs": 1752374451663.630859 },
{"activeModifiers":[],"character":"Enter", "isARepeat":false,"processTimeMs": 137670562.0,"type":"keyUp", "unixTimeMs": 1752374451737.285889 },
{"activeModifiers":[],"character":"G", "isARepeat":false,"processTimeMs": 137695468.0,"type":"keyDown", "unixTimeMs": 1752374476640.648438 },
{"activeModifiers":[],"character":"G", "isARepeat":false,"processTimeMs": 137695546.0,"type":"keyUp", "unixTimeMs": 1752374476720.259277 },
{"activeModifiers":[],"character":"E", "isARepeat":false,"processTimeMs": 137695843.0,"type":"keyDown", "unixTimeMs": 1752374477010.861328 },
{"activeModifiers":[],"character":"E", "isARepeat":false,"processTimeMs": 137695937.0,"type":"keyUp", "unixTimeMs": 1752374477107.357422 },
{"activeModifiers":[],"character":"I", "isARepeat":false,"processTimeMs": 137696109.0,"type":"keyDown", "unixTimeMs": 1752374477280.734863 },
{"activeModifiers":[],"character":"I", "isARepeat":false,"processTimeMs": 137696234.0,"type":"keyUp", "unixTimeMs": 1752374477399.489746 },
{"activeModifiers":[],"character":"M", "isARepeat":false,"processTimeMs": 137696265.0,"type":"keyDown", "unixTimeMs": 1752374477431.330322 },
{"activeModifiers":[],"character":"M", "isARepeat":false,"processTimeMs": 137696375.0,"type":"keyUp", "unixTimeMs": 1752374477541.224365 },
{"activeModifiers":[],"character":"N", "isARepeat":false,"processTimeMs": 137696484.0,"type":"keyDown", "unixTimeMs": 1752374477652.816162 },
{"activeModifiers":[],"character":"I", "isARepeat":false,"processTimeMs": 137696578.0,"type":"keyDown", "unixTimeMs": 1752374477749.606201 },
{"activeModifiers":[],"character":"N", "isARepeat":false,"processTimeMs": 137696625.0,"type":"keyUp", "unixTimeMs": 1752374477797.966064 },
{"activeModifiers":[],"character":"I", "isARepeat":false,"processTimeMs": 137696703.0,"type":"keyUp", "unixTimeMs": 1752374477866.198730 },
{"activeModifiers":[],"character":"Backspace", "isARepeat":false,"processTimeMs": 137697187.0,"type":"keyDown", "unixTimeMs": 1752374478364.954102 },
{"activeModifiers":[],"character":"Backspace", "isARepeat":false,"processTimeMs": 137697250.0,"type":"keyUp", "unixTimeMs": 1752374478426.969238 },
{"activeModifiers":[],"character":"Backspace", "isARepeat":false,"processTimeMs": 137697359.0,"type":"keyDown", "unixTimeMs": 1752374478528.279297 },
{"activeModifiers":[],"character":"Backspace", "isARepeat":false,"processTimeMs": 137697421.0,"type":"keyUp", "unixTimeMs": 1752374478592.670898 },
{"activeModifiers":[],"character":"Backspace", "isARepeat":false,"processTimeMs": 137697515.0,"type":"keyDown", "unixTimeMs": 1752374478690.346436 },
{"activeModifiers":[],"character":"Backspace", "isARepeat":false,"processTimeMs": 137697578.0,"type":"keyUp", "unixTimeMs": 1752374478754.788330 },
{"activeModifiers":[],"character":"Backspace", "isARepeat":false,"processTimeMs": 137697671.0,"type":"keyDown", "unixTimeMs": 1752374478845.751221 },
{"activeModifiers":[],"character":"Backspace", "isARepeat":false,"processTimeMs": 137697765.0,"type":"keyUp", "unixTimeMs": 1752374478929.812988 },
{"activeModifiers":[],"character":"M", "isARepeat":false,"processTimeMs": 137697921.0,"type":"keyDown", "unixTimeMs": 1752374479095.708008 },
{"activeModifiers":[],"character":"M", "isARepeat":false,"processTimeMs": 137698078.0,"type":"keyUp", "unixTimeMs": 1752374479240.642090 },
{"activeModifiers":[],"character":"I", "isARepeat":false,"processTimeMs": 137698078.0,"type":"keyDown", "unixTimeMs": 1752374479252.615234 },
{"activeModifiers":[],"character":"I", "isARepeat":false,"processTimeMs": 137698171.0,"type":"keyUp", "unixTimeMs": 1752374479345.176758 },
{"activeModifiers":[],"character":"N", "isARepeat":false,"processTimeMs": 137698265.0,"type":"keyDown", "unixTimeMs": 1752374479429.802734 },
{"activeModifiers":[],"character":"I", "isARepeat":false,"processTimeMs": 137698328.0,"type":"keyDown", "unixTimeMs": 1752374479496.885986 },
{"activeModifiers":[],"character":"N", "isARepeat":false,"processTimeMs": 137698375.0,"type":"keyUp", "unixTimeMs": 1752374479543.961670 },
{"activeModifiers":[],"character":"I", "isARepeat":false,"processTimeMs": 137698437.0,"type":"keyUp", "unixTimeMs": 1752374479608.523193 },
{"activeModifiers":[],"character":"Enter", "isARepeat":false,"processTimeMs": 137698640.0,"type":"keyDown", "unixTimeMs": 1752374479816.074707 },
{"activeModifiers":[],"character":"Enter", "isARepeat":false,"processTimeMs": 137698703.0,"type":"keyUp", "unixTimeMs": 1752374479867.017090 },
{"activeModifiers":[],"character":"Enter", "isARepeat":false,"processTimeMs": 137698843.0,"type":"keyDown", "unixTimeMs": 1752374480011.742432 },
{"activeModifiers":[],"character":"Enter", "isARepeat":false,"processTimeMs": 137698906.0,"type":"keyUp", "unixTimeMs": 1752374480076.160889 },
{"activeModifiers":[],"character":"/", "isARepeat":false,"processTimeMs": 137724812.0,"type":"keyDown", "unixTimeMs": 1752374505989.154297 },
{"activeModifiers":[],"character":"/", "isARepeat":false,"processTimeMs": 137724875.0,"type":"keyUp", "unixTimeMs": 1752374506039.470703 },
{"activeModifiers":[],"character":"Backspace", "isARepeat":false,"processTimeMs": 137725500.0,"type":"keyDown", "unixTimeMs": 1752374506674.560303 },
{"activeModifiers":[],"character":"Backspace", "isARepeat":false,"processTimeMs": 137725578.0,"type":"keyUp", "unixTimeMs": 1752374506752.291260 },
{"activeModifiers":[],"character":"/", "isARepeat":false,"processTimeMs": 137725796.0,"type":"keyDown", "unixTimeMs": 1752374506973.572510 },
{"activeModifiers":[],"character":"/", "isARepeat":false,"processTimeMs": 137725875.0,"type":"keyUp", "unixTimeMs": 1752374507044.809570 },
{"activeModifiers":[],"character":"M", "isARepeat":false,"processTimeMs": 137726828.0,"type":"keyDown", "unixTimeMs": 1752374507999.906494 },
{"activeModifiers":[],"character":"M", "isARepeat":false,"processTimeMs": 137726906.0,"type":"keyUp", "unixTimeMs": 1752374508083.338623 },
{"activeModifiers":[],"character":"C", "isARepeat":false,"processTimeMs": 137726921.0,"type":"keyDown", "unixTimeMs": 1752374508092.747314 },
{"activeModifiers":[],"character":"C", "isARepeat":false,"processTimeMs": 137727015.0,"type":"keyUp", "unixTimeMs": 1752374508183.439697 },
{"activeModifiers":[],"character":"P", "isARepeat":false,"processTimeMs": 137727093.0,"type":"keyDown", "unixTimeMs": 1752374508270.150879 },
{"activeModifiers":[],"character":"P", "isARepeat":false,"processTimeMs": 137727187.0,"type":"keyUp", "unixTimeMs": 1752374508355.313232 },
{"activeModifiers":[],"character":"Enter", "isARepeat":false,"processTimeMs": 137728140.0,"type":"keyDown", "unixTimeMs": 1752374509307.155518 },
{"activeModifiers":[],"character":"Enter", "isARepeat":false,"processTimeMs": 137728218.0,"type":"keyUp", "unixTimeMs": 1752374509391.618164 },
{"activeModifiers":[],"character":"Y", "isARepeat":false,"processTimeMs": 137743468.0,"type":"keyDown", "unixTimeMs": 1752374524645.696045 },
{"activeModifiers":[],"character":"Y", "isARepeat":false,"processTimeMs": 137743546.0,"type":"keyUp", "unixTimeMs": 1752374524723.779541 },
{"activeModifiers":[],"character":"O", "isARepeat":false,"processTimeMs": 137743703.0,"type":"keyDown", "unixTimeMs": 1752374524874.379150 },
{"activeModifiers":[],"character":"O", "isARepeat":false,"processTimeMs": 137743765.0,"type":"keyUp", "unixTimeMs": 1752374524940.178223 },
{"activeModifiers":[],"character":"U", "isARepeat":false,"processTimeMs": 137743875.0,"type":"keyDown", "unixTimeMs": 1752374525042.457275 },
{"activeModifiers":[],"character":"U", "isARepeat":false,"processTimeMs": 137743937.0,"type":"keyUp", "unixTimeMs": 1752374525109.781982 },
{"activeModifiers":[],"character":"M", "isARepeat":false,"processTimeMs": 137744093.0,"type":"keyDown", "unixTimeMs": 1752374525261.248047 },
{"activeModifiers":[],"character":"M", "isARepeat":false,"processTimeMs": 137744171.0,"type":"keyUp", "unixTimeMs": 1752374525347.260742 },
{"activeModifiers":[],"character":"Backspace", "isARepeat":false,"processTimeMs": 137744406.0,"type":"keyDown", "unixTimeMs": 1752374525577.202637 },
{"activeModifiers":[],"character":"Backspace", "isARepeat":false,"processTimeMs": 137744468.0,"type":"keyUp", "unixTimeMs": 1752374525634.710693 },
{"activeModifiers":[],"character":"Backspace", "isARepeat":false,"processTimeMs": 137744703.0,"type":"keyDown", "unixTimeMs": 1752374525880.706299 },
{"activeModifiers":[],"character":"Backspace", "isARepeat":false,"processTimeMs": 137744765.0,"type":"keyUp", "unixTimeMs": 1752374525930.052002 },
{"activeModifiers":[],"character":"Backspace", "isARepeat":false,"processTimeMs": 137744859.0,"type":"keyDown", "unixTimeMs": 1752374526035.445557 },
{"activeModifiers":[],"character":"Backspace", "isARepeat":false,"processTimeMs": 137744921.0,"type":"keyUp", "unixTimeMs": 1752374526085.875977 },
{"activeModifiers":[],"character":"Backspace", "isARepeat":false,"processTimeMs": 137745000.0,"type":"keyDown", "unixTimeMs": 1752374526176.541992 },
{"activeModifiers":[],"character":"Backspace", "isARepeat":false,"processTimeMs": 137745093.0,"type":"keyUp", "unixTimeMs": 1752374526263.319580 },
{"activeModifiers":[],"character":"Y", "isARepeat":false,"processTimeMs": 137745390.0,"type":"keyDown", "unixTimeMs": 1752374526558.384277 },
{"activeModifiers":[],"character":"Y", "isARepeat":false,"processTimeMs": 137745484.0,"type":"keyUp", "unixTimeMs": 1752374526646.990723 },
{"activeModifiers":[],"character":"O", "isARepeat":false,"processTimeMs": 137745625.0,"type":"keyDown", "unixTimeMs": 1752374526787.443115 },
{"activeModifiers":[],"character":"O", "isARepeat":false,"processTimeMs": 137745671.0,"type":"keyUp", "unixTimeMs": 1752374526849.372803 },
{"activeModifiers":[],"character":"U", "isARepeat":false,"processTimeMs": 137745765.0,"type":"keyDown", "unixTimeMs": 1752374526940.066406 },
{"activeModifiers":[],"character":"U", "isARepeat":false,"processTimeMs": 137745828.0,"type":"keyUp", "unixTimeMs": 1752374526993.043945 },
{"activeModifiers":[],"character":"M", "isARepeat":false,"processTimeMs": 137745953.0,"type":"keyDown", "unixTimeMs": 1752374527122.656738 },
{"activeModifiers":[],"character":"E", "isARepeat":false,"processTimeMs": 137745968.0,"type":"keyDown", "unixTimeMs": 1752374527143.225098 },
{"activeModifiers":[],"character":"M", "isARepeat":false,"processTimeMs": 137746015.0,"type":"keyUp", "unixTimeMs": 1752374527192.126465 },
{"activeModifiers":[],"character":"E", "isARepeat":false,"processTimeMs": 137746031.0,"type":"keyUp", "unixTimeMs": 1752374527204.890381 },
{"activeModifiers":[],"character":"I", "isARepeat":false,"processTimeMs": 137746109.0,"type":"keyDown", "unixTimeMs": 1752374527286.705322 },
{"activeModifiers":[],"character":"I", "isARepeat":false,"processTimeMs": 137746171.0,"type":"keyUp", "unixTimeMs": 1752374527346.862549 },
{"activeModifiers":[],"character":"Y", "isARepeat":false,"processTimeMs": 137746281.0,"type":"keyDown", "unixTimeMs": 1752374527443.864258 },
{"activeModifiers":[],"character":"Y", "isARepeat":false,"processTimeMs": 137746343.0,"type":"keyUp", "unixTimeMs": 1752374527518.476318 },
{"activeModifiers":[],"character":"O", "isARepeat":false,"processTimeMs": 137746453.0,"type":"keyDown", "unixTimeMs": 1752374527630.781250 },
{"activeModifiers":[],"character":"O", "isARepeat":false,"processTimeMs": 137746515.0,"type":"keyUp", "unixTimeMs": 1752374527684.921631 },
{"activeModifiers":[],"character":"U", "isARepeat":false,"processTimeMs": 137746593.0,"type":"keyDown", "unixTimeMs": 1752374527768.952637 },
{"activeModifiers":[],"character":"U", "isARepeat":false,"processTimeMs": 137746671.0,"type":"keyUp", "unixTimeMs": 1752374527845.528076 },
{"activeModifiers":[],"character":"Space", "isARepeat":false,"processTimeMs": 137746687.0,"type":"keyDown", "unixTimeMs": 1752374527855.192383 },
{"activeModifiers":[],"character":"Space", "isARepeat":false,"processTimeMs": 137746781.0,"type":"keyUp", "unixTimeMs": 1752374527950.399902 },
{"activeModifiers":[],"character":"S", "isARepeat":false,"processTimeMs": 137748734.0,"type":"keyDown", "unixTimeMs": 1752374529903.134521 },
{"activeModifiers":[],"character":"S", "isARepeat":false,"processTimeMs": 137748812.0,"type":"keyUp", "unixTimeMs": 1752374529975.960693 },
{"activeModifiers":[],"character":"H", "isARepeat":false,"processTimeMs": 137748828.0,"type":"keyDown", "unixTimeMs": 1752374529995.033203 },
{"activeModifiers":[],"character":"H", "isARepeat":false,"processTimeMs": 137748906.0,"type":"keyUp", "unixTimeMs": 1752374530081.280029 },
{"activeModifiers":[],"character":"I", "isARepeat":false,"processTimeMs": 137749015.0,"type":"keyDown", "unixTimeMs": 1752374530188.559082 },
{"activeModifiers":[],"character":"I", "isARepeat":false,"processTimeMs": 137749062.0,"type":"keyUp", "unixTimeMs": 1752374530209.945312 },
{"activeModifiers":[],"character":"H", "isARepeat":false,"processTimeMs": 137749546.0,"type":"keyDown", "unixTimeMs": 1752374530709.685303 },
{"activeModifiers":[],"character":"H", "isARepeat":false,"processTimeMs": 137749640.0,"type":"keyUp", "unixTimeMs": 1752374530804.345459 },
{"activeModifiers":[],"character":"E", "isARepeat":false,"processTimeMs": 137749656.0,"type":"keyDown", "unixTimeMs": 1752374530820.663086 },
{"activeModifiers":[],"character":"Space", "isARepeat":false,"processTimeMs": 137749750.0,"type":"keyDown", "unixTimeMs": 1752374530926.155518 },
{"activeModifiers":[],"character":"E", "isARepeat":false,"processTimeMs": 137749781.0,"type":"keyUp", "unixTimeMs": 1752374530949.177246 },
{"activeModifiers":[],"character":"Space", "isARepeat":false,"processTimeMs": 137749875.0,"type":"keyUp", "unixTimeMs": 1752374531038.965088 },
{"activeModifiers":[],"character":"Z", "isARepeat":false,"processTimeMs": 137750484.0,"type":"keyDown", "unixTimeMs": 1752374531658.285400 },
{"activeModifiers":[],"character":"Z", "isARepeat":false,"processTimeMs": 137750578.0,"type":"keyUp", "unixTimeMs": 1752374531744.312744 },
{"activeModifiers":[],"character":"H", "isARepeat":false,"processTimeMs": 137750578.0,"type":"keyDown", "unixTimeMs": 1752374531752.953857 },
{"activeModifiers":[],"character":"H", "isARepeat":false,"processTimeMs": 137750656.0,"type":"keyUp", "unixTimeMs": 1752374531826.240723 },
{"activeModifiers":[],"character":"E", "isARepeat":false,"processTimeMs": 137750671.0,"type":"keyDown", "unixTimeMs": 1752374531835.776367 },
{"activeModifiers":[],"character":"E", "isARepeat":false,"processTimeMs": 137750718.0,"type":"keyUp", "unixTimeMs": 1752374531891.329590 },
{"activeModifiers":[],"character":"G", "isARepeat":false,"processTimeMs": 137750765.0,"type":"keyDown", "unixTimeMs": 1752374531937.132080 },
{"activeModifiers":[],"character":"E", "isARepeat":false,"processTimeMs": 137750828.0,"type":"keyDown", "unixTimeMs": 1752374531996.390381 },
{"activeModifiers":[],"character":"G", "isARepeat":false,"processTimeMs": 137750859.0,"type":"keyUp", "unixTimeMs": 1752374532034.487305 },
{"activeModifiers":[],"character":"Space", "isARepeat":false,"processTimeMs": 137750937.0,"type":"keyDown", "unixTimeMs": 1752374532105.953857 },
{"activeModifiers":[],"character":"E", "isARepeat":false,"processTimeMs": 137750953.0,"type":"keyUp", "unixTimeMs": 1752374532121.665527 },
{"activeModifiers":[],"character":"Space", "isARepeat":false,"processTimeMs": 137751046.0,"type":"keyUp", "unixTimeMs": 1752374532218.935547 },
{"activeModifiers":[],"character":"Y", "isARepeat":false,"processTimeMs": 137751093.0,"type":"keyDown", "unixTimeMs": 1752374532257.263184 },
{"activeModifiers":[],"character":"Y", "isARepeat":false,"processTimeMs": 137751171.0,"type":"keyUp", "unixTimeMs": 1752374532336.173584 },
{"activeModifiers":[],"character":"U", "isARepeat":false,"processTimeMs": 137751234.0,"type":"keyDown", "unixTimeMs": 1752374532411.018311 },
{"activeModifiers":[],"character":"E", "isARepeat":false,"processTimeMs": 137751312.0,"type":"keyDown", "unixTimeMs": 1752374532475.072998 },
{"activeModifiers":[],"character":"U", "isARepeat":false,"processTimeMs": 137751328.0,"type":"keyUp", "unixTimeMs": 1752374532491.641113 },
{"activeModifiers":[],"character":"E", "isARepeat":false,"processTimeMs": 137751390.0,"type":"keyUp", "unixTimeMs": 1752374532560.742432 },
{"activeModifiers":[],"character":"Space", "isARepeat":false,"processTimeMs": 137751406.0,"type":"keyDown", "unixTimeMs": 1752374532578.020508 },
{"activeModifiers":[],"character":"Space", "isARepeat":false,"processTimeMs": 137751500.0,"type":"keyUp", "unixTimeMs": 1752374532677.222900 },
{"activeModifiers":[],"character":"C", "isARepeat":false,"processTimeMs": 137751734.0,"type":"keyDown", "unixTimeMs": 1752374532899.307373 },
{"activeModifiers":[],"character":"C", "isARepeat":false,"processTimeMs": 137751812.0,"type":"keyUp", "unixTimeMs": 1752374532981.844482 },
{"activeModifiers":[],"character":"H", "isARepeat":false,"processTimeMs": 137751828.0,"type":"keyDown", "unixTimeMs": 1752374533002.324707 },
{"activeModifiers":[],"character":"H", "isARepeat":false,"processTimeMs": 137751906.0,"type":"keyUp", "unixTimeMs": 1752374533075.468018 },
{"activeModifiers":[],"character":"U", "isARepeat":false,"processTimeMs": 137751984.0,"type":"keyDown", "unixTimeMs": 1752374533161.174805 },
{"activeModifiers":[],"character":"U", "isARepeat":false,"processTimeMs": 137752078.0,"type":"keyUp", "unixTimeMs": 1752374533241.687500 },
{"activeModifiers":[],"character":"X", "isARepeat":false,"processTimeMs": 137752078.0,"type":"keyDown", "unixTimeMs": 1752374533252.735596 },
{"activeModifiers":[],"character":"X", "isARepeat":false,"processTimeMs": 137752171.0,"type":"keyUp", "unixTimeMs": 1752374533337.438721 },
{"activeModifiers":[],"character":"I", "isARepeat":false,"processTimeMs": 137752187.0,"type":"keyDown", "unixTimeMs": 1752374533350.352295 },
{"activeModifiers":[],"character":"I", "isARepeat":false,"processTimeMs": 137752250.0,"type":"keyUp", "unixTimeMs": 1752374533417.486084 },
{"activeModifiers":[],"character":"N", "isARepeat":false,"processTimeMs": 137752328.0,"type":"keyDown", "unixTimeMs": 1752374533503.227783 },
{"activeModifiers":[],"character":"G", "isARepeat":false,"processTimeMs": 137752406.0,"type":"keyDown", "unixTimeMs": 1752374533570.991211 },
{"activeModifiers":[],"character":"N", "isARepeat":false,"processTimeMs": 137752421.0,"type":"keyUp", "unixTimeMs": 1752374533590.582764 },
{"activeModifiers":[],"character":"Space", "isARepeat":false,"processTimeMs": 137752468.0,"type":"keyDown", "unixTimeMs": 1752374533641.079102 },
{"activeModifiers":[],"character":"G", "isARepeat":false,"processTimeMs": 137752484.0,"type":"keyUp", "unixTimeMs": 1752374533657.654785 },
{"activeModifiers":[],"character":"Space", "isARepeat":false,"processTimeMs": 137752578.0,"type":"keyUp", "unixTimeMs": 1752374533753.669678 },
{"activeModifiers":[],"character":"D", "isARepeat":false,"processTimeMs": 137753546.0,"type":"keyDown", "unixTimeMs": 1752374534723.046875 },
{"activeModifiers":[],"character":"E", "isARepeat":false,"processTimeMs": 137753625.0,"type":"keyDown", "unixTimeMs": 1752374534794.040039 },
{"activeModifiers":[],"character":"D", "isARepeat":false,"processTimeMs": 137753656.0,"type":"keyUp", "unixTimeMs": 1752374534830.822998 },
{"activeModifiers":[],"character":"Space", "isARepeat":false,"processTimeMs": 137753718.0,"type":"keyDown", "unixTimeMs": 1752374534882.830078 },
{"activeModifiers":[],"character":"E", "isARepeat":false,"processTimeMs": 137753734.0,"type":"keyUp", "unixTimeMs": 1752374534898.171875 },
{"activeModifiers":[],"character":"Space", "isARepeat":false,"processTimeMs": 137753812.0,"type":"keyUp", "unixTimeMs": 1752374534982.428711 },
{"activeModifiers":[],"character":",", "isARepeat":false,"processTimeMs": 137755468.0,"type":"keyDown", "unixTimeMs": 1752374536631.547119 },
{"activeModifiers":[],"character":",", "isARepeat":false,"processTimeMs": 137755515.0,"type":"keyUp", "unixTimeMs": 1752374536685.871582 },
{"activeModifiers":[],"character":"Backspace", "isARepeat":false,"processTimeMs": 137756343.0,"type":"keyDown", "unixTimeMs": 1752374537521.181641 },
{"activeModifiers":[],"character":"Backspace", "isARepeat":false,"processTimeMs": 137756453.0,"type":"keyUp", "unixTimeMs": 1752374537617.798340 },
{"activeModifiers":[],"character":"F", "isARepeat":false,"processTimeMs": 137756937.0,"type":"keyDown", "unixTimeMs": 1752374538110.783203 },
{"activeModifiers":[],"character":"E", "isARepeat":false,"processTimeMs": 137757015.0,"type":"keyDown", "unixTimeMs": 1752374538188.122314 },
{"activeModifiers":[],"character":"F", "isARepeat":false,"processTimeMs": 137757078.0,"type":"keyUp", "unixTimeMs": 1752374538248.326904 },
{"activeModifiers":[],"character":"E", "isARepeat":false,"processTimeMs": 137757140.0,"type":"keyUp", "unixTimeMs": 1752374538316.508057 },
{"activeModifiers":[],"character":"N", "isARepeat":false,"processTimeMs": 137757203.0,"type":"keyDown", "unixTimeMs": 1752374538376.531250 },
{"activeModifiers":[],"character":"N", "isARepeat":false,"processTimeMs": 137757312.0,"type":"keyUp", "unixTimeMs": 1752374538483.357666 },
{"activeModifiers":[],"character":"G", "isARepeat":false,"processTimeMs": 137757375.0,"type":"keyDown", "unixTimeMs": 1752374538537.334717 },
{"activeModifiers":[],"character":"G", "isARepeat":false,"processTimeMs": 137757453.0,"type":"keyUp", "unixTimeMs": 1752374538620.600342 },
{"activeModifiers":[],"character":"Space", "isARepeat":false,"processTimeMs": 137757765.0,"type":"keyDown", "unixTimeMs": 1752374538934.729248 },
{"activeModifiers":[],"character":"Space", "isARepeat":false,"processTimeMs": 137757859.0,"type":"keyUp", "unixTimeMs": 1752374539030.785400 },
{"activeModifiers":[],"character":"Backspace", "isARepeat":false,"processTimeMs": 137757968.0,"type":"keyDown", "unixTimeMs": 1752374539131.845703 },
{"activeModifiers":[],"character":"Backspace", "isARepeat":false,"processTimeMs": 137758109.0,"type":"keyUp", "unixTimeMs": 1752374539286.583252 },
{"activeModifiers":[],"character":",", "isARepeat":false,"processTimeMs": 137758609.0,"type":"keyDown", "unixTimeMs": 1752374539774.508057 },
{"activeModifiers":[],"character":",", "isARepeat":false,"processTimeMs": 137758671.0,"type":"keyUp", "unixTimeMs": 1752374539834.464355 },
{"activeModifiers":[],"character":"F", "isARepeat":false,"processTimeMs": 137758796.0,"type":"keyDown", "unixTimeMs": 1752374539967.534912 },
{"activeModifiers":[],"character":"E", "isARepeat":false,"processTimeMs": 137758859.0,"type":"keyDown", "unixTimeMs": 1752374540033.720459 },
{"activeModifiers":[],"character":"F", "isARepeat":false,"processTimeMs": 137758906.0,"type":"keyUp", "unixTimeMs": 1752374540080.157715 },
{"activeModifiers":[],"character":"N", "isARepeat":false,"processTimeMs": 137758937.0,"type":"keyDown", "unixTimeMs": 1752374540113.494873 },
{"activeModifiers":[],"character":"E", "isARepeat":false,"processTimeMs": 137758968.0,"type":"keyUp", "unixTimeMs": 1752374540135.025146 },
{"activeModifiers":[],"character":"N", "isARepeat":false,"processTimeMs": 137759015.0,"type":"keyUp", "unixTimeMs": 1752374540187.969971 },
{"activeModifiers":[],"character":"G", "isARepeat":false,"processTimeMs": 137759046.0,"type":"keyDown", "unixTimeMs": 1752374540217.766113 },
{"activeModifiers":[],"character":"G", "isARepeat":false,"processTimeMs": 137759125.0,"type":"keyUp", "unixTimeMs": 1752374540292.274414 },
{"activeModifiers":[],"character":"J", "isARepeat":false,"processTimeMs": 137759218.0,"type":"keyDown", "unixTimeMs": 1752374540384.509521 },
{"activeModifiers":[],"character":"J", "isARepeat":false,"processTimeMs": 137759281.0,"type":"keyUp", "unixTimeMs": 1752374540449.558105 },
{"activeModifiers":[],"character":"I", "isARepeat":false,"processTimeMs": 137759359.0,"type":"keyDown", "unixTimeMs": 1752374540535.919189 },
{"activeModifiers":[],"character":"I", "isARepeat":false,"processTimeMs": 137759437.0,"type":"keyUp", "unixTimeMs": 1752374540608.129883 },
{"activeModifiers":[],"character":"N", "isARepeat":false,"processTimeMs": 137759531.0,"type":"keyDown", "unixTimeMs": 1752374540702.369629 },
{"activeModifiers":[],"character":"G", "isARepeat":false,"processTimeMs": 137759609.0,"type":"keyDown", "unixTimeMs": 1752374540784.440430 },
{"activeModifiers":[],"character":"N", "isARepeat":false,"processTimeMs": 137759625.0,"type":"keyUp", "unixTimeMs": 1752374540799.623291 },
{"activeModifiers":[],"character":"Space", "isARepeat":false,"processTimeMs": 137759687.0,"type":"keyDown", "unixTimeMs": 1752374540861.290039 },
{"activeModifiers":[],"character":"G", "isARepeat":false,"processTimeMs": 137759718.0,"type":"keyUp", "unixTimeMs": 1752374540889.270020 },
{"activeModifiers":[],"character":"Space", "isARepeat":false,"processTimeMs": 137759781.0,"type":"keyUp", "unixTimeMs": 1752374540956.683105 },
{"activeModifiers":[],"character":"H", "isARepeat":false,"processTimeMs": 137759859.0,"type":"keyDown", "unixTimeMs": 1752374541006.403564 },
{"activeModifiers":[],"character":"H", "isARepeat":false,"processTimeMs": 137759968.0,"type":"keyUp", "unixTimeMs": 1752374541143.578857 },
{"activeModifiers":[],"character":"A", "isARepeat":false,"processTimeMs": 137759968.0,"type":"keyDown", "unixTimeMs": 1752374541145.584473 },
{"activeModifiers":[],"character":"A", "isARepeat":false,"processTimeMs": 137760046.0,"type":"keyUp", "unixTimeMs": 1752374541219.708008 },
{"activeModifiers":[],"character":"O", "isARepeat":false,"processTimeMs": 137760093.0,"type":"keyDown", "unixTimeMs": 1752374541261.673096 },
{"activeModifiers":[],"character":"O", "isARepeat":false,"processTimeMs": 137760156.0,"type":"keyUp", "unixTimeMs": 1752374541331.738281 },
{"activeModifiers":[],"character":"Y", "isARepeat":false,"processTimeMs": 137760328.0,"type":"keyDown", "unixTimeMs": 1752374541494.666748 },
{"activeModifiers":[],"character":"Y", "isARepeat":false,"processTimeMs": 137760406.0,"type":"keyUp", "unixTimeMs": 1752374541578.237305 },
{"activeModifiers":[],"character":"I", "isARepeat":false,"processTimeMs": 137760500.0,"type":"keyDown", "unixTimeMs": 1752374541672.334473 },
{"activeModifiers":[],"character":"D", "isARepeat":false,"processTimeMs": 137760546.0,"type":"keyDown", "unixTimeMs": 1752374541720.553711 },
{"activeModifiers":[],"character":"I", "isARepeat":false,"processTimeMs": 137760578.0,"type":"keyUp", "unixTimeMs": 1752374541748.489990 },
{"activeModifiers":[],"character":"D", "isARepeat":false,"processTimeMs": 137760656.0,"type":"keyUp", "unixTimeMs": 1752374541826.979492 },
{"activeModifiers":[],"character":"I", "isARepeat":false,"processTimeMs": 137760671.0,"type":"keyDown", "unixTimeMs": 1752374541837.527588 },
{"activeModifiers":[],"character":"I", "isARepeat":false,"processTimeMs": 137760734.0,"type":"keyUp", "unixTimeMs": 1752374541905.389404 },
{"activeModifiers":[],"character":"A", "isARepeat":false,"processTimeMs": 137760750.0,"type":"keyDown", "unixTimeMs": 1752374541922.325684 },
{"activeModifiers":[],"character":"A", "isARepeat":false,"processTimeMs": 137760843.0,"type":"keyUp", "unixTimeMs": 1752374542006.374268 },
{"activeModifiers":[],"character":"N", "isARepeat":false,"processTimeMs": 137760859.0,"type":"keyDown", "unixTimeMs": 1752374542034.052490 },
{"activeModifiers":[],"character":"Space", "isARepeat":false,"processTimeMs": 137760921.0,"type":"keyDown", "unixTimeMs": 1752374542092.180420 },
{"activeModifiers":[],"character":"N", "isARepeat":false,"processTimeMs": 137760937.0,"type":"keyUp", "unixTimeMs": 1752374542105.426514 },
{"activeModifiers":[],"character":"D", "isARepeat":false,"processTimeMs": 137761000.0,"type":"keyDown", "unixTimeMs": 1752374542164.917969 },
{"activeModifiers":[],"character":"Space", "isARepeat":false,"processTimeMs": 137761031.0,"type":"keyUp", "unixTimeMs": 1752374542199.665283 },
{"activeModifiers":[],"character":"E", "isARepeat":false,"processTimeMs": 137761046.0,"type":"keyDown", "unixTimeMs": 1752374542219.152344 },
{"activeModifiers":[],"character":"D", "isARepeat":false,"processTimeMs": 137761109.0,"type":"keyUp", "unixTimeMs": 1752374542286.459961 },
{"activeModifiers":[],"character":"Space", "isARepeat":false,"processTimeMs": 137761125.0,"type":"keyDown", "unixTimeMs": 1752374542297.443848 },
{"activeModifiers":[],"character":"E", "isARepeat":false,"processTimeMs": 137761156.0,"type":"keyUp", "unixTimeMs": 1752374542332.871826 },
{"activeModifiers":[],"character":"Space", "isARepeat":false,"processTimeMs": 137761234.0,"type":"keyUp", "unixTimeMs": 1752374542396.510742 },
{"activeModifiers":[],"character":"X", "isARepeat":false,"processTimeMs": 137763078.0,"type":"keyDown", "unixTimeMs": 1752374544244.766357 },
{"activeModifiers":[],"character":"X", "isARepeat":false,"processTimeMs": 137763171.0,"type":"keyUp", "unixTimeMs": 1752374544346.738770 },
{"activeModifiers":[],"character":"I", "isARepeat":false,"processTimeMs": 137763187.0,"type":"keyDown", "unixTimeMs": 1752374544362.515137 },
{"activeModifiers":[],"character":"I", "isARepeat":false,"processTimeMs": 137763265.0,"type":"keyUp", "unixTimeMs": 1752374544441.307861 },
{"activeModifiers":[],"character":"A", "isARepeat":false,"processTimeMs": 137763328.0,"type":"keyDown", "unixTimeMs": 1752374544492.265381 },
{"activeModifiers":[],"character":"O", "isARepeat":false,"processTimeMs": 137763390.0,"type":"keyDown", "unixTimeMs": 1752374544555.568359 },
{"activeModifiers":[],"character":"A", "isARepeat":false,"processTimeMs": 137763390.0,"type":"keyUp", "unixTimeMs": 1752374544560.583740 },
{"activeModifiers":[],"character":"O", "isARepeat":false,"processTimeMs": 137763468.0,"type":"keyUp", "unixTimeMs": 1752374544616.560547 },
{"activeModifiers":[],"character":"Z", "isARepeat":false,"processTimeMs": 137763531.0,"type":"keyDown", "unixTimeMs": 1752374544706.635010 },
{"activeModifiers":[],"character":"Z", "isARepeat":false,"processTimeMs": 137763640.0,"type":"keyUp", "unixTimeMs": 1752374544807.612061 },
{"activeModifiers":[],"character":"H", "isARepeat":false,"processTimeMs": 137763640.0,"type":"keyDown", "unixTimeMs": 1752374544814.646484 },
{"activeModifiers":[],"character":"H", "isARepeat":false,"processTimeMs": 137763718.0,"type":"keyUp", "unixTimeMs": 1752374544880.951904 },
{"activeModifiers":[],"character":"O", "isARepeat":false,"processTimeMs": 137763812.0,"type":"keyDown", "unixTimeMs": 1752374544982.912842 },
{"activeModifiers":[],"character":"O", "isARepeat":false,"processTimeMs": 137763890.0,"type":"keyUp", "unixTimeMs": 1752374545054.983154 },
{"activeModifiers":[],"character":"N", "isARepeat":false,"processTimeMs": 137763968.0,"type":"keyDown", "unixTimeMs": 1752374545145.447754 },
{"activeModifiers":[],"character":"G", "isARepeat":false,"processTimeMs": 137764015.0,"type":"keyDown", "unixTimeMs": 1752374545189.892090 },
{"activeModifiers":[],"character":"N", "isARepeat":false,"processTimeMs": 137764078.0,"type":"keyUp", "unixTimeMs": 1752374545250.502686 },
{"activeModifiers":[],"character":"Space", "isARepeat":false,"processTimeMs": 137764109.0,"type":"keyDown", "unixTimeMs": 1752374545273.862305 },
{"activeModifiers":[],"character":"G", "isARepeat":false,"processTimeMs": 137764125.0,"type":"keyUp", "unixTimeMs": 1752374545294.625244 },
{"activeModifiers":[],"character":"Space", "isARepeat":false,"processTimeMs": 137764187.0,"type":"keyUp", "unixTimeMs": 1752374545352.592285 },
{"activeModifiers":[],"character":"C", "isARepeat":false,"processTimeMs": 137764203.0,"type":"keyDown", "unixTimeMs": 1752374545380.358887 },
{"activeModifiers":[],"character":"H", "isARepeat":false,"processTimeMs": 137764328.0,"type":"keyDown", "unixTimeMs": 1752374545495.310791 },
{"activeModifiers":[],"character":"C", "isARepeat":false,"processTimeMs": 137764343.0,"type":"keyUp", "unixTimeMs": 1752374545508.453125 },
{"activeModifiers":[],"character":"E", "isARepeat":false,"processTimeMs": 137764406.0,"type":"keyDown", "unixTimeMs": 1752374545569.836670 },
{"activeModifiers":[],"character":"H", "isARepeat":false,"processTimeMs": 137764421.0,"type":"keyUp", "unixTimeMs": 1752374545569.890137 },
{"activeModifiers":[],"character":"E", "isARepeat":false,"processTimeMs": 137764515.0,"type":"keyUp", "unixTimeMs": 1752374545679.277100 },
{"activeModifiers":[],"character":"N", "isARepeat":false,"processTimeMs": 137764531.0,"type":"keyDown", "unixTimeMs": 1752374545694.240479 },
{"activeModifiers":[],"character":"G", "isARepeat":false,"processTimeMs": 137764531.0,"type":"keyDown", "unixTimeMs": 1752374545696.750488 },
{"activeModifiers":[],"character":"N", "isARepeat":false,"processTimeMs": 137764593.0,"type":"keyUp", "unixTimeMs": 1752374545760.584717 },
{"activeModifiers":[],"character":"G", "isARepeat":false,"processTimeMs": 137764625.0,"type":"keyUp", "unixTimeMs": 1752374545797.270508 },
{"activeModifiers":[],"character":"S", "isARepeat":false,"processTimeMs": 137764687.0,"type":"keyDown", "unixTimeMs": 1752374545859.954346 },
{"activeModifiers":[],"character":"S", "isARepeat":false,"processTimeMs": 137764765.0,"type":"keyUp", "unixTimeMs": 1752374545942.046387 },
{"activeModifiers":[],"character":"H", "isARepeat":false,"processTimeMs": 137764781.0,"type":"keyDown", "unixTimeMs": 1752374545950.737305 },
{"activeModifiers":[],"character":"H", "isARepeat":false,"processTimeMs": 137764843.0,"type":"keyUp", "unixTimeMs": 1752374546010.464844 },
{"activeModifiers":[],"character":"I", "isARepeat":false,"processTimeMs": 137764921.0,"type":"keyDown", "unixTimeMs": 1752374546090.676758 },
{"activeModifiers":[],"character":"Space", "isARepeat":false,"processTimeMs": 137765000.0,"type":"keyDown", "unixTimeMs": 1752374546171.144531 },
{"activeModifiers":[],"character":"I", "isARepeat":false,"processTimeMs": 137765015.0,"type":"keyUp", "unixTimeMs": 1752374546183.831299 },
{"activeModifiers":[],"character":"Space", "isARepeat":false,"processTimeMs": 137765093.0,"type":"keyUp", "unixTimeMs": 1752374546259.356445 },
{"activeModifiers":[],"character":",", "isARepeat":false,"processTimeMs": 137767796.0,"type":"keyDown", "unixTimeMs": 1752374548961.502441 },
{"activeModifiers":[],"character":",", "isARepeat":false,"processTimeMs": 137767875.0,"type":"keyUp", "unixTimeMs": 1752374549039.349609 },
{"activeModifiers":[],"character":"Z", "isARepeat":false,"processTimeMs": 137769250.0,"type":"keyDown", "unixTimeMs": 1752374550416.392334 },
{"activeModifiers":[],"character":"Z", "isARepeat":false,"processTimeMs": 137769312.0,"type":"keyUp", "unixTimeMs": 1752374550489.702881 },
{"activeModifiers":[],"character":"U", "isARepeat":false,"processTimeMs": 137769343.0,"type":"keyDown", "unixTimeMs": 1752374550505.861328 },
{"activeModifiers":[],"character":"U", "isARepeat":false,"processTimeMs": 137769406.0,"type":"keyUp", "unixTimeMs": 1752374550579.050049 },
{"activeModifiers":[],"character":"I", "isARepeat":false,"processTimeMs": 137769484.0,"type":"keyDown", "unixTimeMs": 1752374550660.656494 },
{"activeModifiers":[],"character":"I", "isARepeat":false,"processTimeMs": 137769562.0,"type":"keyUp", "unixTimeMs": 1752374550727.358887 },
{"activeModifiers":[],"character":"H", "isARepeat":false,"processTimeMs": 137769671.0,"type":"keyDown", "unixTimeMs": 1752374550840.976318 },
{"activeModifiers":[],"character":"H", "isARepeat":false,"processTimeMs": 137769734.0,"type":"keyUp", "unixTimeMs": 1752374550911.795898 },
{"activeModifiers":[],"character":"A", "isARepeat":false,"processTimeMs": 137769765.0,"type":"keyDown", "unixTimeMs": 1752374550932.359619 },
{"activeModifiers":[],"character":"A", "isARepeat":false,"processTimeMs": 137769828.0,"type":"keyUp", "unixTimeMs": 1752374551003.919678 },
{"activeModifiers":[],"character":"O", "isARepeat":false,"processTimeMs": 137769875.0,"type":"keyDown", "unixTimeMs": 1752374551044.229248 },
{"activeModifiers":[],"character":"O", "isARepeat":false,"processTimeMs": 137769937.0,"type":"keyUp", "unixTimeMs": 1752374551108.357910 },
{"activeModifiers":[],"character":"Space", "isARepeat":false,"processTimeMs": 137769984.0,"type":"keyDown", "unixTimeMs": 1752374551159.755127 },
{"activeModifiers":[],"character":"Space", "isARepeat":false,"processTimeMs": 137770062.0,"type":"keyUp", "unixTimeMs": 1752374551235.676758 },
{"activeModifiers":[],"character":"S", "isARepeat":false,"processTimeMs": 137770109.0,"type":"keyDown", "unixTimeMs": 1752374551284.997559 },
{"activeModifiers":[],"character":"S", "isARepeat":false,"processTimeMs": 137770187.0,"type":"keyUp", "unixTimeMs": 1752374551349.691895 },
{"activeModifiers":[],"character":"H", "isARepeat":false,"processTimeMs": 137770187.0,"type":"keyDown", "unixTimeMs": 1752374551357.962891 },
{"activeModifiers":[],"character":"H", "isARepeat":false,"processTimeMs": 137770250.0,"type":"keyUp", "unixTimeMs": 1752374551414.147949 },
{"activeModifiers":[],"character":"I", "isARepeat":false,"processTimeMs": 137770343.0,"type":"keyDown", "unixTimeMs": 1752374551506.924805 },
{"activeModifiers":[],"character":"Space", "isARepeat":false,"processTimeMs": 137770390.0,"type":"keyDown", "unixTimeMs": 1752374551565.853271 },
{"activeModifiers":[],"character":"I", "isARepeat":false,"processTimeMs": 137770406.0,"type":"keyUp", "unixTimeMs": 1752374551580.000977 },
{"activeModifiers":[],"character":"Space", "isARepeat":false,"processTimeMs": 137770468.0,"type":"keyUp", "unixTimeMs": 1752374551646.369141 },
{"activeModifiers":[],"character":"H", "isARepeat":false,"processTimeMs": 137770640.0,"type":"keyDown", "unixTimeMs": 1752374551809.425293 },
{"activeModifiers":[],"character":"H", "isARepeat":false,"processTimeMs": 137770703.0,"type":"keyUp", "unixTimeMs": 1752374551871.775146 },
{"activeModifiers":[],"character":"U", "isARepeat":false,"processTimeMs": 137770781.0,"type":"keyDown", "unixTimeMs": 1752374551957.226074 },
{"activeModifiers":[],"character":"U", "isARepeat":false,"processTimeMs": 137770859.0,"type":"keyUp", "unixTimeMs": 1752374552025.693115 },
{"activeModifiers":[],"character":"O", "isARepeat":false,"processTimeMs": 137770968.0,"type":"keyDown", "unixTimeMs": 1752374552139.927002 },
{"activeModifiers":[],"character":"C", "isARepeat":false,"processTimeMs": 137771015.0,"type":"keyDown", "unixTimeMs": 1752374552178.318115 },
{"activeModifiers":[],"character":"O", "isARepeat":false,"processTimeMs": 137771015.0,"type":"keyUp", "unixTimeMs": 1752374552191.358154 },
{"activeModifiers":[],"character":"C", "isARepeat":false,"processTimeMs": 137771109.0,"type":"keyUp", "unixTimeMs": 1752374552274.742920 },
{"activeModifiers":[],"character":"H", "isARepeat":false,"processTimeMs": 137771140.0,"type":"keyDown", "unixTimeMs": 1752374552313.319580 },
{"activeModifiers":[],"character":"H", "isARepeat":false,"processTimeMs": 137771203.0,"type":"keyUp", "unixTimeMs": 1752374552372.595947 },
{"activeModifiers":[],"character":"E", "isARepeat":false,"processTimeMs": 137771218.0,"type":"keyDown", "unixTimeMs": 1752374552381.414307 },
{"activeModifiers":[],"character":"E", "isARepeat":false,"processTimeMs": 137771312.0,"type":"keyUp", "unixTimeMs": 1752374552477.918701 },
{"activeModifiers":[],"character":"G", "isARepeat":false,"processTimeMs": 137771531.0,"type":"keyDown", "unixTimeMs": 1752374552698.803711 },
{"activeModifiers":[],"character":"G", "isARepeat":false,"processTimeMs": 137771593.0,"type":"keyUp", "unixTimeMs": 1752374552771.251709 },
{"activeModifiers":[],"character":"Space", "isARepeat":false,"processTimeMs": 137771968.0,"type":"keyDown", "unixTimeMs": 1752374553134.267090 },
{"activeModifiers":[],"character":"Space", "isARepeat":false,"processTimeMs": 137772031.0,"type":"keyUp", "unixTimeMs": 1752374553200.658691 },
{"activeModifiers":[],"character":"Backspace", "isARepeat":false,"processTimeMs": 137772250.0,"type":"keyDown", "unixTimeMs": 1752374553424.686279 },
{"activeModifiers":[],"character":"Backspace", "isARepeat":false,"processTimeMs": 137772343.0,"type":"keyUp", "unixTimeMs": 1752374553509.035889 },
{"activeModifiers":[],"character":"Backspace", "isARepeat":false,"processTimeMs": 137772453.0,"type":"keyDown", "unixTimeMs": 1752374553619.137939 },
{"activeModifiers":[],"character":"Backspace", "isARepeat":false,"processTimeMs": 137772531.0,"type":"keyUp", "unixTimeMs": 1752374553695.174805 },
{"activeModifiers":[],"character":"H", "isARepeat":false,"processTimeMs": 137772765.0,"type":"keyDown", "unixTimeMs": 1752374553940.260742 },
{"activeModifiers":[],"character":"H", "isARepeat":false,"processTimeMs": 137772843.0,"type":"keyUp", "unixTimeMs": 1752374554011.475342 },
{"activeModifiers":[],"character":"U", "isARepeat":false,"processTimeMs": 137772937.0,"type":"keyDown", "unixTimeMs": 1752374554105.241699 },
{"activeModifiers":[],"character":"U", "isARepeat":false,"processTimeMs": 137773000.0,"type":"keyUp", "unixTimeMs": 1752374554165.277100 },
{"activeModifiers":[],"character":"O", "isARepeat":false,"processTimeMs": 137773109.0,"type":"keyDown", "unixTimeMs": 1752374554278.785645 },
{"activeModifiers":[],"character":"O", "isARepeat":false,"processTimeMs": 137773171.0,"type":"keyUp", "unixTimeMs": 1752374554342.593750 },
{"activeModifiers":[],"character":"C", "isARepeat":false,"processTimeMs": 137773421.0,"type":"keyDown", "unixTimeMs": 1752374554595.451660 },
{"activeModifiers":[],"character":"C", "isARepeat":false,"processTimeMs": 137773546.0,"type":"keyUp", "unixTimeMs": 1752374554710.558105 },
{"activeModifiers":[],"character":"H", "isARepeat":false,"processTimeMs": 137773562.0,"type":"keyDown", "unixTimeMs": 1752374554726.558105 },
{"activeModifiers":[],"character":"E", "isARepeat":false,"processTimeMs": 137773578.0,"type":"keyDown", "unixTimeMs": 1752374554743.501221 },
{"activeModifiers":[],"character":"H", "isARepeat":false,"processTimeMs": 137773593.0,"type":"keyUp", "unixTimeMs": 1752374554766.263428 },
{"activeModifiers":[],"character":"E", "isARepeat":false,"processTimeMs": 137773687.0,"type":"keyUp", "unixTimeMs": 1752374554851.425537 },
{"activeModifiers":[],"character":"G", "isARepeat":false,"processTimeMs": 137773781.0,"type":"keyDown", "unixTimeMs": 1752374554950.168701 },
{"activeModifiers":[],"character":"G", "isARepeat":false,"processTimeMs": 137773859.0,"type":"keyUp", "unixTimeMs": 1752374555034.516357 },
{"activeModifiers":[],"character":"Space", "isARepeat":false,"processTimeMs": 137773984.0,"type":"keyDown", "unixTimeMs": 1752374555160.251221 },
{"activeModifiers":[],"character":"Space", "isARepeat":false,"processTimeMs": 137774078.0,"type":"keyUp", "unixTimeMs": 1752374555242.788574 },
{"activeModifiers":[],"character":"N", "isARepeat":false,"processTimeMs": 137774140.0,"type":"keyDown", "unixTimeMs": 1752374555310.026855 },
{"activeModifiers":[],"character":"N", "isARepeat":false,"processTimeMs": 137774203.0,"type":"keyUp", "unixTimeMs": 1752374555371.011230 },
{"activeModifiers":[],"character":"Backspace", "isARepeat":false,"processTimeMs": 137774531.0,"type":"keyDown", "unixTimeMs": 1752374555700.951660 },
{"activeModifiers":[],"character":"Backspace", "isARepeat":false,"processTimeMs": 137774593.0,"type":"keyUp", "unixTimeMs": 1752374555761.037598 },
{"activeModifiers":[],"character":"Backspace", "isARepeat":false,"processTimeMs": 137774703.0,"type":"keyDown", "unixTimeMs": 1752374555868.757568 },
{"activeModifiers":[],"character":"Backspace", "isARepeat":false,"processTimeMs": 137774750.0,"type":"keyUp", "unixTimeMs": 1752374555922.301270 },
{"activeModifiers":[],"character":"Backspace", "isARepeat":false,"processTimeMs": 137775015.0,"type":"keyDown", "unixTimeMs": 1752374556184.912109 },
{"activeModifiers":[],"character":"Backspace", "isARepeat":false,"processTimeMs": 137775078.0,"type":"keyUp", "unixTimeMs": 1752374556250.955078 },
{"activeModifiers":[],"character":"H", "isARepeat":false,"processTimeMs": 137775234.0,"type":"keyDown", "unixTimeMs": 1752374556402.774902 },
{"activeModifiers":[],"character":"H", "isARepeat":false,"processTimeMs": 137775296.0,"type":"keyUp", "unixTimeMs": 1752374556474.367920 },
{"activeModifiers":[],"character":"U", "isARepeat":false,"processTimeMs": 137775390.0,"type":"keyDown", "unixTimeMs": 1752374556557.837402 },
{"activeModifiers":[],"character":"U", "isARepeat":false,"processTimeMs": 137775453.0,"type":"keyUp", "unixTimeMs": 1752374556619.159180 },
{"activeModifiers":[],"character":"O", "isARepeat":false,"processTimeMs": 137775562.0,"type":"keyDown", "unixTimeMs": 1752374556733.187744 },
{"activeModifiers":[],"character":"O", "isARepeat":false,"processTimeMs": 137775625.0,"type":"keyUp", "unixTimeMs": 1752374556795.985107 },
{"activeModifiers":[],"character":"C", "isARepeat":false,"processTimeMs": 137775671.0,"type":"keyDown", "unixTimeMs": 1752374556848.749268 },
{"activeModifiers":[],"character":"C", "isARepeat":false,"processTimeMs": 137775765.0,"type":"keyUp", "unixTimeMs": 1752374556929.377197 },
{"activeModifiers":[],"character":"H", "isARepeat":false,"processTimeMs": 137775765.0,"type":"keyDown", "unixTimeMs": 1752374556939.891113 },
{"activeModifiers":[],"character":"H", "isARepeat":false,"processTimeMs": 137775843.0,"type":"keyUp", "unixTimeMs": 1752374557013.062012 },
{"activeModifiers":[],"character":"E", "isARepeat":false,"processTimeMs": 137775843.0,"type":"keyDown", "unixTimeMs": 1752374557018.332764 },
{"activeModifiers":[],"character":"E", "isARepeat":false,"processTimeMs": 137775921.0,"type":"keyUp", "unixTimeMs": 1752374557090.687988 },
{"activeModifiers":[],"character":"Space", "isARepeat":false,"processTimeMs": 137776281.0,"type":"keyDown", "unixTimeMs": 1752374557445.401611 },
{"activeModifiers":[],"character":"Space", "isARepeat":false,"processTimeMs": 137776390.0,"type":"keyUp", "unixTimeMs": 1752374557561.940430 },
{"activeModifiers":[],"character":"N", "isARepeat":false,"processTimeMs": 137776500.0,"type":"keyDown", "unixTimeMs": 1752374557673.088379 },
{"activeModifiers":[],"character":"N", "isARepeat":false,"processTimeMs": 137776562.0,"type":"keyUp", "unixTimeMs": 1752374557734.546631 },
{"activeModifiers":[],"character":"E", "isARepeat":false,"processTimeMs": 137776578.0,"type":"keyDown", "unixTimeMs": 1752374557755.671143 },
{"activeModifiers":[],"character":"N", "isARepeat":false,"processTimeMs": 137776656.0,"type":"keyDown", "unixTimeMs": 1752374557824.304688 },
{"activeModifiers":[],"character":"E", "isARepeat":false,"processTimeMs": 137776671.0,"type":"keyUp", "unixTimeMs": 1752374557840.673096 },
{"activeModifiers":[],"character":"G", "isARepeat":false,"processTimeMs": 137776718.0,"type":"keyDown", "unixTimeMs": 1752374557884.978760 },
{"activeModifiers":[],"character":"N", "isARepeat":false,"processTimeMs": 137776734.0,"type":"keyUp", "unixTimeMs": 1752374557900.715576 },
{"activeModifiers":[],"character":"G", "isARepeat":false,"processTimeMs": 137776812.0,"type":"keyUp", "unixTimeMs": 1752374557978.922119 },
{"activeModifiers":[],"character":"Space", "isARepeat":false,"processTimeMs": 137776953.0,"type":"keyDown", "unixTimeMs": 1752374558124.555664 },
{"activeModifiers":[],"character":"D", "isARepeat":false,"processTimeMs": 137777015.0,"type":"keyDown", "unixTimeMs": 1752374558179.592285 },
{"activeModifiers":[],"character":"Space", "isARepeat":false,"processTimeMs": 137777031.0,"type":"keyUp", "unixTimeMs": 1752374558195.956787 },
{"activeModifiers":[],"character":"A", "isARepeat":false,"processTimeMs": 137777109.0,"type":"keyDown", "unixTimeMs": 1752374558279.774658 },
{"activeModifiers":[],"character":"D", "isARepeat":false,"processTimeMs": 137777140.0,"type":"keyUp", "unixTimeMs": 1752374558313.710693 },
{"activeModifiers":[],"character":"A", "isARepeat":false,"processTimeMs": 137777234.0,"type":"keyUp", "unixTimeMs": 1752374558397.605713 },
{"activeModifiers":[],"character":"O", "isARepeat":false,"processTimeMs": 137777234.0,"type":"keyDown", "unixTimeMs": 1752374558411.777100 },
{"activeModifiers":[],"character":"O", "isARepeat":false,"processTimeMs": 137777328.0,"type":"keyUp", "unixTimeMs": 1752374558502.706299 },
{"activeModifiers":[],"character":"D", "isARepeat":false,"processTimeMs": 137777328.0,"type":"keyDown", "unixTimeMs": 1752374558505.173096 },
{"activeModifiers":[],"character":"A", "isARepeat":false,"processTimeMs": 137777406.0,"type":"keyDown", "unixTimeMs": 1752374558575.446777 },
{"activeModifiers":[],"character":"D", "isARepeat":false,"processTimeMs": 137777437.0,"type":"keyUp", "unixTimeMs": 1752374558602.406738 },
{"activeModifiers":[],"character":"A", "isARepeat":false,"processTimeMs": 137777515.0,"type":"keyUp", "unixTimeMs": 1752374558683.084473 },
{"activeModifiers":[],"character":"Space", "isARepeat":false,"processTimeMs": 137777578.0,"type":"keyDown", "unixTimeMs": 1752374558747.790039 },
{"activeModifiers":[],"character":"D", "isARepeat":false,"processTimeMs": 137777640.0,"type":"keyDown", "unixTimeMs": 1752374558813.312012 },
{"activeModifiers":[],"character":"Space", "isARepeat":false,"processTimeMs": 137777671.0,"type":"keyUp", "unixTimeMs": 1752374558848.864990 },
{"activeModifiers":[],"character":"E", "isARepeat":false,"processTimeMs": 137777718.0,"type":"keyDown", "unixTimeMs": 1752374558892.283936 },
{"activeModifiers":[],"character":"D", "isARepeat":false,"processTimeMs": 137777765.0,"type":"keyUp", "unixTimeMs": 1752374558931.198730 },
{"activeModifiers":[],"character":"Space", "isARepeat":false,"processTimeMs": 137777781.0,"type":"keyDown", "unixTimeMs": 1752374558956.263916 },
{"activeModifiers":[],"character":"E", "isARepeat":false,"processTimeMs": 137777812.0,"type":"keyUp", "unixTimeMs": 1752374558988.665283 },
{"activeModifiers":[],"character":"Space", "isARepeat":false,"processTimeMs": 137777875.0,"type":"keyUp", "unixTimeMs": 1752374559047.686768 },
{"activeModifiers":[],"character":".", "isARepeat":false,"processTimeMs": 137778921.0,"type":"keyDown", "unixTimeMs": 1752374560098.699707 },
{"activeModifiers":[],"character":".", "isARepeat":false,"processTimeMs": 137779000.0,"type":"keyUp", "unixTimeMs": 1752374560163.631592 },
{"activeModifiers":[],"character":"W", "isARepeat":false,"processTimeMs": 137779671.0,"type":"keyDown", "unixTimeMs": 1752374560845.601562 },
{"activeModifiers":[],"character":"W", "isARepeat":false,"processTimeMs": 137779750.0,"type":"keyUp", "unixTimeMs": 1752374560921.021729 },
{"activeModifiers":[],"character":"O", "isARepeat":false,"processTimeMs": 137779781.0,"type":"keyDown", "unixTimeMs": 1752374560946.321777 },
{"activeModifiers":[],"character":"O", "isARepeat":false,"processTimeMs": 137779843.0,"type":"keyUp", "unixTimeMs": 1752374561013.740479 },
{"activeModifiers":[],"character":"Space", "isARepeat":false,"processTimeMs": 137779890.0,"type":"keyDown", "unixTimeMs": 1752374561053.989502 },
{"activeModifiers":[],"character":"Space", "isARepeat":false,"processTimeMs": 137779984.0,"type":"keyUp", "unixTimeMs": 1752374561154.543945 },
{"activeModifiers":[],"character":"X", "isARepeat":false,"processTimeMs": 137780187.0,"type":"keyDown", "unixTimeMs": 1752374561352.930908 },
{"activeModifiers":[],"character":"X", "isARepeat":false,"processTimeMs": 137780265.0,"type":"keyUp", "unixTimeMs": 1752374561438.736572 },
{"activeModifiers":[],"character":"U", "isARepeat":false,"processTimeMs": 137780281.0,"type":"keyDown", "unixTimeMs": 1752374561445.310059 },
{"activeModifiers":[],"character":"U", "isARepeat":false,"processTimeMs": 137780328.0,"type":"keyUp", "unixTimeMs": 1752374561498.317627 },
{"activeModifiers":[],"character":"Y", "isARepeat":false,"processTimeMs": 137780484.0,"type":"keyDown", "unixTimeMs": 1752374561649.167969 },
{"activeModifiers":[],"character":"Y", "isARepeat":false,"processTimeMs": 137780531.0,"type":"keyUp", "unixTimeMs": 1752374561702.315918 },
{"activeModifiers":[],"character":"A", "isARepeat":false,"processTimeMs": 137780562.0,"type":"keyDown", "unixTimeMs": 1752374561739.293945 },
{"activeModifiers":[],"character":"A", "isARepeat":false,"processTimeMs": 137780656.0,"type":"keyUp", "unixTimeMs": 1752374561819.323242 },
{"activeModifiers":[],"character":"O", "isARepeat":false,"processTimeMs": 137780765.0,"type":"keyDown", "unixTimeMs": 1752374561938.520020 },
{"activeModifiers":[],"character":"O", "isARepeat":false,"processTimeMs": 137780828.0,"type":"keyUp", "unixTimeMs": 1752374562003.632324 },
{"activeModifiers":[],"character":"Space", "isARepeat":false,"processTimeMs": 137780890.0,"type":"keyDown", "unixTimeMs": 1752374562057.964355 },
{"activeModifiers":[],"character":"Space", "isARepeat":false,"processTimeMs": 137780968.0,"type":"keyUp", "unixTimeMs": 1752374562145.699707 },
{"activeModifiers":[],"character":"Z", "isARepeat":false,"processTimeMs": 137781343.0,"type":"keyDown", "unixTimeMs": 1752374562519.406250 },
{"activeModifiers":[],"character":"H", "isARepeat":false,"processTimeMs": 137781421.0,"type":"keyDown", "unixTimeMs": 1752374562587.838867 },
{"activeModifiers":[],"character":"Z", "isARepeat":false,"processTimeMs": 137781437.0,"type":"keyUp", "unixTimeMs": 1752374562605.698486 },
{"activeModifiers":[],"character":"H", "isARepeat":false,"processTimeMs": 137781500.0,"type":"keyUp", "unixTimeMs": 1752374562663.572021 },
{"activeModifiers":[],"character":"E", "isARepeat":false,"processTimeMs": 137781578.0,"type":"keyDown", "unixTimeMs": 1752374562746.101318 },
{"activeModifiers":[],"character":"G", "isARepeat":false,"processTimeMs": 137781593.0,"type":"keyDown", "unixTimeMs": 1752374562770.252686 },
{"activeModifiers":[],"character":"E", "isARepeat":false,"processTimeMs": 137781640.0,"type":"keyUp", "unixTimeMs": 1752374562815.788330 },
{"activeModifiers":[],"character":"G", "isARepeat":false,"processTimeMs": 137781671.0,"type":"keyUp", "unixTimeMs": 1752374562837.887695 },
{"activeModifiers":[],"character":"E", "isARepeat":false,"processTimeMs": 137781734.0,"type":"keyDown", "unixTimeMs": 1752374562904.975098 },
{"activeModifiers":[],"character":"Space", "isARepeat":false,"processTimeMs": 137781765.0,"type":"keyDown", "unixTimeMs": 1752374562942.239990 },
{"activeModifiers":[],"character":"E", "isARepeat":false,"processTimeMs": 137781812.0,"type":"keyUp", "unixTimeMs": 1752374562979.976318 },
{"activeModifiers":[],"character":"Space", "isARepeat":false,"processTimeMs": 137781890.0,"type":"keyUp", "unixTimeMs": 1752374563061.791992 },
{"activeModifiers":[],"character":"C", "isARepeat":false,"processTimeMs": 137782078.0,"type":"keyDown", "unixTimeMs": 1752374563243.719482 },
{"activeModifiers":[],"character":"C", "isARepeat":false,"processTimeMs": 137782203.0,"type":"keyUp", "unixTimeMs": 1752374563372.063477 },
{"activeModifiers":[],"character":"H", "isARepeat":false,"processTimeMs": 137782218.0,"type":"keyDown", "unixTimeMs": 1752374563389.835205 },
{"activeModifiers":[],"character":"H", "isARepeat":false,"processTimeMs": 137782296.0,"type":"keyUp", "unixTimeMs": 1752374563463.343262 },
{"activeModifiers":[],"character":"E", "isARepeat":false,"processTimeMs": 137782312.0,"type":"keyDown", "unixTimeMs": 1752374563481.573242 },
{"activeModifiers":[],"character":"E", "isARepeat":false,"processTimeMs": 137782406.0,"type":"keyUp", "unixTimeMs": 1752374563579.302002 },
{"activeModifiers":[],"character":"N", "isARepeat":false,"processTimeMs": 137782421.0,"type":"keyDown", "unixTimeMs": 1752374563586.246094 },
{"activeModifiers":[],"character":"N", "isARepeat":false,"processTimeMs": 137782500.0,"type":"keyUp", "unixTimeMs": 1752374563668.500488 },
{"activeModifiers":[],"character":"G", "isARepeat":false,"processTimeMs": 137782515.0,"type":"keyDown", "unixTimeMs": 1752374563678.041260 },
{"activeModifiers":[],"character":"G", "isARepeat":false,"processTimeMs": 137782546.0,"type":"keyUp", "unixTimeMs": 1752374563719.072021 },
{"activeModifiers":[],"character":"S", "isARepeat":false,"processTimeMs": 137782671.0,"type":"keyDown", "unixTimeMs": 1752374563842.478760 },
{"activeModifiers":[],"character":"S", "isARepeat":false,"processTimeMs": 137782734.0,"type":"keyUp", "unixTimeMs": 1752374563910.467285 },
{"activeModifiers":[],"character":"H", "isARepeat":false,"processTimeMs": 137782750.0,"type":"keyDown", "unixTimeMs": 1752374563920.545166 },
{"activeModifiers":[],"character":"H", "isARepeat":false,"processTimeMs": 137782828.0,"type":"keyUp", "unixTimeMs": 1752374563999.051758 },
{"activeModifiers":[],"character":"I", "isARepeat":false,"processTimeMs": 137782937.0,"type":"keyDown", "unixTimeMs": 1752374564112.149658 },
{"activeModifiers":[],"character":"I", "isARepeat":false,"processTimeMs": 137783015.0,"type":"keyUp", "unixTimeMs": 1752374564185.752441 },
{"activeModifiers":[],"character":"Space", "isARepeat":false,"processTimeMs": 137783093.0,"type":"keyDown", "unixTimeMs": 1752374564260.503906 },
{"activeModifiers":[],"character":"Space", "isARepeat":false,"processTimeMs": 137783187.0,"type":"keyUp", "unixTimeMs": 1752374564352.954346 },
{"activeModifiers":[],"character":"Backspace", "isARepeat":false,"processTimeMs": 137784125.0,"type":"keyDown", "unixTimeMs": 1752374565297.914551 },
{"activeModifiers":[],"character":"Backspace", "isARepeat":false,"processTimeMs": 137784218.0,"type":"keyUp", "unixTimeMs": 1752374565394.911621 },
{"activeModifiers":[],"character":"Backspace", "isARepeat":false,"processTimeMs": 137784328.0,"type":"keyDown", "unixTimeMs": 1752374565491.222900 },
{"activeModifiers":[],"character":"Backspace", "isARepeat":false,"processTimeMs": 137784390.0,"type":"keyUp", "unixTimeMs": 1752374565560.356445 },
{"activeModifiers":[],"character":"Z", "isARepeat":false,"processTimeMs": 137785140.0,"type":"keyDown", "unixTimeMs": 1752374566307.081543 },
{"activeModifiers":[],"character":"Z", "isARepeat":false,"processTimeMs": 137785234.0,"type":"keyUp", "unixTimeMs": 1752374566402.218506 },
{"activeModifiers":[],"character":"U", "isARepeat":false,"processTimeMs": 137785562.0,"type":"keyDown", "unixTimeMs": 1752374566725.946777 },
{"activeModifiers":[],"character":"U", "isARepeat":false,"processTimeMs": 137785625.0,"type":"keyUp", "unixTimeMs": 1752374566796.093018 },
{"activeModifiers":[],"character":"I", "isARepeat":false,"processTimeMs": 137785750.0,"type":"keyDown", "unixTimeMs": 1752374566921.392822 },
{"activeModifiers":[],"character":"I", "isARepeat":false,"processTimeMs": 137785843.0,"type":"keyUp", "unixTimeMs": 1752374567020.316162 },
{"activeModifiers":[],"character":"T", "isARepeat":false,"processTimeMs": 137786281.0,"type":"keyDown", "unixTimeMs": 1752374567449.297119 },
{"activeModifiers":[],"character":"T", "isARepeat":false,"processTimeMs": 137786359.0,"type":"keyUp", "unixTimeMs": 1752374567521.630371 },
{"activeModifiers":[],"character":"Backspace", "isARepeat":false,"processTimeMs": 137786671.0,"type":"keyDown", "unixTimeMs": 1752374567837.840088 },
{"activeModifiers":[],"character":"Backspace", "isARepeat":false,"processTimeMs": 137786734.0,"type":"keyUp", "unixTimeMs": 1752374567897.722900 },
{"activeModifiers":[],"character":"Backspace", "isARepeat":false,"processTimeMs": 137786828.0,"type":"keyDown", "unixTimeMs": 1752374567997.871094 },
{"activeModifiers":[],"character":"Backspace", "isARepeat":false,"processTimeMs": 137786890.0,"type":"keyUp", "unixTimeMs": 1752374568062.581055 },
{"activeModifiers":[],"character":"Backspace", "isARepeat":false,"processTimeMs": 137787000.0,"type":"keyDown", "unixTimeMs": 1752374568169.022461 },
{"activeModifiers":[],"character":"Backspace", "isARepeat":false,"processTimeMs": 137787062.0,"type":"keyUp", "unixTimeMs": 1752374568230.399902 },
{"activeModifiers":[],"character":"Backspace", "isARepeat":false,"processTimeMs": 137787171.0,"type":"keyDown", "unixTimeMs": 1752374568346.550293 },
{"activeModifiers":[],"character":"Backspace", "isARepeat":false,"processTimeMs": 137787234.0,"type":"keyUp", "unixTimeMs": 1752374568406.898682 },
{"activeModifiers":[],"character":"Backspace", "isARepeat":false,"processTimeMs": 137787500.0,"type":"keyDown", "unixTimeMs": 1752374568670.214111 },
{"activeModifiers":[],"character":"Backspace", "isARepeat":false,"processTimeMs": 137787593.0,"type":"keyUp", "unixTimeMs": 1752374568762.672852 },
{"activeModifiers":[],"character":"Backspace", "isARepeat":false,"processTimeMs": 137787703.0,"type":"keyDown", "unixTimeMs": 1752374568869.775879 },
{"activeModifiers":[],"character":"Backspace", "isARepeat":false,"processTimeMs": 137787796.0,"type":"keyUp", "unixTimeMs": 1752374568963.406494 },
{"activeModifiers":[],"character":"Q", "isARepeat":false,"processTimeMs": 137788000.0,"type":"keyDown", "unixTimeMs": 1752374569177.462891 },
{"activeModifiers":[],"character":"I", "isARepeat":false,"processTimeMs": 137788093.0,"type":"keyDown", "unixTimeMs": 1752374569256.184082 },
{"activeModifiers":[],"character":"Q", "isARepeat":false,"processTimeMs": 137788093.0,"type":"keyUp", "unixTimeMs": 1752374569270.020508 },
{"activeModifiers":[],"character":"I", "isARepeat":false,"processTimeMs": 137788156.0,"type":"keyUp", "unixTimeMs": 1752374569330.332520 },
{"activeModifiers":[],"character":"Z", "isARepeat":false,"processTimeMs": 137788218.0,"type":"keyDown", "unixTimeMs": 1752374569392.603271 },
{"activeModifiers":[],"character":"Z", "isARepeat":false,"processTimeMs": 137788328.0,"type":"keyUp", "unixTimeMs": 1752374569492.732910 },
{"activeModifiers":[],"character":"H", "isARepeat":false,"processTimeMs": 137788328.0,"type":"keyDown", "unixTimeMs": 1752374569495.238770 },
{"activeModifiers":[],"character":"H", "isARepeat":false,"processTimeMs": 137788375.0,"type":"keyUp", "unixTimeMs": 1752374569540.152100 },
{"activeModifiers":[],"character":"O", "isARepeat":false,"processTimeMs": 137788484.0,"type":"keyDown", "unixTimeMs": 1752374569650.192871 },
{"activeModifiers":[],"character":"O", "isARepeat":false,"processTimeMs": 137788531.0,"type":"keyUp", "unixTimeMs": 1752374569703.378418 },
{"activeModifiers":[],"character":"N", "isARepeat":false,"processTimeMs": 137788625.0,"type":"keyDown", "unixTimeMs": 1752374569793.721436 },
{"activeModifiers":[],"character":"G", "isARepeat":false,"processTimeMs": 137788671.0,"type":"keyDown", "unixTimeMs": 1752374569838.190430 },
{"activeModifiers":[],"character":"Space", "isARepeat":false,"processTimeMs": 137788687.0,"type":"keyDown", "unixTimeMs": 1752374569862.953613 },
{"activeModifiers":[],"character":"N", "isARepeat":false,"processTimeMs": 137788718.0,"type":"keyUp", "unixTimeMs": 1752374569881.028320 },
{"activeModifiers":[],"character":"G", "isARepeat":false,"processTimeMs": 137788750.0,"type":"keyUp", "unixTimeMs": 1752374569925.739990 },
{"activeModifiers":[],"character":"Space", "isARepeat":false,"processTimeMs": 137788796.0,"type":"keyUp", "unixTimeMs": 1752374569962.785156 },
{"activeModifiers":[],"character":"Z", "isARepeat":false,"processTimeMs": 137788875.0,"type":"keyDown", "unixTimeMs": 1752374570046.871094 },
{"activeModifiers":[],"character":"U", "isARepeat":false,"processTimeMs": 137788937.0,"type":"keyDown", "unixTimeMs": 1752374570103.631348 },
{"activeModifiers":[],"character":"Z", "isARepeat":false,"processTimeMs": 137788953.0,"type":"keyUp", "unixTimeMs": 1752374570116.851562 },
{"activeModifiers":[],"character":"U", "isARepeat":false,"processTimeMs": 137789000.0,"type":"keyUp", "unixTimeMs": 1752374570174.464355 },
{"activeModifiers":[],"character":"I", "isARepeat":false,"processTimeMs": 137789093.0,"type":"keyDown", "unixTimeMs": 1752374570261.501953 },
{"activeModifiers":[],"character":"I", "isARepeat":false,"processTimeMs": 137789171.0,"type":"keyUp", "unixTimeMs": 1752374570342.990234 },
{"activeModifiers":[],"character":"T", "isARepeat":false,"processTimeMs": 137789359.0,"type":"keyDown", "unixTimeMs": 1752374570526.746338 },
{"activeModifiers":[],"character":"T", "isARepeat":false,"processTimeMs": 137789468.0,"type":"keyUp", "unixTimeMs": 1752374570635.737305 },
{"activeModifiers":[],"character":"U", "isARepeat":false,"processTimeMs": 137789593.0,"type":"keyDown", "unixTimeMs": 1752374570760.575195 },
{"activeModifiers":[],"character":"U", "isARepeat":false,"processTimeMs": 137789656.0,"type":"keyUp", "unixTimeMs": 1752374570833.152100 },
{"activeModifiers":[],"character":"I", "isARepeat":false,"processTimeMs": 137789765.0,"type":"keyDown", "unixTimeMs": 1752374570933.557617 },
{"activeModifiers":[],"character":"I", "isARepeat":false,"processTimeMs": 137789828.0,"type":"keyUp", "unixTimeMs": 1752374570994.964600 },
{"activeModifiers":[],"character":"J", "isARepeat":false,"processTimeMs": 137789921.0,"type":"keyDown", "unixTimeMs": 1752374571091.775146 },
{"activeModifiers":[],"character":"J", "isARepeat":false,"processTimeMs": 137789984.0,"type":"keyUp", "unixTimeMs": 1752374571152.355957 },
{"activeModifiers":[],"character":"I", "isARepeat":false,"processTimeMs": 137790046.0,"type":"keyDown", "unixTimeMs": 1752374571213.461426 },
{"activeModifiers":[],"character":"I", "isARepeat":false,"processTimeMs": 137790109.0,"type":"keyUp", "unixTimeMs": 1752374571285.348389 },
{"activeModifiers":[],"character":"A", "isARepeat":false,"processTimeMs": 137790125.0,"type":"keyDown", "unixTimeMs": 1752374571293.892334 },
{"activeModifiers":[],"character":"A", "isARepeat":false,"processTimeMs": 137790187.0,"type":"keyUp", "unixTimeMs": 1752374571355.342285 },
{"activeModifiers":[],"character":"N", "isARepeat":false,"processTimeMs": 137790203.0,"type":"keyDown", "unixTimeMs": 1752374571373.689697 },
{"activeModifiers":[],"character":"N", "isARepeat":false,"processTimeMs": 137790281.0,"type":"keyUp", "unixTimeMs": 1752374571456.477539 },
{"activeModifiers":[],"character":"Space", "isARepeat":false,"processTimeMs": 137790296.0,"type":"keyDown", "unixTimeMs": 1752374571467.552734 },
{"activeModifiers":[],"character":"C", "isARepeat":false,"processTimeMs": 137790390.0,"type":"keyDown", "unixTimeMs": 1752374571559.291260 },
{"activeModifiers":[],"character":"Space", "isARepeat":false,"processTimeMs": 137790406.0,"type":"keyUp", "unixTimeMs": 1752374571574.511475 },
{"activeModifiers":[],"character":"H", "isARepeat":false,"processTimeMs": 137790468.0,"type":"keyDown", "unixTimeMs": 1752374571636.716064 },
{"activeModifiers":[],"character":"C", "isARepeat":false,"processTimeMs": 137790484.0,"type":"keyUp", "unixTimeMs": 1752374571653.789551 },
{"activeModifiers":[],"character":"H", "isARepeat":false,"processTimeMs": 137790546.0,"type":"keyUp", "unixTimeMs": 1752374571722.599609 },
{"activeModifiers":[],"character":"E", "isARepeat":false,"processTimeMs": 137790593.0,"type":"keyDown", "unixTimeMs": 1752374571757.224365 },
{"activeModifiers":[],"character":"N", "isARepeat":false,"processTimeMs": 137790640.0,"type":"keyDown", "unixTimeMs": 1752374571812.199463 },
{"activeModifiers":[],"character":"E", "isARepeat":false,"processTimeMs": 137790671.0,"type":"keyUp", "unixTimeMs": 1752374571841.812500 },
{"activeModifiers":[],"character":"G", "isARepeat":false,"processTimeMs": 137790734.0,"type":"keyDown", "unixTimeMs": 1752374571901.513184 },
{"activeModifiers":[],"character":"N", "isARepeat":false,"processTimeMs": 137790750.0,"type":"keyUp", "unixTimeMs": 1752374571919.015381 },
{"activeModifiers":[],"character":"G", "isARepeat":false,"processTimeMs": 137790796.0,"type":"keyUp", "unixTimeMs": 1752374571970.399902 },
{"activeModifiers":[],"character":"S", "isARepeat":false,"processTimeMs": 137790937.0,"type":"keyDown", "unixTimeMs": 1752374572110.347412 },
{"activeModifiers":[],"character":"S", "isARepeat":false,"processTimeMs": 137791062.0,"type":"keyUp", "unixTimeMs": 1752374572231.798828 },
{"activeModifiers":[],"character":"H", "isARepeat":false,"processTimeMs": 137791078.0,"type":"keyDown", "unixTimeMs": 1752374572240.320312 },
{"activeModifiers":[],"character":"H", "isARepeat":false,"processTimeMs": 137791125.0,"type":"keyUp", "unixTimeMs": 1752374572299.661377 },
{"activeModifiers":[],"character":"I", "isARepeat":false,"processTimeMs": 137791203.0,"type":"keyDown", "unixTimeMs": 1752374572378.245117 },
{"activeModifiers":[],"character":"Space", "isARepeat":false,"processTimeMs": 137791265.0,"type":"keyDown", "unixTimeMs": 1752374572430.256836 },
{"activeModifiers":[],"character":"I", "isARepeat":false,"processTimeMs": 137791281.0,"type":"keyUp", "unixTimeMs": 1752374572450.350830 },
{"activeModifiers":[],"character":"D", "isARepeat":false,"processTimeMs": 137791328.0,"type":"keyDown", "unixTimeMs": 1752374572492.951660 },
{"activeModifiers":[],"character":"Space", "isARepeat":false,"processTimeMs": 137791359.0,"type":"keyUp", "unixTimeMs": 1752374572524.811279 },
{"activeModifiers":[],"character":"E", "isARepeat":false,"processTimeMs": 137791390.0,"type":"keyDown", "unixTimeMs": 1752374572556.932861 },
{"activeModifiers":[],"character":"D", "isARepeat":false,"processTimeMs": 137791437.0,"type":"keyUp", "unixTimeMs": 1752374572608.405762 },
{"activeModifiers":[],"character":"Space", "isARepeat":false,"processTimeMs": 137791453.0,"type":"keyDown", "unixTimeMs": 1752374572629.704346 },
{"activeModifiers":[],"character":"E", "isARepeat":false,"processTimeMs": 137791515.0,"type":"keyUp", "unixTimeMs": 1752374572686.104736 },
{"activeModifiers":[],"character":"Space", "isARepeat":false,"processTimeMs": 137791578.0,"type":"keyUp", "unixTimeMs": 1752374572746.452148 },
{"activeModifiers":[],"character":"Backspace", "isARepeat":false,"processTimeMs": 137792484.0,"type":"keyDown", "unixTimeMs": 1752374573659.143066 },
{"activeModifiers":[],"character":"Backspace", "isARepeat":false,"processTimeMs": 137792562.0,"type":"keyUp", "unixTimeMs": 1752374573731.735107 },
{"activeModifiers":[],"character":"2", "isARepeat":false,"processTimeMs": 137793515.0,"type":"keyDown", "unixTimeMs": 1752374574688.805908 },
{"activeModifiers":[],"character":"2", "isARepeat":false,"processTimeMs": 137793578.0,"type":"keyUp", "unixTimeMs": 1752374574749.468750 },
{"activeModifiers":[],"character":"T", "isARepeat":false,"processTimeMs": 137793796.0,"type":"keyDown", "unixTimeMs": 1752374574962.980469 },
{"activeModifiers":[],"character":"T", "isARepeat":false,"processTimeMs": 137793875.0,"type":"keyUp", "unixTimeMs": 1752374575045.586914 },
{"activeModifiers":[],"character":"I", "isARepeat":false,"processTimeMs": 137794015.0,"type":"keyDown", "unixTimeMs": 1752374575183.840332 },
{"activeModifiers":[],"character":"I", "isARepeat":false,"processTimeMs": 137794078.0,"type":"keyUp", "unixTimeMs": 1752374575252.259277 },
{"activeModifiers":[],"character":"A", "isARepeat":false,"processTimeMs": 137794093.0,"type":"keyDown", "unixTimeMs": 1752374575268.556885 },
{"activeModifiers":[],"character":"A", "isARepeat":false,"processTimeMs": 137794171.0,"type":"keyUp", "unixTimeMs": 1752374575348.601562 },
{"activeModifiers":[],"character":"N", "isARepeat":false,"processTimeMs": 137794203.0,"type":"keyDown", "unixTimeMs": 1752374575376.980469 },
{"activeModifiers":[],"character":"N", "isARepeat":false,"processTimeMs": 137794281.0,"type":"keyUp", "unixTimeMs": 1752374575450.986816 },
{"activeModifiers":[],"character":"Space", "isARepeat":false,"processTimeMs": 137794343.0,"type":"keyDown", "unixTimeMs": 1752374575517.498779 },
{"activeModifiers":[],"character":"Space", "isARepeat":false,"processTimeMs": 137794453.0,"type":"keyUp", "unixTimeMs": 1752374575625.824707 },
{"activeModifiers":[],"character":"D", "isARepeat":false,"processTimeMs": 137795625.0,"type":"keyDown", "unixTimeMs": 1752374576795.834717 },
{"activeModifiers":[],"character":"E", "isARepeat":false,"processTimeMs": 137795687.0,"type":"keyDown", "unixTimeMs": 1752374576864.784180 },
{"activeModifiers":[],"character":"D", "isARepeat":false,"processTimeMs": 137795750.0,"type":"keyUp", "unixTimeMs": 1752374576923.717285 },
{"activeModifiers":[],"character":"Space", "isARepeat":false,"processTimeMs": 137795781.0,"type":"keyDown", "unixTimeMs": 1752374576944.937500 },
{"activeModifiers":[],"character":"E", "isARepeat":false,"processTimeMs": 137795796.0,"type":"keyUp", "unixTimeMs": 1752374576971.951172 },
{"activeModifiers":[],"character":"Space", "isARepeat":false,"processTimeMs": 137795890.0,"type":"keyUp", "unixTimeMs": 1752374577055.525879 },
{"activeModifiers":[],"character":"L", "isARepeat":false,"processTimeMs": 137796421.0,"type":"keyDown", "unixTimeMs": 1752374577593.160889 },
{"activeModifiers":[],"character":"L", "isARepeat":false,"processTimeMs": 137796484.0,"type":"keyUp", "unixTimeMs": 1752374577656.339600 },
{"activeModifiers":[],"character":"V", "isARepeat":false,"processTimeMs": 137796687.0,"type":"keyDown", "unixTimeMs": 1752374577854.441162 },
{"activeModifiers":[],"character":"V", "isARepeat":false,"processTimeMs": 137796734.0,"type":"keyUp", "unixTimeMs": 1752374577904.543945 },
{"activeModifiers":[],"character":"Y", "isARepeat":false,"processTimeMs": 137796890.0,"type":"keyDown", "unixTimeMs": 1752374578054.629883 },
{"activeModifiers":[],"character":"Y", "isARepeat":false,"processTimeMs": 137796953.0,"type":"keyUp", "unixTimeMs": 1752374578125.992920 },
{"activeModifiers":[],"character":"O", "isARepeat":false,"processTimeMs": 137797093.0,"type":"keyDown", "unixTimeMs": 1752374578260.529541 },
{"activeModifiers":[],"character":"O", "isARepeat":false,"processTimeMs": 137797140.0,"type":"keyUp", "unixTimeMs": 1752374578313.412354 },
{"activeModifiers":[],"character":"U", "isARepeat":false,"processTimeMs": 137797234.0,"type":"keyDown", "unixTimeMs": 1752374578407.315918 },
{"activeModifiers":[],"character":"U", "isARepeat":false,"processTimeMs": 137797312.0,"type":"keyUp", "unixTimeMs": 1752374578479.514404 },
{"activeModifiers":[],"character":"U", "isARepeat":false,"processTimeMs": 137797671.0,"type":"keyDown", "unixTimeMs": 1752374578836.527100 },
{"activeModifiers":[],"character":"U", "isARepeat":false,"processTimeMs": 137797718.0,"type":"keyUp", "unixTimeMs": 1752374578881.376221 },
{"activeModifiers":[],"character":"Backspace", "isARepeat":false,"processTimeMs": 137798031.0,"type":"keyDown", "unixTimeMs": 1752374579206.633545 },
{"activeModifiers":[],"character":"Backspace", "isARepeat":false,"processTimeMs": 137798125.0,"type":"keyUp", "unixTimeMs": 1752374579291.818359 },
{"activeModifiers":[],"character":"G", "isARepeat":false,"processTimeMs": 137798343.0,"type":"keyDown", "unixTimeMs": 1752374579513.001709 },
{"activeModifiers":[],"character":"G", "isARepeat":false,"processTimeMs": 137798421.0,"type":"keyUp", "unixTimeMs": 1752374579587.038330 },
{"activeModifiers":[],"character":"U", "isARepeat":false,"processTimeMs": 137798531.0,"type":"keyDown", "unixTimeMs": 1752374579705.023682 },
{"activeModifiers":[],"character":"U", "isARepeat":false,"processTimeMs": 137798593.0,"type":"keyUp", "unixTimeMs": 1752374579769.299072 },
{"activeModifiers":[],"character":"I", "isARepeat":false,"processTimeMs": 137798703.0,"type":"keyDown", "unixTimeMs": 1752374579874.572754 },
{"activeModifiers":[],"character":"I", "isARepeat":false,"processTimeMs": 137798765.0,"type":"keyUp", "unixTimeMs": 1752374579935.678711 },
{"activeModifiers":[],"character":"H", "isARepeat":false,"processTimeMs": 137798875.0,"type":"keyDown", "unixTimeMs": 1752374580047.093506 },
{"activeModifiers":[],"character":"H", "isARepeat":false,"processTimeMs": 137798937.0,"type":"keyUp", "unixTimeMs": 1752374580103.896729 },
{"activeModifiers":[],"character":"U", "isARepeat":false,"processTimeMs": 137799000.0,"type":"keyDown", "unixTimeMs": 1752374580169.031494 },
{"activeModifiers":[],"character":"A", "isARepeat":false,"processTimeMs": 137799062.0,"type":"keyDown", "unixTimeMs": 1752374580237.449951 },
{"activeModifiers":[],"character":"U", "isARepeat":false,"processTimeMs": 137799078.0,"type":"keyUp", "unixTimeMs": 1752374580253.078857 },
{"activeModifiers":[],"character":"A", "isARepeat":false,"processTimeMs": 137799140.0,"type":"keyUp", "unixTimeMs": 1752374580309.187256 },
{"activeModifiers":[],"character":"Space", "isARepeat":false,"processTimeMs": 137799406.0,"type":"keyDown", "unixTimeMs": 1752374580575.881104 },
{"activeModifiers":[],"character":"Space", "isARepeat":false,"processTimeMs": 137799500.0,"type":"keyUp", "unixTimeMs": 1752374580665.080322 },
{"activeModifiers":[],"character":",", "isARepeat":false,"processTimeMs": 137800359.0,"type":"keyDown", "unixTimeMs": 1752374581527.436279 },
{"activeModifiers":[],"character":",", "isARepeat":false,"processTimeMs": 137800437.0,"type":"keyUp", "unixTimeMs": 1752374581600.226318 },
{"activeModifiers":[],"character":"Y", "isARepeat":false,"processTimeMs": 137800609.0,"type":"keyDown", "unixTimeMs": 1752374581780.010498 },
{"activeModifiers":[],"character":"Y", "isARepeat":false,"processTimeMs": 137800687.0,"type":"keyUp", "unixTimeMs": 1752374581854.810791 },
{"activeModifiers":[],"character":"I", "isARepeat":false,"processTimeMs": 137800796.0,"type":"keyDown", "unixTimeMs": 1752374581966.148193 },
{"activeModifiers":[],"character":"I", "isARepeat":false,"processTimeMs": 137800875.0,"type":"keyUp", "unixTimeMs": 1752374582038.749268 },
{"activeModifiers":[],"character":"J", "isARepeat":false,"processTimeMs": 137800953.0,"type":"keyDown", "unixTimeMs": 1752374582126.988037 },
{"activeModifiers":[],"character":"J", "isARepeat":false,"processTimeMs": 137801015.0,"type":"keyUp", "unixTimeMs": 1752374582183.823242 },
{"activeModifiers":[],"character":"I", "isARepeat":false,"processTimeMs": 137801078.0,"type":"keyDown", "unixTimeMs": 1752374582249.138916 },
{"activeModifiers":[],"character":"I", "isARepeat":false,"processTimeMs": 137801156.0,"type":"keyUp", "unixTimeMs": 1752374582318.945312 },
{"activeModifiers":[],"character":"Space", "isARepeat":false,"processTimeMs": 137801171.0,"type":"keyDown", "unixTimeMs": 1752374582341.919922 },
{"activeModifiers":[],"character":"Space", "isARepeat":false,"processTimeMs": 137801250.0,"type":"keyUp", "unixTimeMs": 1752374582427.047852 },
{"activeModifiers":[],"character":"C", "isARepeat":false,"processTimeMs": 137801843.0,"type":"keyDown", "unixTimeMs": 1752374583021.247559 },
{"activeModifiers":[],"character":"C", "isARepeat":false,"processTimeMs": 137801968.0,"type":"keyUp", "unixTimeMs": 1752374583133.511230 },
{"activeModifiers":[],"character":"O", "isARepeat":false,"processTimeMs": 137801968.0,"type":"keyDown", "unixTimeMs": 1752374583136.005127 },
{"activeModifiers":[],"character":"O", "isARepeat":false,"processTimeMs": 137802031.0,"type":"keyUp", "unixTimeMs": 1752374583201.286133 },
{"activeModifiers":[],"character":"N", "isARepeat":false,"processTimeMs": 137802140.0,"type":"keyDown", "unixTimeMs": 1752374583308.298096 },
{"activeModifiers":[],"character":"G", "isARepeat":false,"processTimeMs": 137802218.0,"type":"keyDown", "unixTimeMs": 1752374583386.273926 },
{"activeModifiers":[],"character":"N", "isARepeat":false,"processTimeMs": 137802218.0,"type":"keyUp", "unixTimeMs": 1752374583396.252441 },
{"activeModifiers":[],"character":"Space", "isARepeat":false,"processTimeMs": 137802265.0,"type":"keyDown", "unixTimeMs": 1752374583440.754395 },
{"activeModifiers":[],"character":"G", "isARepeat":false,"processTimeMs": 137802296.0,"type":"keyUp", "unixTimeMs": 1752374583468.931641 },
{"activeModifiers":[],"character":"Space", "isARepeat":false,"processTimeMs": 137802343.0,"type":"keyUp", "unixTimeMs": 1752374583513.618652 },
{"activeModifiers":[],"character":"H", "isARepeat":false,"processTimeMs": 137802421.0,"type":"keyDown", "unixTimeMs": 1752374583585.892090 },
{"activeModifiers":[],"character":"H", "isARepeat":false,"processTimeMs": 137802484.0,"type":"keyUp", "unixTimeMs": 1752374583657.162109 },
{"activeModifiers":[],"character":"A", "isARepeat":false,"processTimeMs": 137802718.0,"type":"keyDown", "unixTimeMs": 1752374583884.398438 },
{"activeModifiers":[],"character":"A", "isARepeat":false,"processTimeMs": 137802828.0,"type":"keyUp", "unixTimeMs": 1752374584003.736328 },
{"activeModifiers":[],"character":"O", "isARepeat":false,"processTimeMs": 137802906.0,"type":"keyDown", "unixTimeMs": 1752374584078.338867 },
{"activeModifiers":[],"character":"O", "isARepeat":false,"processTimeMs": 137802968.0,"type":"keyUp", "unixTimeMs": 1752374584137.695557 },
{"activeModifiers":[],"character":"Backspace", "isARepeat":false,"processTimeMs": 137803281.0,"type":"keyDown", "unixTimeMs": 1752374584444.251221 },
{"activeModifiers":[],"character":"Backspace", "isARepeat":false,"processTimeMs": 137803359.0,"type":"keyUp", "unixTimeMs": 1752374584532.390625 },
{"activeModifiers":[],"character":"N", "isARepeat":false,"processTimeMs": 137803531.0,"type":"keyDown", "unixTimeMs": 1752374584697.342285 },
{"activeModifiers":[],"character":"N", "isARepeat":false,"processTimeMs": 137803609.0,"type":"keyUp", "unixTimeMs": 1752374584776.514648 },
{"activeModifiers":[],"character":"G", "isARepeat":false,"processTimeMs": 137803640.0,"type":"keyDown", "unixTimeMs": 1752374584817.341553 },
{"activeModifiers":[],"character":"G", "isARepeat":false,"processTimeMs": 137803718.0,"type":"keyUp", "unixTimeMs": 1752374584887.731689 },
{"activeModifiers":[],"character":"Z", "isARepeat":false,"processTimeMs": 137803859.0,"type":"keyDown", "unixTimeMs": 1752374585023.041260 },
{"activeModifiers":[],"character":"H", "isARepeat":false,"processTimeMs": 137803890.0,"type":"keyDown", "unixTimeMs": 1752374585060.513672 },
{"activeModifiers":[],"character":"Z", "isARepeat":false,"processTimeMs": 137803921.0,"type":"keyUp", "unixTimeMs": 1752374585097.840088 },
{"activeModifiers":[],"character":"H", "isARepeat":false,"processTimeMs": 137803968.0,"type":"keyUp", "unixTimeMs": 1752374585133.826904 },
{"activeModifiers":[],"character":"O", "isARepeat":false,"processTimeMs": 137804062.0,"type":"keyDown", "unixTimeMs": 1752374585237.365479 },
{"activeModifiers":[],"character":"O", "isARepeat":false,"processTimeMs": 137804125.0,"type":"keyUp", "unixTimeMs": 1752374585292.240479 },
{"activeModifiers":[],"character":"U", "isARepeat":false,"processTimeMs": 137804218.0,"type":"keyDown", "unixTimeMs": 1752374585390.092285 },
{"activeModifiers":[],"character":"Space", "isARepeat":false,"processTimeMs": 137804265.0,"type":"keyDown", "unixTimeMs": 1752374585429.687256 },
{"activeModifiers":[],"character":"U", "isARepeat":false,"processTimeMs": 137804296.0,"type":"keyUp", "unixTimeMs": 1752374585469.078369 },
{"activeModifiers":[],"character":"D", "isARepeat":false,"processTimeMs": 137804328.0,"type":"keyDown", "unixTimeMs": 1752374585502.821289 },
{"activeModifiers":[],"character":"Space", "isARepeat":false,"processTimeMs": 137804390.0,"type":"keyUp", "unixTimeMs": 1752374585555.560791 },
{"activeModifiers":[],"character":"A", "isARepeat":false,"processTimeMs": 137804437.0,"type":"keyDown", "unixTimeMs": 1752374585613.360840 },
{"activeModifiers":[],"character":"D", "isARepeat":false,"processTimeMs": 137804484.0,"type":"keyUp", "unixTimeMs": 1752374585650.292969 },
{"activeModifiers":[],"character":"A", "isARepeat":false,"processTimeMs": 137804546.0,"type":"keyUp", "unixTimeMs": 1752374585717.349609 },
{"activeModifiers":[],"character":"O", "isARepeat":false,"processTimeMs": 137804562.0,"type":"keyDown", "unixTimeMs": 1752374585727.899902 },
{"activeModifiers":[],"character":"O", "isARepeat":false,"processTimeMs": 137804640.0,"type":"keyUp", "unixTimeMs": 1752374585806.034424 },
{"activeModifiers":[],"character":"Space", "isARepeat":false,"processTimeMs": 137804640.0,"type":"keyDown", "unixTimeMs": 1752374585809.416504 },
{"activeModifiers":[],"character":"Space", "isARepeat":false,"processTimeMs": 137804750.0,"type":"keyUp", "unixTimeMs": 1752374585922.814697 },
{"activeModifiers":[],"character":"Z", "isARepeat":false,"processTimeMs": 137804765.0,"type":"keyDown", "unixTimeMs": 1752374585930.794434 },
{"activeModifiers":[],"character":"Z", "isARepeat":false,"processTimeMs": 137804843.0,"type":"keyUp", "unixTimeMs": 1752374586018.373535 },
{"activeModifiers":[],"character":"H", "isARepeat":false,"processTimeMs": 137804859.0,"type":"keyDown", "unixTimeMs": 1752374586027.394287 },
{"activeModifiers":[],"character":"H", "isARepeat":false,"processTimeMs": 137804921.0,"type":"keyUp", "unixTimeMs": 1752374586087.604492 },
{"activeModifiers":[],"character":"E", "isARepeat":false,"processTimeMs": 137804937.0,"type":"keyDown", "unixTimeMs": 1752374586106.184570 },
{"activeModifiers":[],"character":"E", "isARepeat":false,"processTimeMs": 137805015.0,"type":"keyUp", "unixTimeMs": 1752374586187.829590 },
{"activeModifiers":[],"character":"G", "isARepeat":false,"processTimeMs": 137805031.0,"type":"keyDown", "unixTimeMs": 1752374586199.811279 },
{"activeModifiers":[],"character":"E", "isARepeat":false,"processTimeMs": 137805093.0,"type":"keyDown", "unixTimeMs": 1752374586268.290283 },
{"activeModifiers":[],"character":"G", "isARepeat":false,"processTimeMs": 137805125.0,"type":"keyUp", "unixTimeMs": 1752374586293.441162 },
{"activeModifiers":[],"character":"Space", "isARepeat":false,"processTimeMs": 137805187.0,"type":"keyDown", "unixTimeMs": 1752374586356.343018 },
{"activeModifiers":[],"character":"E", "isARepeat":false,"processTimeMs": 137805203.0,"type":"keyUp", "unixTimeMs": 1752374586374.003174 },
{"activeModifiers":[],"character":"Space", "isARepeat":false,"processTimeMs": 137805296.0,"type":"keyUp", "unixTimeMs": 1752374586464.494873 },
{"activeModifiers":[],"character":"C", "isARepeat":false,"processTimeMs": 137805687.0,"type":"keyDown", "unixTimeMs": 1752374586860.795898 },
{"activeModifiers":[],"character":"H", "isARepeat":false,"processTimeMs": 137805734.0,"type":"keyDown", "unixTimeMs": 1752374586908.781982 },
{"activeModifiers":[],"character":"C", "isARepeat":false,"processTimeMs": 137805781.0,"type":"keyUp", "unixTimeMs": 1752374586957.242188 },
{"activeModifiers":[],"character":"H", "isARepeat":false,"processTimeMs": 137805812.0,"type":"keyUp", "unixTimeMs": 1752374586981.926270 },
{"activeModifiers":[],"character":"E", "isARepeat":false,"processTimeMs": 137805843.0,"type":"keyDown", "unixTimeMs": 1752374587010.426514 },
{"activeModifiers":[],"character":"N", "isARepeat":false,"processTimeMs": 137805921.0,"type":"keyDown", "unixTimeMs": 1752374587091.083740 },
{"activeModifiers":[],"character":"E", "isARepeat":false,"processTimeMs": 137805937.0,"type":"keyUp", "unixTimeMs": 1752374587104.443115 },
{"activeModifiers":[],"character":"G", "isARepeat":false,"processTimeMs": 137805984.0,"type":"keyDown", "unixTimeMs": 1752374587153.377930 },
{"activeModifiers":[],"character":"N", "isARepeat":false,"processTimeMs": 137806000.0,"type":"keyUp", "unixTimeMs": 1752374587177.640381 },
{"activeModifiers":[],"character":"G", "isARepeat":false,"processTimeMs": 137806062.0,"type":"keyUp", "unixTimeMs": 1752374587236.978516 },
{"activeModifiers":[],"character":"S", "isARepeat":false,"processTimeMs": 137806140.0,"type":"keyDown", "unixTimeMs": 1752374587317.462891 },
{"activeModifiers":[],"character":"H", "isARepeat":false,"processTimeMs": 137806218.0,"type":"keyDown", "unixTimeMs": 1752374587385.781494 },
{"activeModifiers":[],"character":"S", "isARepeat":false,"processTimeMs": 137806234.0,"type":"keyUp", "unixTimeMs": 1752374587402.529297 },
{"activeModifiers":[],"character":"H", "isARepeat":false,"processTimeMs": 137806281.0,"type":"keyUp", "unixTimeMs": 1752374587449.485840 },
{"activeModifiers":[],"character":"I", "isARepeat":false,"processTimeMs": 137806375.0,"type":"keyDown", "unixTimeMs": 1752374587542.633301 },
{"activeModifiers":[],"character":"I", "isARepeat":false,"processTimeMs": 137806437.0,"type":"keyUp", "unixTimeMs": 1752374587607.449219 },
{"activeModifiers":[],"character":"Space", "isARepeat":false,"processTimeMs": 137806453.0,"type":"keyDown", "unixTimeMs": 1752374587617.720703 },
{"activeModifiers":[],"character":"Space", "isARepeat":false,"processTimeMs": 137806546.0,"type":"keyUp", "unixTimeMs": 1752374587714.871582 },
{"activeModifiers":[],"character":"D", "isARepeat":false,"processTimeMs": 137806671.0,"type":"keyDown", "unixTimeMs": 1752374587838.730957 },
{"activeModifiers":[],"character":"E", "isARepeat":false,"processTimeMs": 137806718.0,"type":"keyDown", "unixTimeMs": 1752374587885.982910 },
{"activeModifiers":[],"character":"D", "isARepeat":false,"processTimeMs": 137806781.0,"type":"keyUp", "unixTimeMs": 1752374587953.112305 },
{"activeModifiers":[],"character":"Space", "isARepeat":false,"processTimeMs": 137806796.0,"type":"keyDown", "unixTimeMs": 1752374587967.909180 },
{"activeModifiers":[],"character":"E", "isARepeat":false,"processTimeMs": 137806828.0,"type":"keyUp", "unixTimeMs": 1752374588001.390625 },
{"activeModifiers":[],"character":"Space", "isARepeat":false,"processTimeMs": 137806906.0,"type":"keyUp", "unixTimeMs": 1752374588073.293701 },
{"activeModifiers":[],"character":"H", "isARepeat":false,"processTimeMs": 137807406.0,"type":"keyDown", "unixTimeMs": 1752374588578.489990 },
{"activeModifiers":[],"character":"H", "isARepeat":false,"processTimeMs": 137807484.0,"type":"keyUp", "unixTimeMs": 1752374588655.060303 },
{"activeModifiers":[],"character":"U", "isARepeat":false,"processTimeMs": 137807562.0,"type":"keyDown", "unixTimeMs": 1752374588737.121094 },
{"activeModifiers":[],"character":"U", "isARepeat":false,"processTimeMs": 137807640.0,"type":"keyUp", "unixTimeMs": 1752374588815.075928 },
{"activeModifiers":[],"character":"O", "isARepeat":false,"processTimeMs": 137807718.0,"type":"keyDown", "unixTimeMs": 1752374588896.239502 },
{"activeModifiers":[],"character":"C", "isARepeat":false,"processTimeMs": 137807765.0,"type":"keyDown", "unixTimeMs": 1752374588941.677979 },
{"activeModifiers":[],"character":"O", "isARepeat":false,"processTimeMs": 137807796.0,"type":"keyUp", "unixTimeMs": 1752374588969.565186 },
{"activeModifiers":[],"character":"C", "isARepeat":false,"processTimeMs": 137807890.0,"type":"keyUp", "unixTimeMs": 1752374589056.088379 },
{"activeModifiers":[],"character":"H", "isARepeat":false,"processTimeMs": 137807921.0,"type":"keyDown", "unixTimeMs": 1752374589086.492920 },
{"activeModifiers":[],"character":"E", "isARepeat":false,"processTimeMs": 137807968.0,"type":"keyDown", "unixTimeMs": 1752374589139.863281 },
{"activeModifiers":[],"character":"H", "isARepeat":false,"processTimeMs": 137807984.0,"type":"keyUp", "unixTimeMs": 1752374589153.709473 },
{"activeModifiers":[],"character":"E", "isARepeat":false,"processTimeMs": 137808046.0,"type":"keyUp", "unixTimeMs": 1752374589223.491699 },
{"activeModifiers":[],"character":"Space", "isARepeat":false,"processTimeMs": 137808375.0,"type":"keyDown", "unixTimeMs": 1752374589541.196045 },
{"activeModifiers":[],"character":"Space", "isARepeat":false,"processTimeMs": 137808484.0,"type":"keyUp", "unixTimeMs": 1752374589631.241211 },
{"activeModifiers":[],"character":"P", "isARepeat":false,"processTimeMs": 137814859.0,"type":"keyDown", "unixTimeMs": 1752374596029.983643 },
{"activeModifiers":[],"character":"P", "isARepeat":false,"processTimeMs": 137814921.0,"type":"keyUp", "unixTimeMs": 1752374596095.422119 },
{"activeModifiers":[],"character":"I", "isARepeat":false,"processTimeMs": 137815062.0,"type":"keyDown", "unixTimeMs": 1752374596227.205811 },
{"activeModifiers":[],"character":"I", "isARepeat":false,"processTimeMs": 137815140.0,"type":"keyUp", "unixTimeMs": 1752374596303.756348 },
{"activeModifiers":[],"character":"A", "isARepeat":false,"processTimeMs": 137815140.0,"type":"keyDown", "unixTimeMs": 1752374596313.041260 },
{"activeModifiers":[],"character":"A", "isARepeat":false,"processTimeMs": 137815234.0,"type":"keyUp", "unixTimeMs": 1752374596407.265625 },
{"activeModifiers":[],"character":"O", "isARepeat":false,"processTimeMs": 137815250.0,"type":"keyDown", "unixTimeMs": 1752374596418.014648 },
{"activeModifiers":[],"character":"O", "isARepeat":false,"processTimeMs": 137815328.0,"type":"keyUp", "unixTimeMs": 1752374596491.112305 },
{"activeModifiers":[],"character":"Space", "isARepeat":false,"processTimeMs": 137815343.0,"type":"keyDown", "unixTimeMs": 1752374596509.614014 },
{"activeModifiers":[],"character":"Space", "isARepeat":false,"processTimeMs": 137815468.0,"type":"keyUp", "unixTimeMs": 1752374596632.566895 },
{"activeModifiers":[],"character":".", "isARepeat":false,"processTimeMs": 137820921.0,"type":"keyDown", "unixTimeMs": 1752374602092.112061 },
{"activeModifiers":[],"character":".", "isARepeat":false,"processTimeMs": 137821000.0,"type":"keyUp", "unixTimeMs": 1752374602169.127686 },
{"activeModifiers":[],"character":"Enter", "isARepeat":false,"processTimeMs": 137822171.0,"type":"keyDown", "unixTimeMs": 1752374603340.075928 },
{"activeModifiers":[],"character":"Enter", "isARepeat":false,"processTimeMs": 137822234.0,"type":"keyUp", "unixTimeMs": 1752374603404.430664 },
{"activeModifiers":[],"character":"Enter", "isARepeat":false,"processTimeMs": 137859406.0,"type":"keyDown", "unixTimeMs": 1752374640573.606689 },
{"activeModifiers":[],"character":"Enter", "isARepeat":false,"processTimeMs": 137859468.0,"type":"keyUp", "unixTimeMs": 1752374640644.516113 },
{"activeModifiers":[],"character":"Enter", "isARepeat":false,"processTimeMs": 137867921.0,"type":"keyDown", "unixTimeMs": 1752374649096.048096 },
{"activeModifiers":[],"character":"Enter", "isARepeat":false,"processTimeMs": 137867968.0,"type":"keyUp", "unixTimeMs": 1752374649141.604980 },
]