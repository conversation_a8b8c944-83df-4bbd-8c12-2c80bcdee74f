@import '_content/SF.Biz.AdminConsole.FrontEnd/SF.Biz.AdminConsole.FrontEnd.2p3zb2cryj.bundle.scp.css';
@import '_content/SF.Biz.UIComponents/SF.Biz.UIComponents.ps4k54vapv.bundle.scp.css';
@import '_content/SF.Sys.RazorControls/SF.Sys.RazorControls.ter6k28rqf.bundle.scp.css';
@import '_content/SF.Sys.UIComponents.AutoUI/SF.Sys.UIComponents.AutoUI.ff963smbbe.bundle.scp.css';

/* _content/CMP.Server/Components/Pages/Users/<USER>/Login.razor.rz.scp.css */
/* _content/CMP.Server/Components/Pages/Users/<USER>/Bind.razor.rz.scp.css */
.phonebind_0[b-06855q50jj] {
    position: absolute;
    width: 390px;
    height: 330px;
    border-radius: 10px;
    opacity: 1;
    background: #141414;
    text-align:center;
}

.phonebind_0_1[b-06855q50jj] {
    position: absolute;
    width:100%;
    top:40px;
}

.phonebind[b-06855q50jj] {
    position: absolute;
    display: flex;
    justify-content: center;
    align-items: center;
    top: 0px;
    bottom: 0px;
    left: 0px;
    right: 0px;
}

.phonebind_1[b-06855q50jj] {
    position: absolute;
    top:30px;
    left:25px;
}
/* _content/CMP.Server/Layouts/ConsoleUserLayout.razor.rz.scp.css */

.userlayout-background[b-e31jpbqci5] {
    position: absolute;
    width: 100%;
    height: 100%;
    opacity: 1;
    background-image: url("/images/backlogin_back.png");
    background-repeat: repeat;
    background-size: 100% 100%;
}

.userlayout_1[b-e31jpbqci5] {
    position: absolute;
    width: 100%;
    height: 100%;
    opacity: 1;
}

.userlayout_2[b-e31jpbqci5] {
    position: absolute;
    top: 40px;
    bottom: 0px;
    left: 0px;
    right: 0px;
}

.userlayout_3[b-e31jpbqci5] {
    position: absolute;
    left: 13px;
    top: 13px;
    width: 22px;
    height: 22px;
    opacity: 1;
}

    .userlayout_3 img[b-e31jpbqci5] {
        width: 22px;
        height: 22px;
    }

.userlayout_4[b-e31jpbqci5] {
    position: absolute;
    left: 41px;
    top: 13px;
    width: 200px;
    height: 28px;
    opacity: 1;
    color: black;
    font-size: 12px;
    font-family: PingFang SC;
    margin-top: 2px;
}

.userlayout_5[b-e31jpbqci5] {
    font-family: PingFang SC;
    font-weight: 500;
    font-size: 12px;
    color: #FFFFFF
}

.userlayout_6[b-e31jpbqci5] {
    font-family: PingFang SC;
    font-weight: 300;
    font-size: 10px;
    color: #5A5A5A
}

.userlayout_7[b-e31jpbqci5] {
    position: absolute;
    left: 13px;
    top: 13px;
    opacity: 1;
}

.userlayout_8[b-e31jpbqci5] {
    position: absolute;
    right: 20px;
    top: 12px;
    width: 120px;
    height: 24px;
    opacity: 1;
}

.userlayout_9[b-e31jpbqci5] {
    position: absolute;
    left: 31px;
    width: 24px;
    height: 24px;
    border-radius: 4px;
    opacity: 1;
    background: #262626;
}

.userlayout_8 img[b-e31jpbqci5] {
    position: absolute;
    top: 3px;
    width: 52px;
    height: 18px;
    opacity: 1;
}
/* _content/CMP.Server/Layouts/EmptyLayout.razor.rz.scp.css */
body[b-dd8uaux4sn]
{
}
