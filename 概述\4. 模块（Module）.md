[@Module()](https://) 装饰器采用单个对象，其属性描述模块：

|属性名称        ||
|--|--|
|[providers](https://)|将由 Nest 注入器实例化并且至少可以在该模块中共享的提供程序|
|[controllers](https://)|此模块中定义的必须实例化的控制器集|
|[imports](https://)|导出此模块所需的提供程序的导入模块列表|
|[exports](https://)|这个模块提供的 [providers](https://) 的子集应该在导入这个模块的其他模块中可用。你可以使用提供器本身或仅使用其令牌（[provide](https://) 值）|

```typescript
/* cats.service.ts */
// 定义一个通用的 service，用于后续使用
import { Injectable } from '@nestjs/common';

@Injectable()
export class CatsService {
  private readonly cats = ['cat1', 'cat2'];

  findAll(): string[] {
    return this.cats;
  }
}
```

<br/>

## 1. 功能模块

```typescript
/* cats.module.ts */
import { Module } from '@nestjs/common';
import { CatsService } from './cats.service';
import { CatsController } from './cats.controller';

// 这里相当于是将同一类功能的接口和实现方法放在一块
// 比如支付模块，到后续需要使用的时候，直接将这个模块导入
// 就不需要在重新实现代码了
@Module({
  // 导入与之相关的 Controller
  controllers: [CatsController],
  // 导入与之相关的 提供者
  providers: [CatsService],
})
export class CatsModule {}
```

> 【注】：只创建模块可以使用 `nest g module cats` 去实现
