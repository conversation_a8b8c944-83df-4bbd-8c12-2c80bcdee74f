## 1. 项目准备

```powershell
# 安装 NestJs 相关工具
npm i -g @nestjs/cli

# 创建一个 NestJs 项目
nest new <项目名称>

# 运行项目（我这里使用的是 pnpm）
pnpm run start
```

## 2. 代码结构

```powershell
# 目录结构
src
- app.controller.spec.ts   # 控制器的单元测试
- app.controller.ts        # 具有单一路由的基本控制器
- app.module.ts            # 应用的根模块
- app.service.ts           # 具有单一方法的基本服务
- main.ts                  # 使用核心函数 NestFactory 创建 Nest 应用实例的应用入口文件
```

> 【注】：这个整体结构有点类似于传统的 mvc 结构，
> 
> - **Controller**：处理 HTTP 请求，解析请求参数，调用 Service，返回响应。（只解析数据，服务调用，不做业务逻辑）
> - **Service**：处理业务逻辑，与数据层交互。

```typescript
/* main.ts */
import { NestFactory } from '@nestjs/core';
import { AppModule } from './app.module';

async function bootstrap() {
  // 创建应用实例
  const app = await NestFactory.create(AppModule);
  
  // 监听 3000 端口
  await app.listen(3000);
}
bootstrap();
```

> 【注】:这里默认使用的是 Express 内核，需要高性能可以换 Fastify 。
> 
> `const app = await NestFactory.create<NestExpressApplication>(AppModule);`
> 
> 除非你确实想要访问底层平台 API，否则**不需要**指定类型。