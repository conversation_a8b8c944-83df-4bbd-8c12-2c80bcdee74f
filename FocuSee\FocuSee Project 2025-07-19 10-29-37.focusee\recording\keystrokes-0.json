[

{"activeModifiers":[],"character":"S", "isARepeat":false,"processTimeMs": 655464562.0,"type":"keyDown", "unixTimeMs": 1752892241564.798096 },
{"activeModifiers":[],"character":"S", "isARepeat":false,"processTimeMs": 655464625.0,"type":"keyUp", "unixTimeMs": 1752892241636.958496 },
{"activeModifiers":[],"character":"H", "isARepeat":false,"processTimeMs": 655464625.0,"type":"keyDown", "unixTimeMs": 1752892241642.714355 },
{"activeModifiers":[],"character":"H", "isARepeat":false,"processTimeMs": 655464687.0,"type":"keyUp", "unixTimeMs": 1752892241703.157227 },
{"activeModifiers":[],"character":"E", "isARepeat":false,"processTimeMs": 655464718.0,"type":"keyDown", "unixTimeMs": 1752892241741.957275 },
{"activeModifiers":[],"character":"N", "isARepeat":false,"processTimeMs": 655464796.0,"type":"keyDown", "unixTimeMs": 1752892241813.681152 },
{"activeModifiers":[],"character":"E", "isARepeat":false,"processTimeMs": 655464828.0,"type":"keyUp", "unixTimeMs": 1752892241840.898926 },
{"activeModifiers":[],"character":"N", "isARepeat":false,"processTimeMs": 655464875.0,"type":"keyUp", "unixTimeMs": 1752892241886.233887 },
{"activeModifiers":[],"character":"G", "isARepeat":false,"processTimeMs": 655464875.0,"type":"keyDown", "unixTimeMs": 1752892241897.159180 },
{"activeModifiers":[],"character":"G", "isARepeat":false,"processTimeMs": 655464953.0,"type":"keyUp", "unixTimeMs": 1752892241968.761719 },
{"activeModifiers":[],"character":"C", "isARepeat":false,"processTimeMs": 655465062.0,"type":"keyDown", "unixTimeMs": 1752892242079.678467 },
{"activeModifiers":[],"character":"H", "isARepeat":false,"processTimeMs": 655465140.0,"type":"keyDown", "unixTimeMs": 1752892242154.411621 },
{"activeModifiers":[],"character":"C", "isARepeat":false,"processTimeMs": 655465156.0,"type":"keyUp", "unixTimeMs": 1752892242180.699219 },
{"activeModifiers":[],"character":"E", "isARepeat":false,"processTimeMs": 655465187.0,"type":"keyDown", "unixTimeMs": 1752892242204.517578 },
{"activeModifiers":[],"character":"H", "isARepeat":false,"processTimeMs": 655465203.0,"type":"keyUp", "unixTimeMs": 1752892242225.324707 },
{"activeModifiers":[],"character":"E", "isARepeat":false,"processTimeMs": 655465296.0,"type":"keyUp", "unixTimeMs": 1752892242309.927979 },
{"activeModifiers":[],"character":"N", "isARepeat":false,"processTimeMs": 655465312.0,"type":"keyDown", "unixTimeMs": 1752892242327.545166 },
{"activeModifiers":[],"character":"G", "isARepeat":false,"processTimeMs": 655465343.0,"type":"keyDown", "unixTimeMs": 1752892242367.942383 },
{"activeModifiers":[],"character":"N", "isARepeat":false,"processTimeMs": 655465421.0,"type":"keyUp", "unixTimeMs": 1752892242433.206787 },
{"activeModifiers":[],"character":"Space", "isARepeat":false,"processTimeMs": 655465421.0,"type":"keyDown", "unixTimeMs": 1752892242443.719482 },
{"activeModifiers":[],"character":"G", "isARepeat":false,"processTimeMs": 655465453.0,"type":"keyUp", "unixTimeMs": 1752892242471.105957 },
{"activeModifiers":[],"character":"Space", "isARepeat":false,"processTimeMs": 655465531.0,"type":"keyUp", "unixTimeMs": 1752892242553.023682 },
{"activeModifiers":[],"character":"Y", "isARepeat":false,"processTimeMs": 655466640.0,"type":"keyDown", "unixTimeMs": 1752892243651.911133 },
{"activeModifiers":[],"character":"Y", "isARepeat":false,"processTimeMs": 655466703.0,"type":"keyUp", "unixTimeMs": 1752892243722.819580 },
{"activeModifiers":[],"character":"I", "isARepeat":false,"processTimeMs": 655466796.0,"type":"keyDown", "unixTimeMs": 1752892243816.658447 },
{"activeModifiers":[],"character":"I", "isARepeat":false,"processTimeMs": 655466875.0,"type":"keyUp", "unixTimeMs": 1752892243900.327881 },
{"activeModifiers":[],"character":"Space", "isARepeat":false,"processTimeMs": 655466984.0,"type":"keyDown", "unixTimeMs": 1752892244008.623291 },
{"activeModifiers":[],"character":"Space", "isARepeat":false,"processTimeMs": 655467093.0,"type":"keyUp", "unixTimeMs": 1752892244113.721924 },
{"activeModifiers":[],"character":"Y", "isARepeat":false,"processTimeMs": 655467796.0,"type":"keyDown", "unixTimeMs": 1752892244820.887695 },
{"activeModifiers":[],"character":"Y", "isARepeat":false,"processTimeMs": 655467890.0,"type":"keyUp", "unixTimeMs": 1752892244905.767578 },
{"activeModifiers":[],"character":"I", "isARepeat":false,"processTimeMs": 655467984.0,"type":"keyDown", "unixTimeMs": 1752892245000.600830 },
{"activeModifiers":[],"character":"I", "isARepeat":false,"processTimeMs": 655468062.0,"type":"keyUp", "unixTimeMs": 1752892245083.697998 },
{"activeModifiers":[],"character":"N", "isARepeat":false,"processTimeMs": 655468156.0,"type":"keyDown", "unixTimeMs": 1752892245167.938477 },
{"activeModifiers":[],"character":"N", "isARepeat":false,"processTimeMs": 655468234.0,"type":"keyUp", "unixTimeMs": 1752892245251.900635 },
{"activeModifiers":[],"character":"G", "isARepeat":false,"processTimeMs": 655468953.0,"type":"keyDown", "unixTimeMs": 1752892245966.864746 },
{"activeModifiers":[],"character":"G", "isARepeat":false,"processTimeMs": 655469031.0,"type":"keyUp", "unixTimeMs": 1752892246042.887451 },
{"activeModifiers":[],"character":"X", "isARepeat":false,"processTimeMs": 655469187.0,"type":"keyDown", "unixTimeMs": 1752892246202.741699 },
{"activeModifiers":[],"character":"X", "isARepeat":false,"processTimeMs": 655469250.0,"type":"keyUp", "unixTimeMs": 1752892246272.798340 },
{"activeModifiers":[],"character":"I", "isARepeat":false,"processTimeMs": 655469281.0,"type":"keyDown", "unixTimeMs": 1752892246298.824463 },
{"activeModifiers":[],"character":"I", "isARepeat":false,"processTimeMs": 655469328.0,"type":"keyUp", "unixTimeMs": 1752892246351.770264 },
{"activeModifiers":[],"character":"A", "isARepeat":false,"processTimeMs": 655469375.0,"type":"keyDown", "unixTimeMs": 1752892246394.787354 },
{"activeModifiers":[],"character":"A", "isARepeat":false,"processTimeMs": 655469468.0,"type":"keyUp", "unixTimeMs": 1752892246479.820557 },
{"activeModifiers":[],"character":"N", "isARepeat":false,"processTimeMs": 655469468.0,"type":"keyDown", "unixTimeMs": 1752892246488.818604 },
{"activeModifiers":[],"character":"N", "isARepeat":false,"processTimeMs": 655469531.0,"type":"keyUp", "unixTimeMs": 1752892246548.800293 },
{"activeModifiers":[],"character":"G", "isARepeat":false,"processTimeMs": 655469546.0,"type":"keyDown", "unixTimeMs": 1752892246558.800049 },
{"activeModifiers":[],"character":"G", "isARepeat":false,"processTimeMs": 655469609.0,"type":"keyUp", "unixTimeMs": 1752892246631.690430 },
{"activeModifiers":[],"character":"S", "isARepeat":false,"processTimeMs": 655469781.0,"type":"keyDown", "unixTimeMs": 1752892246800.035645 },
{"activeModifiers":[],"character":"H", "isARepeat":false,"processTimeMs": 655469859.0,"type":"keyDown", "unixTimeMs": 1752892246880.102051 },
{"activeModifiers":[],"character":"S", "isARepeat":false,"processTimeMs": 655469875.0,"type":"keyUp", "unixTimeMs": 1752892246895.126709 },
{"activeModifiers":[],"character":"H", "isARepeat":false,"processTimeMs": 655469937.0,"type":"keyUp", "unixTimeMs": 1752892246960.696045 },
{"activeModifiers":[],"character":"I", "isARepeat":false,"processTimeMs": 655470031.0,"type":"keyDown", "unixTimeMs": 1752892247048.790283 },
{"activeModifiers":[],"character":"I", "isARepeat":false,"processTimeMs": 655470125.0,"type":"keyUp", "unixTimeMs": 1752892247140.112549 },
{"activeModifiers":[],"character":"Space", "isARepeat":false,"processTimeMs": 655470140.0,"type":"keyDown", "unixTimeMs": 1752892247152.117920 },
{"activeModifiers":[],"character":"Space", "isARepeat":false,"processTimeMs": 655470234.0,"type":"keyUp", "unixTimeMs": 1752892247253.997314 },
{"activeModifiers":[],"character":"Backspace", "isARepeat":false,"processTimeMs": 655470687.0,"type":"keyDown", "unixTimeMs": 1752892247705.171143 },
{"activeModifiers":[],"character":"Backspace", "isARepeat":false,"processTimeMs": 655470734.0,"type":"keyUp", "unixTimeMs": 1752892247752.004395 },
{"activeModifiers":[],"character":"Backspace", "isARepeat":false,"processTimeMs": 655470828.0,"type":"keyDown", "unixTimeMs": 1752892247846.036621 },
{"activeModifiers":[],"character":"Backspace", "isARepeat":false,"processTimeMs": 655470906.0,"type":"keyUp", "unixTimeMs": 1752892247926.059570 },
{"activeModifiers":[],"character":"Backspace", "isARepeat":false,"processTimeMs": 655470984.0,"type":"keyDown", "unixTimeMs": 1752892247997.092529 },
{"activeModifiers":[],"character":"Backspace", "isARepeat":false,"processTimeMs": 655471062.0,"type":"keyUp", "unixTimeMs": 1752892248083.820557 },
{"activeModifiers":[],"character":"Y", "isARepeat":false,"processTimeMs": 655471187.0,"type":"keyDown", "unixTimeMs": 1752892248212.067139 },
{"activeModifiers":[],"character":"Y", "isARepeat":false,"processTimeMs": 655471281.0,"type":"keyUp", "unixTimeMs": 1752892248294.959229 },
{"activeModifiers":[],"character":"I", "isARepeat":false,"processTimeMs": 655471375.0,"type":"keyDown", "unixTimeMs": 1752892248386.207520 },
{"activeModifiers":[],"character":"I", "isARepeat":false,"processTimeMs": 655471437.0,"type":"keyUp", "unixTimeMs": 1752892248460.104980 },
{"activeModifiers":[],"character":"N", "isARepeat":false,"processTimeMs": 655471515.0,"type":"keyDown", "unixTimeMs": 1752892248529.441895 },
{"activeModifiers":[],"character":"G", "isARepeat":false,"processTimeMs": 655471593.0,"type":"keyDown", "unixTimeMs": 1752892248612.346436 },
{"activeModifiers":[],"character":"N", "isARepeat":false,"processTimeMs": 655471609.0,"type":"keyUp", "unixTimeMs": 1752892248627.374512 },
{"activeModifiers":[],"character":"G", "isARepeat":false,"processTimeMs": 655471671.0,"type":"keyUp", "unixTimeMs": 1752892248697.298096 },
{"activeModifiers":[],"character":"Y", "isARepeat":false,"processTimeMs": 655471765.0,"type":"keyDown", "unixTimeMs": 1752892248777.208984 },
{"activeModifiers":[],"character":"Y", "isARepeat":false,"processTimeMs": 655471843.0,"type":"keyUp", "unixTimeMs": 1752892248862.555176 },
{"activeModifiers":[],"character":"A", "isARepeat":false,"processTimeMs": 655471859.0,"type":"keyDown", "unixTimeMs": 1752892248872.072754 },
{"activeModifiers":[],"character":"A", "isARepeat":false,"processTimeMs": 655471937.0,"type":"keyUp", "unixTimeMs": 1752892248951.063721 },
{"activeModifiers":[],"character":"N", "isARepeat":false,"processTimeMs": 655471953.0,"type":"keyDown", "unixTimeMs": 1752892248966.299316 },
{"activeModifiers":[],"character":"N", "isARepeat":false,"processTimeMs": 655472031.0,"type":"keyUp", "unixTimeMs": 1752892249050.371826 },
{"activeModifiers":[],"character":"G", "isARepeat":false,"processTimeMs": 655472031.0,"type":"keyDown", "unixTimeMs": 1752892249052.376221 },
{"activeModifiers":[],"character":"G", "isARepeat":false,"processTimeMs": 655472109.0,"type":"keyUp", "unixTimeMs": 1752892249126.989258 },
{"activeModifiers":[],"character":"S", "isARepeat":false,"processTimeMs": 655472203.0,"type":"keyDown", "unixTimeMs": 1752892249221.238037 },
{"activeModifiers":[],"character":"H", "isARepeat":false,"processTimeMs": 655472265.0,"type":"keyDown", "unixTimeMs": 1752892249289.001221 },
{"activeModifiers":[],"character":"S", "isARepeat":false,"processTimeMs": 655472281.0,"type":"keyUp", "unixTimeMs": 1752892249305.874512 },
{"activeModifiers":[],"character":"H", "isARepeat":false,"processTimeMs": 655472343.0,"type":"keyUp", "unixTimeMs": 1752892249363.773438 },
{"activeModifiers":[],"character":"I", "isARepeat":false,"processTimeMs": 655472421.0,"type":"keyDown", "unixTimeMs": 1752892249434.135742 },
{"activeModifiers":[],"character":"I", "isARepeat":false,"processTimeMs": 655472531.0,"type":"keyUp", "unixTimeMs": 1752892249542.020508 },
{"activeModifiers":[],"character":"Space", "isARepeat":false,"processTimeMs": 655472562.0,"type":"keyDown", "unixTimeMs": 1752892249581.895264 },
{"activeModifiers":[],"character":"Space", "isARepeat":false,"processTimeMs": 655472656.0,"type":"keyUp", "unixTimeMs": 1752892249679.031982 },
{"activeModifiers":[],"character":"K", "isARepeat":false,"processTimeMs": 655472734.0,"type":"keyDown", "unixTimeMs": 1752892249752.156494 },
{"activeModifiers":[],"character":"K", "isARepeat":false,"processTimeMs": 655472812.0,"type":"keyUp", "unixTimeMs": 1752892249835.123047 },
{"activeModifiers":[],"character":"O", "isARepeat":false,"processTimeMs": 655472906.0,"type":"keyDown", "unixTimeMs": 1752892249930.173096 },
{"activeModifiers":[],"character":"O", "isARepeat":false,"processTimeMs": 655472984.0,"type":"keyUp", "unixTimeMs": 1752892249995.320068 },
{"activeModifiers":[],"character":"U", "isARepeat":false,"processTimeMs": 655473062.0,"type":"keyDown", "unixTimeMs": 1752892250077.935303 },
{"activeModifiers":[],"character":"U", "isARepeat":false,"processTimeMs": 655473140.0,"type":"keyUp", "unixTimeMs": 1752892250165.024902 },
{"activeModifiers":[],"character":"W", "isARepeat":false,"processTimeMs": 655473218.0,"type":"keyDown", "unixTimeMs": 1752892250243.543945 },
{"activeModifiers":[],"character":"E", "isARepeat":false,"processTimeMs": 655473406.0,"type":"keyDown", "unixTimeMs": 1752892250419.180420 },
{"activeModifiers":[],"character":"W", "isARepeat":false,"processTimeMs": 655473421.0,"type":"keyUp", "unixTimeMs": 1752892250441.181396 },
{"activeModifiers":[],"character":"E", "isARepeat":false,"processTimeMs": 655473515.0,"type":"keyUp", "unixTimeMs": 1752892250536.254395 },
{"activeModifiers":[],"character":"N", "isARepeat":false,"processTimeMs": 655473625.0,"type":"keyDown", "unixTimeMs": 1752892250648.041260 },
{"activeModifiers":[],"character":"N", "isARepeat":false,"processTimeMs": 655473703.0,"type":"keyUp", "unixTimeMs": 1752892250719.340332 },
{"activeModifiers":[],"character":"Space", "isARepeat":false,"processTimeMs": 655473890.0,"type":"keyDown", "unixTimeMs": 1752892250912.063232 },
{"activeModifiers":[],"character":"Space", "isARepeat":false,"processTimeMs": 655473968.0,"type":"keyUp", "unixTimeMs": 1752892250993.321533 },
{"activeModifiers":[],"character":"S", "isARepeat":false,"processTimeMs": 655474046.0,"type":"keyDown", "unixTimeMs": 1752892251065.138672 },
{"activeModifiers":[],"character":"S", "isARepeat":false,"processTimeMs": 655474109.0,"type":"keyUp", "unixTimeMs": 1752892251125.485107 },
{"activeModifiers":[],"character":"H", "isARepeat":false,"processTimeMs": 655474171.0,"type":"keyDown", "unixTimeMs": 1752892251184.006348 },
{"activeModifiers":[],"character":"H", "isARepeat":false,"processTimeMs": 655474234.0,"type":"keyUp", "unixTimeMs": 1752892251244.988770 },
{"activeModifiers":[],"character":"Backspace", "isARepeat":false,"processTimeMs": 655474484.0,"type":"keyDown", "unixTimeMs": 1752892251499.615967 },
{"activeModifiers":[],"character":"Backspace", "isARepeat":false,"processTimeMs": 655474562.0,"type":"keyUp", "unixTimeMs": 1752892251576.119141 },
{"activeModifiers":[],"character":"Backspace", "isARepeat":false,"processTimeMs": 655474671.0,"type":"keyDown", "unixTimeMs": 1752892251692.364502 },
{"activeModifiers":[],"character":"Backspace", "isARepeat":false,"processTimeMs": 655474750.0,"type":"keyUp", "unixTimeMs": 1752892251775.549316 },
{"activeModifiers":[],"character":"S", "isARepeat":false,"processTimeMs": 655475546.0,"type":"keyDown", "unixTimeMs": 1752892252567.193604 },
{"activeModifiers":[],"character":"S", "isARepeat":false,"processTimeMs": 655475640.0,"type":"keyUp", "unixTimeMs": 1752892252651.510010 },
{"activeModifiers":[],"character":"H", "isARepeat":false,"processTimeMs": 655475640.0,"type":"keyDown", "unixTimeMs": 1752892252660.243896 },
{"activeModifiers":[],"character":"H", "isARepeat":false,"processTimeMs": 655475687.0,"type":"keyUp", "unixTimeMs": 1752892252710.346680 },
{"activeModifiers":[],"character":"U", "isARepeat":false,"processTimeMs": 655475765.0,"type":"keyDown", "unixTimeMs": 1752892252780.086426 },
{"activeModifiers":[],"character":"U", "isARepeat":false,"processTimeMs": 655475843.0,"type":"keyUp", "unixTimeMs": 1752892252854.154785 },
{"activeModifiers":[],"character":"O", "isARepeat":false,"processTimeMs": 655475937.0,"type":"keyDown", "unixTimeMs": 1752892252953.372314 },
{"activeModifiers":[],"character":"O", "isARepeat":false,"processTimeMs": 655476015.0,"type":"keyUp", "unixTimeMs": 1752892253034.215820 },
{"activeModifiers":[],"character":"C", "isARepeat":false,"processTimeMs": 655476031.0,"type":"keyDown", "unixTimeMs": 1752892253044.011719 },
{"activeModifiers":[],"character":"C", "isARepeat":false,"processTimeMs": 655476125.0,"type":"keyUp", "unixTimeMs": 1752892253138.654053 },
{"activeModifiers":[],"character":"H", "isARepeat":false,"processTimeMs": 655476140.0,"type":"keyDown", "unixTimeMs": 1752892253162.766113 },
{"activeModifiers":[],"character":"H", "isARepeat":false,"processTimeMs": 655476218.0,"type":"keyUp", "unixTimeMs": 1752892253232.060791 },
{"activeModifiers":[],"character":"U", "isARepeat":false,"processTimeMs": 655476281.0,"type":"keyDown", "unixTimeMs": 1752892253302.069580 },
{"activeModifiers":[],"character":"Space", "isARepeat":false,"processTimeMs": 655476375.0,"type":"keyDown", "unixTimeMs": 1752892253392.772705 },
{"activeModifiers":[],"character":"U", "isARepeat":false,"processTimeMs": 655476390.0,"type":"keyUp", "unixTimeMs": 1752892253408.736328 },
{"activeModifiers":[],"character":"D", "isARepeat":false,"processTimeMs": 655476437.0,"type":"keyDown", "unixTimeMs": 1752892253456.449951 },
{"activeModifiers":[],"character":"Space", "isARepeat":false,"processTimeMs": 655476484.0,"type":"keyUp", "unixTimeMs": 1752892253498.839111 },
{"activeModifiers":[],"character":"E", "isARepeat":false,"processTimeMs": 655476500.0,"type":"keyDown", "unixTimeMs": 1752892253523.364746 },
{"activeModifiers":[],"character":"D", "isARepeat":false,"processTimeMs": 655476546.0,"type":"keyUp", "unixTimeMs": 1752892253571.135498 },
{"activeModifiers":[],"character":"Space", "isARepeat":false,"processTimeMs": 655476578.0,"type":"keyDown", "unixTimeMs": 1752892253594.135742 },
{"activeModifiers":[],"character":"E", "isARepeat":false,"processTimeMs": 655476609.0,"type":"keyUp", "unixTimeMs": 1752892253628.980225 },
{"activeModifiers":[],"character":"Space", "isARepeat":false,"processTimeMs": 655476687.0,"type":"keyUp", "unixTimeMs": 1752892253709.605225 },
{"activeModifiers":[],"character":"B", "isARepeat":false,"processTimeMs": 655478937.0,"type":"keyDown", "unixTimeMs": 1752892255960.719727 },
{"activeModifiers":[],"character":"B", "isARepeat":false,"processTimeMs": 655479015.0,"type":"keyUp", "unixTimeMs": 1752892256039.363770 },
{"activeModifiers":[],"character":"A", "isARepeat":false,"processTimeMs": 655479062.0,"type":"keyDown", "unixTimeMs": 1752892256077.279297 },
{"activeModifiers":[],"character":"A", "isARepeat":false,"processTimeMs": 655479125.0,"type":"keyUp", "unixTimeMs": 1752892256143.455566 },
{"activeModifiers":[],"character":"O", "isARepeat":false,"processTimeMs": 655479171.0,"type":"keyDown", "unixTimeMs": 1752892256196.311523 },
{"activeModifiers":[],"character":"O", "isARepeat":false,"processTimeMs": 655479234.0,"type":"keyUp", "unixTimeMs": 1752892256256.115479 },
{"activeModifiers":[],"character":"J", "isARepeat":false,"processTimeMs": 655479390.0,"type":"keyDown", "unixTimeMs": 1752892256410.317139 },
{"activeModifiers":[],"character":"J", "isARepeat":false,"processTimeMs": 655479453.0,"type":"keyUp", "unixTimeMs": 1752892256464.077148 },
{"activeModifiers":[],"character":"I", "isARepeat":false,"processTimeMs": 655479515.0,"type":"keyDown", "unixTimeMs": 1752892256536.417480 },
{"activeModifiers":[],"character":"I", "isARepeat":false,"processTimeMs": 655479578.0,"type":"keyUp", "unixTimeMs": 1752892256601.195312 },
{"activeModifiers":[],"character":"A", "isARepeat":false,"processTimeMs": 655479593.0,"type":"keyDown", "unixTimeMs": 1752892256607.714111 },
{"activeModifiers":[],"character":"A", "isARepeat":false,"processTimeMs": 655479671.0,"type":"keyUp", "unixTimeMs": 1752892256685.332764 },
{"activeModifiers":[],"character":"N", "isARepeat":false,"processTimeMs": 655479703.0,"type":"keyDown", "unixTimeMs": 1752892256722.578613 },
{"activeModifiers":[],"character":"N", "isARepeat":false,"processTimeMs": 655479765.0,"type":"keyUp", "unixTimeMs": 1752892256790.784912 },
{"activeModifiers":[],"character":"P", "isARepeat":false,"processTimeMs": 655480000.0,"type":"keyDown", "unixTimeMs": 1752892257019.328369 },
{"activeModifiers":[],"character":"P", "isARepeat":false,"processTimeMs": 655480062.0,"type":"keyUp", "unixTimeMs": 1752892257082.601074 },
{"activeModifiers":[],"character":"I", "isARepeat":false,"processTimeMs": 655480187.0,"type":"keyDown", "unixTimeMs": 1752892257199.375244 },
{"activeModifiers":[],"character":"I", "isARepeat":false,"processTimeMs": 655480234.0,"type":"keyUp", "unixTimeMs": 1752892257255.456299 },
{"activeModifiers":[],"character":"N", "isARepeat":false,"processTimeMs": 655480343.0,"type":"keyDown", "unixTimeMs": 1752892257366.764893 },
{"activeModifiers":[],"character":"G", "isARepeat":false,"processTimeMs": 655480421.0,"type":"keyDown", "unixTimeMs": 1752892257444.400391 },
{"activeModifiers":[],"character":"N", "isARepeat":false,"processTimeMs": 655480437.0,"type":"keyUp", "unixTimeMs": 1752892257457.405273 },
{"activeModifiers":[],"character":"G", "isARepeat":false,"processTimeMs": 655480500.0,"type":"keyUp", "unixTimeMs": 1752892257513.649658 },
{"activeModifiers":[],"character":"Space", "isARepeat":false,"processTimeMs": 655480609.0,"type":"keyDown", "unixTimeMs": 1752892257627.980957 },
{"activeModifiers":[],"character":"Space", "isARepeat":false,"processTimeMs": 655480734.0,"type":"keyUp", "unixTimeMs": 1752892257750.166016 },
{"activeModifiers":[],"character":"G", "isARepeat":false,"processTimeMs": 655480796.0,"type":"keyDown", "unixTimeMs": 1752892257819.712891 },
{"activeModifiers":[],"character":"G", "isARepeat":false,"processTimeMs": 655480875.0,"type":"keyUp", "unixTimeMs": 1752892257895.457520 },
{"activeModifiers":[],"character":"U", "isARepeat":false,"processTimeMs": 655480968.0,"type":"keyDown", "unixTimeMs": 1752892257994.412842 },
{"activeModifiers":[],"character":"A", "isARepeat":false,"processTimeMs": 655481046.0,"type":"keyDown", "unixTimeMs": 1752892258072.031494 },
{"activeModifiers":[],"character":"U", "isARepeat":false,"processTimeMs": 655481062.0,"type":"keyUp", "unixTimeMs": 1752892258085.454834 },
{"activeModifiers":[],"character":"A", "isARepeat":false,"processTimeMs": 655481140.0,"type":"keyUp", "unixTimeMs": 1752892258163.423584 },
{"activeModifiers":[],"character":"Space", "isARepeat":false,"processTimeMs": 655481531.0,"type":"keyDown", "unixTimeMs": 1752892258544.420654 },
{"activeModifiers":[],"character":"Space", "isARepeat":false,"processTimeMs": 655481625.0,"type":"keyUp", "unixTimeMs": 1752892258643.866455 },
{"activeModifiers":[],"character":"Backspace", "isARepeat":false,"processTimeMs": 655481656.0,"type":"keyDown", "unixTimeMs": 1752892258672.574219 },
{"activeModifiers":[],"character":"Backspace", "isARepeat":false,"processTimeMs": 655481718.0,"type":"keyUp", "unixTimeMs": 1752892258743.371094 },
{"activeModifiers":[],"character":"Backspace", "isARepeat":false,"processTimeMs": 655481812.0,"type":"keyDown", "unixTimeMs": 1752892258837.692383 },
{"activeModifiers":[],"character":"Backspace", "isARepeat":false,"processTimeMs": 655481890.0,"type":"keyUp", "unixTimeMs": 1752892258913.490967 },
{"activeModifiers":[],"character":"Backspace", "isARepeat":false,"processTimeMs": 655481968.0,"type":"keyDown", "unixTimeMs": 1752892258992.780762 },
{"activeModifiers":[],"character":"Backspace", "isARepeat":false,"processTimeMs": 655482046.0,"type":"keyUp", "unixTimeMs": 1752892259071.708496 },
{"activeModifiers":[],"character":"Backspace", "isARepeat":false,"processTimeMs": 655482140.0,"type":"keyDown", "unixTimeMs": 1752892259159.595947 },
{"activeModifiers":[],"character":"Backspace", "isARepeat":false,"processTimeMs": 655482218.0,"type":"keyUp", "unixTimeMs": 1752892259232.647217 },
{"activeModifiers":[],"character":"B", "isARepeat":false,"processTimeMs": 655482375.0,"type":"keyDown", "unixTimeMs": 1752892259385.920166 },
{"activeModifiers":[],"character":"B", "isARepeat":false,"processTimeMs": 655482453.0,"type":"keyUp", "unixTimeMs": 1752892259475.499268 },
{"activeModifiers":[],"character":"A", "isARepeat":false,"processTimeMs": 655482531.0,"type":"keyDown", "unixTimeMs": 1752892259555.533691 },
{"activeModifiers":[],"character":"O", "isARepeat":false,"processTimeMs": 655482609.0,"type":"keyDown", "unixTimeMs": 1752892259623.608154 },
{"activeModifiers":[],"character":"A", "isARepeat":false,"processTimeMs": 655482609.0,"type":"keyUp", "unixTimeMs": 1752892259630.124756 },
{"activeModifiers":[],"character":"O", "isARepeat":false,"processTimeMs": 655482656.0,"type":"keyUp", "unixTimeMs": 1752892259670.757080 },
{"activeModifiers":[],"character":"J", "isARepeat":false,"processTimeMs": 655482765.0,"type":"keyDown", "unixTimeMs": 1752892259790.626709 },
{"activeModifiers":[],"character":"J", "isARepeat":false,"processTimeMs": 655482828.0,"type":"keyUp", "unixTimeMs": 1752892259846.680420 },
{"activeModifiers":[],"character":"I", "isARepeat":false,"processTimeMs": 655482890.0,"type":"keyDown", "unixTimeMs": 1752892259906.785889 },
{"activeModifiers":[],"character":"I", "isARepeat":false,"processTimeMs": 655482953.0,"type":"keyUp", "unixTimeMs": 1752892259972.819580 },
{"activeModifiers":[],"character":"A", "isARepeat":false,"processTimeMs": 655482968.0,"type":"keyDown", "unixTimeMs": 1752892259987.814697 },
{"activeModifiers":[],"character":"N", "isARepeat":false,"processTimeMs": 655483046.0,"type":"keyDown", "unixTimeMs": 1752892260065.844482 },
{"activeModifiers":[],"character":"A", "isARepeat":false,"processTimeMs": 655483078.0,"type":"keyUp", "unixTimeMs": 1752892260088.327393 },
{"activeModifiers":[],"character":"N", "isARepeat":false,"processTimeMs": 655483109.0,"type":"keyUp", "unixTimeMs": 1752892260125.629150 },
{"activeModifiers":[],"character":"P", "isARepeat":false,"processTimeMs": 655483234.0,"type":"keyDown", "unixTimeMs": 1752892260253.327637 },
{"activeModifiers":[],"character":"P", "isARepeat":false,"processTimeMs": 655483296.0,"type":"keyUp", "unixTimeMs": 1752892260313.890381 },
{"activeModifiers":[],"character":"I", "isARepeat":false,"processTimeMs": 655483406.0,"type":"keyDown", "unixTimeMs": 1752892260422.837158 },
{"activeModifiers":[],"character":"I", "isARepeat":false,"processTimeMs": 655483468.0,"type":"keyUp", "unixTimeMs": 1752892260489.977295 },
{"activeModifiers":[],"character":"N", "isARepeat":false,"processTimeMs": 655483593.0,"type":"keyDown", "unixTimeMs": 1752892260613.753906 },
{"activeModifiers":[],"character":"N", "isARepeat":false,"processTimeMs": 655483671.0,"type":"keyUp", "unixTimeMs": 1752892260690.842529 },
{"activeModifiers":[],"character":"Space", "isARepeat":false,"processTimeMs": 655484312.0,"type":"keyDown", "unixTimeMs": 1752892261332.443604 },
{"activeModifiers":[],"character":"Space", "isARepeat":false,"processTimeMs": 655484437.0,"type":"keyUp", "unixTimeMs": 1752892261448.828857 },
{"activeModifiers":[],"character":"G", "isARepeat":false,"processTimeMs": 655484640.0,"type":"keyDown", "unixTimeMs": 1752892261665.641113 },
{"activeModifiers":[],"character":"G", "isARepeat":false,"processTimeMs": 655484734.0,"type":"keyUp", "unixTimeMs": 1752892261751.025879 },
{"activeModifiers":[],"character":"U", "isARepeat":false,"processTimeMs": 655484812.0,"type":"keyDown", "unixTimeMs": 1752892261836.802490 },
{"activeModifiers":[],"character":"A", "isARepeat":false,"processTimeMs": 655484890.0,"type":"keyDown", "unixTimeMs": 1752892261905.919189 },
{"activeModifiers":[],"character":"U", "isARepeat":false,"processTimeMs": 655484906.0,"type":"keyUp", "unixTimeMs": 1752892261920.086914 },
{"activeModifiers":[],"character":"A", "isARepeat":false,"processTimeMs": 655484968.0,"type":"keyUp", "unixTimeMs": 1752892261986.958740 },
{"activeModifiers":[],"character":"N", "isARepeat":false,"processTimeMs": 655484984.0,"type":"keyDown", "unixTimeMs": 1752892262002.476074 },
{"activeModifiers":[],"character":"N", "isARepeat":false,"processTimeMs": 655485078.0,"type":"keyUp", "unixTimeMs": 1752892262088.936279 },
{"activeModifiers":[],"character":"G", "isARepeat":false,"processTimeMs": 655485078.0,"type":"keyDown", "unixTimeMs": 1752892262097.403564 },
{"activeModifiers":[],"character":"G", "isARepeat":false,"processTimeMs": 655485140.0,"type":"keyUp", "unixTimeMs": 1752892262157.966797 },
{"activeModifiers":[],"character":"H", "isARepeat":false,"processTimeMs": 655485296.0,"type":"keyDown", "unixTimeMs": 1752892262307.021729 },
{"activeModifiers":[],"character":"H", "isARepeat":false,"processTimeMs": 655485375.0,"type":"keyUp", "unixTimeMs": 1752892262386.631104 },
{"activeModifiers":[],"character":"Backspace", "isARepeat":false,"processTimeMs": 655485671.0,"type":"keyDown", "unixTimeMs": 1752892262692.015869 },
{"activeModifiers":[],"character":"Backspace", "isARepeat":false,"processTimeMs": 655485750.0,"type":"keyUp", "unixTimeMs": 1752892262761.716797 },
{"activeModifiers":[],"character":"G", "isARepeat":false,"processTimeMs": 655485875.0,"type":"keyDown", "unixTimeMs": 1752892262890.954834 },
{"activeModifiers":[],"character":"G", "isARepeat":false,"processTimeMs": 655485968.0,"type":"keyUp", "unixTimeMs": 1752892262984.785645 },
{"activeModifiers":[],"character":"A", "isARepeat":false,"processTimeMs": 655486062.0,"type":"keyDown", "unixTimeMs": 1752892263073.023682 },
{"activeModifiers":[],"character":"A", "isARepeat":false,"processTimeMs": 655486093.0,"type":"keyUp", "unixTimeMs": 1752892263113.339600 },
{"activeModifiers":[],"character":"O", "isARepeat":false,"processTimeMs": 655486109.0,"type":"keyDown", "unixTimeMs": 1752892263129.851562 },
{"activeModifiers":[],"character":"O", "isARepeat":false,"processTimeMs": 655486171.0,"type":"keyUp", "unixTimeMs": 1752892263191.438477 },
{"activeModifiers":[],"character":"C", "isARepeat":false,"processTimeMs": 655486218.0,"type":"keyDown", "unixTimeMs": 1752892263235.713623 },
{"activeModifiers":[],"character":"C", "isARepeat":false,"processTimeMs": 655486296.0,"type":"keyUp", "unixTimeMs": 1752892263318.752686 },
{"activeModifiers":[],"character":"I", "isARepeat":false,"processTimeMs": 655486406.0,"type":"keyDown", "unixTimeMs": 1752892263419.812744 },
{"activeModifiers":[],"character":"I", "isARepeat":false,"processTimeMs": 655486468.0,"type":"keyUp", "unixTimeMs": 1752892263482.832520 },
{"activeModifiers":[],"character":"Space", "isARepeat":false,"processTimeMs": 655486562.0,"type":"keyDown", "unixTimeMs": 1752892263576.994873 },
{"activeModifiers":[],"character":"Space", "isARepeat":false,"processTimeMs": 655486656.0,"type":"keyUp", "unixTimeMs": 1752892263666.757080 },
]