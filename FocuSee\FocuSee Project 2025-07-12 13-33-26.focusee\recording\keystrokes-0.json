[

{"activeModifiers":[],"character":"Z", "isARepeat":false,"processTimeMs": 61638437.0,"type":"keyDown", "unixTimeMs": 1752298422775.987305 },
{"activeModifiers":[],"character":"Z", "isARepeat":false,"processTimeMs": 61638515.0,"type":"keyUp", "unixTimeMs": 1752298422861.434082 },
{"activeModifiers":[],"character":"H", "isARepeat":false,"processTimeMs": 61638531.0,"type":"keyDown", "unixTimeMs": 1752298422871.542480 },
{"activeModifiers":[],"character":"E", "isARepeat":false,"processTimeMs": 61638609.0,"type":"keyDown", "unixTimeMs": 1752298422952.100342 },
{"activeModifiers":[],"character":"H", "isARepeat":false,"processTimeMs": 61638625.0,"type":"keyUp", "unixTimeMs": 1752298422970.239258 },
{"activeModifiers":[],"character":"E", "isARepeat":false,"processTimeMs": 61638687.0,"type":"keyUp", "unixTimeMs": 1752298423029.342773 },
{"activeModifiers":[],"character":"Y", "isARepeat":false,"processTimeMs": 61638734.0,"type":"keyDown", "unixTimeMs": 1752298423069.580811 },
{"activeModifiers":[],"character":"Y", "isARepeat":false,"processTimeMs": 61638828.0,"type":"keyUp", "unixTimeMs": 1752298423163.523682 },
{"activeModifiers":[],"character":"I", "isARepeat":false,"processTimeMs": 61638921.0,"type":"keyDown", "unixTimeMs": 1752298423263.632812 },
{"activeModifiers":[],"character":"I", "isARepeat":false,"processTimeMs": 61639000.0,"type":"keyUp", "unixTimeMs": 1752298423340.461182 },
{"activeModifiers":[],"character":"Z", "isARepeat":false,"processTimeMs": 61639109.0,"type":"keyDown", "unixTimeMs": 1752298423452.444336 },
{"activeModifiers":[],"character":"H", "isARepeat":false,"processTimeMs": 61639171.0,"type":"keyDown", "unixTimeMs": 1752298423505.463379 },
{"activeModifiers":[],"character":"Z", "isARepeat":false,"processTimeMs": 61639203.0,"type":"keyUp", "unixTimeMs": 1752298423538.828857 },
{"activeModifiers":[],"character":"H", "isARepeat":false,"processTimeMs": 61639250.0,"type":"keyUp", "unixTimeMs": 1752298423582.774414 },
{"activeModifiers":[],"character":"O", "isARepeat":false,"processTimeMs": 61639343.0,"type":"keyDown", "unixTimeMs": 1752298423688.666748 },
{"activeModifiers":[],"character":"O", "isARepeat":false,"processTimeMs": 61639421.0,"type":"keyUp", "unixTimeMs": 1752298423756.935303 },
{"activeModifiers":[],"character":"U", "isARepeat":false,"processTimeMs": 61639484.0,"type":"keyDown", "unixTimeMs": 1752298423828.587891 },
{"activeModifiers":[],"character":"U", "isARepeat":false,"processTimeMs": 61639562.0,"type":"keyUp", "unixTimeMs": 1752298423903.774902 },
{"activeModifiers":[],"character":"Space", "isARepeat":false,"processTimeMs": 61639609.0,"type":"keyDown", "unixTimeMs": 1752298423948.972412 },
{"activeModifiers":[],"character":"Space", "isARepeat":false,"processTimeMs": 61639687.0,"type":"keyUp", "unixTimeMs": 1752298424031.553711 },
{"activeModifiers":[],"character":"Z", "isARepeat":false,"processTimeMs": 61641578.0,"type":"keyDown", "unixTimeMs": 1752298425916.418457 },
{"activeModifiers":[],"character":"Z", "isARepeat":false,"processTimeMs": 61641671.0,"type":"keyUp", "unixTimeMs": 1752298426014.920410 },
{"activeModifiers":[],"character":"H", "isARepeat":false,"processTimeMs": 61641703.0,"type":"keyDown", "unixTimeMs": 1752298426036.951660 },
{"activeModifiers":[],"character":"H", "isARepeat":false,"processTimeMs": 61641781.0,"type":"keyUp", "unixTimeMs": 1752298426116.674805 },
{"activeModifiers":[],"character":"E", "isARepeat":false,"processTimeMs": 61641781.0,"type":"keyDown", "unixTimeMs": 1752298426126.253662 },
{"activeModifiers":[],"character":"E", "isARepeat":false,"processTimeMs": 61641859.0,"type":"keyUp", "unixTimeMs": 1752298426199.144531 },
{"activeModifiers":[],"character":"Y", "isARepeat":false,"processTimeMs": 61642015.0,"type":"keyDown", "unixTimeMs": 1752298426351.347900 },
{"activeModifiers":[],"character":"Y", "isARepeat":false,"processTimeMs": 61642109.0,"type":"keyUp", "unixTimeMs": 1752298426442.762939 },
{"activeModifiers":[],"character":"Backspace", "isARepeat":false,"processTimeMs": 61642500.0,"type":"keyDown", "unixTimeMs": 1752298426840.875732 },
{"activeModifiers":[],"character":"Backspace", "isARepeat":false,"processTimeMs": 61642546.0,"type":"keyUp", "unixTimeMs": 1752298426881.663086 },
{"activeModifiers":[],"character":"Backspace", "isARepeat":false,"processTimeMs": 61642625.0,"type":"keyDown", "unixTimeMs": 1752298426972.282471 },
{"activeModifiers":[],"character":"Backspace", "isARepeat":false,"processTimeMs": 61642703.0,"type":"keyUp", "unixTimeMs": 1752298427045.768311 },
{"activeModifiers":[],"character":"Backspace", "isARepeat":false,"processTimeMs": 61642781.0,"type":"keyDown", "unixTimeMs": 1752298427125.706299 },
{"activeModifiers":[],"character":"Backspace", "isARepeat":false,"processTimeMs": 61642859.0,"type":"keyUp", "unixTimeMs": 1752298427198.278809 },
{"activeModifiers":[],"character":"Backspace", "isARepeat":false,"processTimeMs": 61642937.0,"type":"keyDown", "unixTimeMs": 1752298427284.766113 },
{"activeModifiers":[],"character":"Backspace", "isARepeat":false,"processTimeMs": 61643015.0,"type":"keyUp", "unixTimeMs": 1752298427354.372070 },
{"activeModifiers":[],"character":"S", "isARepeat":false,"processTimeMs": 61644390.0,"type":"keyDown", "unixTimeMs": 1752298428729.845947 },
{"activeModifiers":[],"character":"S", "isARepeat":false,"processTimeMs": 61644515.0,"type":"keyUp", "unixTimeMs": 1752298428848.629150 },
{"activeModifiers":[],"character":"Backspace", "isARepeat":false,"processTimeMs": 61645000.0,"type":"keyDown", "unixTimeMs": 1752298429341.137939 },
{"activeModifiers":[],"character":"Backspace", "isARepeat":false,"processTimeMs": 61645078.0,"type":"keyUp", "unixTimeMs": 1752298429419.620117 },
{"activeModifiers":[],"character":"Z", "isARepeat":false,"processTimeMs": 61646046.0,"type":"keyDown", "unixTimeMs": 1752298430385.384521 },
{"activeModifiers":[],"character":"Z", "isARepeat":false,"processTimeMs": 61646171.0,"type":"keyUp", "unixTimeMs": 1752298430508.770508 },
{"activeModifiers":[],"character":"H", "isARepeat":false,"processTimeMs": 61646218.0,"type":"keyDown", "unixTimeMs": 1752298430556.165283 },
{"activeModifiers":[],"character":"H", "isARepeat":false,"processTimeMs": 61646296.0,"type":"keyUp", "unixTimeMs": 1752298430633.381348 },
{"activeModifiers":[],"character":"E", "isARepeat":false,"processTimeMs": 61646296.0,"type":"keyDown", "unixTimeMs": 1752298430640.399658 },
{"activeModifiers":[],"character":"E", "isARepeat":false,"processTimeMs": 61646390.0,"type":"keyUp", "unixTimeMs": 1752298430726.863525 },
{"activeModifiers":[],"character":"Y", "isARepeat":false,"processTimeMs": 61646562.0,"type":"keyDown", "unixTimeMs": 1752298430908.355957 },
{"activeModifiers":[],"character":"Y", "isARepeat":false,"processTimeMs": 61646656.0,"type":"keyUp", "unixTimeMs": 1752298431001.743652 },
{"activeModifiers":[],"character":"I", "isARepeat":false,"processTimeMs": 61646765.0,"type":"keyDown", "unixTimeMs": 1752298431105.317139 },
{"activeModifiers":[],"character":"I", "isARepeat":false,"processTimeMs": 61646843.0,"type":"keyUp", "unixTimeMs": 1752298431182.162354 },
{"activeModifiers":[],"character":"Z", "isARepeat":false,"processTimeMs": 61646968.0,"type":"keyDown", "unixTimeMs": 1752298431306.228271 },
{"activeModifiers":[],"character":"H", "isARepeat":false,"processTimeMs": 61647046.0,"type":"keyDown", "unixTimeMs": 1752298431394.585938 },
{"activeModifiers":[],"character":"Z", "isARepeat":false,"processTimeMs": 61647078.0,"type":"keyUp", "unixTimeMs": 1752298431424.389160 },
{"activeModifiers":[],"character":"H", "isARepeat":false,"processTimeMs": 61647125.0,"type":"keyUp", "unixTimeMs": 1752298431464.052002 },
{"activeModifiers":[],"character":"O", "isARepeat":false,"processTimeMs": 61647234.0,"type":"keyDown", "unixTimeMs": 1752298431579.489258 },
{"activeModifiers":[],"character":"O", "isARepeat":false,"processTimeMs": 61647296.0,"type":"keyUp", "unixTimeMs": 1752298431643.469971 },
{"activeModifiers":[],"character":"U", "isARepeat":false,"processTimeMs": 61647390.0,"type":"keyDown", "unixTimeMs": 1752298431726.145508 },
{"activeModifiers":[],"character":"U", "isARepeat":false,"processTimeMs": 61647453.0,"type":"keyUp", "unixTimeMs": 1752298431790.825439 },
{"activeModifiers":[],"character":"Space", "isARepeat":false,"processTimeMs": 61647468.0,"type":"keyDown", "unixTimeMs": 1752298431786.684326 },
{"activeModifiers":[],"character":"Space", "isARepeat":false,"processTimeMs": 61647562.0,"type":"keyUp", "unixTimeMs": 1752298431907.155518 },
{"activeModifiers":[],"character":"Y", "isARepeat":false,"processTimeMs": 61647578.0,"type":"keyDown", "unixTimeMs": 1752298431913.105469 },
{"activeModifiers":[],"character":"Y", "isARepeat":false,"processTimeMs": 61647671.0,"type":"keyUp", "unixTimeMs": 1752298432006.189697 },
{"activeModifiers":[],"character":"O", "isARepeat":false,"processTimeMs": 61647796.0,"type":"keyDown", "unixTimeMs": 1752298432134.679688 },
{"activeModifiers":[],"character":"O", "isARepeat":false,"processTimeMs": 61647859.0,"type":"keyUp", "unixTimeMs": 1752298432196.598877 },
{"activeModifiers":[],"character":"U", "isARepeat":false,"processTimeMs": 61647937.0,"type":"keyDown", "unixTimeMs": 1752298432278.209229 },
{"activeModifiers":[],"character":"U", "isARepeat":false,"processTimeMs": 61648015.0,"type":"keyUp", "unixTimeMs": 1752298432361.327637 },
{"activeModifiers":[],"character":"Space", "isARepeat":false,"processTimeMs": 61648046.0,"type":"keyDown", "unixTimeMs": 1752298432390.177490 },
{"activeModifiers":[],"character":"Space", "isARepeat":false,"processTimeMs": 61648156.0,"type":"keyUp", "unixTimeMs": 1752298432494.387207 },
{"activeModifiers":[],"character":"N", "isARepeat":false,"processTimeMs": 61648218.0,"type":"keyDown", "unixTimeMs": 1752298432561.048828 },
{"activeModifiers":[],"character":"N", "isARepeat":false,"processTimeMs": 61648281.0,"type":"keyUp", "unixTimeMs": 1752298432625.088867 },
{"activeModifiers":[],"character":"A", "isARepeat":false,"processTimeMs": 61648312.0,"type":"keyDown", "unixTimeMs": 1752298432654.320312 },
{"activeModifiers":[],"character":"A", "isARepeat":false,"processTimeMs": 61648390.0,"type":"keyUp", "unixTimeMs": 1752298432730.654297 },
{"activeModifiers":[],"character":"X", "isARepeat":false,"processTimeMs": 61648500.0,"type":"keyDown", "unixTimeMs": 1752298432843.082520 },
{"activeModifiers":[],"character":"X", "isARepeat":false,"processTimeMs": 61648593.0,"type":"keyUp", "unixTimeMs": 1752298432927.364014 },
{"activeModifiers":[],"character":"I", "isARepeat":false,"processTimeMs": 61648609.0,"type":"keyDown", "unixTimeMs": 1752298432946.094727 },
{"activeModifiers":[],"character":"I", "isARepeat":false,"processTimeMs": 61648687.0,"type":"keyUp", "unixTimeMs": 1752298433026.302734 },
{"activeModifiers":[],"character":"E", "isARepeat":false,"processTimeMs": 61648703.0,"type":"keyDown", "unixTimeMs": 1752298433035.976074 },
{"activeModifiers":[],"character":"E", "isARepeat":false,"processTimeMs": 61648765.0,"type":"keyUp", "unixTimeMs": 1752298433111.006836 },
{"activeModifiers":[],"character":"Space", "isARepeat":false,"processTimeMs": 61648796.0,"type":"keyDown", "unixTimeMs": 1752298433139.742920 },
{"activeModifiers":[],"character":"Space", "isARepeat":false,"processTimeMs": 61648921.0,"type":"keyUp", "unixTimeMs": 1752298433259.728516 },
{"activeModifiers":[],"character":"Z", "isARepeat":false,"processTimeMs": 61649312.0,"type":"keyDown", "unixTimeMs": 1752298433648.677979 },
{"activeModifiers":[],"character":"Z", "isARepeat":false,"processTimeMs": 61649406.0,"type":"keyUp", "unixTimeMs": 1752298433742.361084 },
{"activeModifiers":[],"character":"H", "isARepeat":false,"processTimeMs": 61649421.0,"type":"keyDown", "unixTimeMs": 1752298433765.125488 },
{"activeModifiers":[],"character":"H", "isARepeat":false,"processTimeMs": 61649500.0,"type":"keyUp", "unixTimeMs": 1752298433844.472900 },
{"activeModifiers":[],"character":"I", "isARepeat":false,"processTimeMs": 61649609.0,"type":"keyDown", "unixTimeMs": 1752298433943.092773 },
{"activeModifiers":[],"character":"I", "isARepeat":false,"processTimeMs": 61649671.0,"type":"keyUp", "unixTimeMs": 1752298434010.592285 },
{"activeModifiers":[],"character":"D", "isARepeat":false,"processTimeMs": 61649937.0,"type":"keyDown", "unixTimeMs": 1752298434274.409668 },
{"activeModifiers":[],"character":"E", "isARepeat":false,"processTimeMs": 61650031.0,"type":"keyDown", "unixTimeMs": 1752298434368.163818 },
{"activeModifiers":[],"character":"D", "isARepeat":false,"processTimeMs": 61650062.0,"type":"keyUp", "unixTimeMs": 1752298434402.156738 },
{"activeModifiers":[],"character":"E", "isARepeat":false,"processTimeMs": 61650125.0,"type":"keyUp", "unixTimeMs": 1752298434471.202881 },
{"activeModifiers":[],"character":"U", "isARepeat":false,"processTimeMs": 61650593.0,"type":"keyDown", "unixTimeMs": 1752298434928.078857 },
{"activeModifiers":[],"character":"U", "isARepeat":false,"processTimeMs": 61650640.0,"type":"keyUp", "unixTimeMs": 1752298434974.939697 },
{"activeModifiers":[],"character":"Backspace", "isARepeat":false,"processTimeMs": 61651031.0,"type":"keyDown", "unixTimeMs": 1752298435369.058105 },
{"activeModifiers":[],"character":"Backspace", "isARepeat":false,"processTimeMs": 61651109.0,"type":"keyUp", "unixTimeMs": 1752298435449.214111 },
{"activeModifiers":[],"character":"G", "isARepeat":false,"processTimeMs": 61651312.0,"type":"keyDown", "unixTimeMs": 1752298435651.373291 },
{"activeModifiers":[],"character":"G", "isARepeat":false,"processTimeMs": 61651375.0,"type":"keyUp", "unixTimeMs": 1752298435722.433594 },
{"activeModifiers":[],"character":"U", "isARepeat":false,"processTimeMs": 61651468.0,"type":"keyDown", "unixTimeMs": 1752298435808.777832 },
{"activeModifiers":[],"character":"A", "isARepeat":false,"processTimeMs": 61651484.0,"type":"keyDown", "unixTimeMs": 1752298435829.894043 },
{"activeModifiers":[],"character":"U", "isARepeat":false,"processTimeMs": 61651546.0,"type":"keyUp", "unixTimeMs": 1752298435892.262207 },
{"activeModifiers":[],"character":"A", "isARepeat":false,"processTimeMs": 61651578.0,"type":"keyUp", "unixTimeMs": 1752298435919.257568 },
{"activeModifiers":[],"character":"N", "isARepeat":false,"processTimeMs": 61651640.0,"type":"keyDown", "unixTimeMs": 1752298435975.545166 },
{"activeModifiers":[],"character":"Z", "isARepeat":false,"processTimeMs": 61651687.0,"type":"keyDown", "unixTimeMs": 1752298436022.738037 },
{"activeModifiers":[],"character":"N", "isARepeat":false,"processTimeMs": 61651718.0,"type":"keyUp", "unixTimeMs": 1752298436061.538574 },
{"activeModifiers":[],"character":"Z", "isARepeat":false,"processTimeMs": 61651765.0,"type":"keyUp", "unixTimeMs": 1752298436103.408691 },
{"activeModifiers":[],"character":"H", "isARepeat":false,"processTimeMs": 61651828.0,"type":"keyDown", "unixTimeMs": 1752298436165.052490 },
{"activeModifiers":[],"character":"H", "isARepeat":false,"processTimeMs": 61651890.0,"type":"keyUp", "unixTimeMs": 1752298436236.173584 },
{"activeModifiers":[],"character":"U", "isARepeat":false,"processTimeMs": 61651953.0,"type":"keyDown", "unixTimeMs": 1752298436299.984375 },
{"activeModifiers":[],"character":"Space", "isARepeat":false,"processTimeMs": 61652031.0,"type":"keyDown", "unixTimeMs": 1752298436372.190918 },
{"activeModifiers":[],"character":"U", "isARepeat":false,"processTimeMs": 61652046.0,"type":"keyUp", "unixTimeMs": 1752298436384.837646 },
{"activeModifiers":[],"character":"D", "isARepeat":false,"processTimeMs": 61652109.0,"type":"keyDown", "unixTimeMs": 1752298436444.664307 },
{"activeModifiers":[],"character":"E", "isARepeat":false,"processTimeMs": 61652156.0,"type":"keyDown", "unixTimeMs": 1752298436491.264404 },
{"activeModifiers":[],"character":"Space", "isARepeat":false,"processTimeMs": 61652171.0,"type":"keyUp", "unixTimeMs": 1752298436509.624756 },
{"activeModifiers":[],"character":"D", "isARepeat":false,"processTimeMs": 61652203.0,"type":"keyUp", "unixTimeMs": 1752298436549.851318 },
{"activeModifiers":[],"character":"Space", "isARepeat":false,"processTimeMs": 61652250.0,"type":"keyDown", "unixTimeMs": 1752298436598.114746 },
{"activeModifiers":[],"character":"E", "isARepeat":false,"processTimeMs": 61652296.0,"type":"keyUp", "unixTimeMs": 1752298436633.142334 },
{"activeModifiers":[],"character":"Space", "isARepeat":false,"processTimeMs": 61652375.0,"type":"keyUp", "unixTimeMs": 1752298436722.934326 },
{"activeModifiers":["shift"],"character":"A", "isARepeat":false,"processTimeMs": 61653000.0,"type":"keyDown", "unixTimeMs": 1752298437341.909668 },
{"activeModifiers":["shift"],"character":"I", "isARepeat":false,"processTimeMs": 61653156.0,"type":"keyDown", "unixTimeMs": 1752298437493.224121 },
{"activeModifiers":["shift"],"character":"A", "isARepeat":false,"processTimeMs": 61653171.0,"type":"keyUp", "unixTimeMs": 1752298437514.697754 },
{"activeModifiers":["shift"],"character":"I", "isARepeat":false,"processTimeMs": 61653218.0,"type":"keyUp", "unixTimeMs": 1752298437558.555664 },
{"activeModifiers":[],"character":"Enter", "isARepeat":false,"processTimeMs": 61653421.0,"type":"keyDown", "unixTimeMs": 1752298437764.292725 },
{"activeModifiers":[],"character":"Enter", "isARepeat":false,"processTimeMs": 61653500.0,"type":"keyUp", "unixTimeMs": 1752298437835.677002 },
{"activeModifiers":[],"character":"X", "isARepeat":false,"processTimeMs": 61653609.0,"type":"keyDown", "unixTimeMs": 1752298437952.989990 },
{"activeModifiers":[],"character":"X", "isARepeat":false,"processTimeMs": 61653703.0,"type":"keyUp", "unixTimeMs": 1752298438035.811279 },
{"activeModifiers":[],"character":"I", "isARepeat":false,"processTimeMs": 61653703.0,"type":"keyDown", "unixTimeMs": 1752298438047.649658 },
{"activeModifiers":[],"character":"I", "isARepeat":false,"processTimeMs": 61653781.0,"type":"keyUp", "unixTimeMs": 1752298438127.645996 },
{"activeModifiers":[],"character":"N", "isARepeat":false,"processTimeMs": 61653875.0,"type":"keyDown", "unixTimeMs": 1752298438220.353760 },
{"activeModifiers":[],"character":"N", "isARepeat":false,"processTimeMs": 61653968.0,"type":"keyUp", "unixTimeMs": 1752298438302.408691 },
{"activeModifiers":[],"character":"W", "isARepeat":false,"processTimeMs": 61654015.0,"type":"keyDown", "unixTimeMs": 1752298438348.151611 },
{"activeModifiers":[],"character":"E", "isARepeat":false,"processTimeMs": 61654093.0,"type":"keyDown", "unixTimeMs": 1752298438427.493896 },
{"activeModifiers":[],"character":"W", "isARepeat":false,"processTimeMs": 61654093.0,"type":"keyUp", "unixTimeMs": 1752298438441.495117 },
{"activeModifiers":[],"character":"E", "isARepeat":false,"processTimeMs": 61654187.0,"type":"keyUp", "unixTimeMs": 1752298438521.628906 },
{"activeModifiers":[],"character":"N", "isARepeat":false,"processTimeMs": 61654187.0,"type":"keyDown", "unixTimeMs": 1752298438529.872803 },
{"activeModifiers":[],"character":"N", "isARepeat":false,"processTimeMs": 61654250.0,"type":"keyUp", "unixTimeMs": 1752298438594.782715 },
{"activeModifiers":[],"character":"Space", "isARepeat":false,"processTimeMs": 61654296.0,"type":"keyDown", "unixTimeMs": 1752298438643.754883 },
{"activeModifiers":[],"character":"Space", "isARepeat":false,"processTimeMs": 61654406.0,"type":"keyUp", "unixTimeMs": 1752298438743.572021 },
{"activeModifiers":["shift"],"character":"?", "isARepeat":false,"processTimeMs": 61655046.0,"type":"keyDown", "unixTimeMs": 1752298439391.944092 },
{"activeModifiers":["shift"],"character":"?", "isARepeat":false,"processTimeMs": 61655093.0,"type":"keyUp", "unixTimeMs": 1752298439438.477295 },
{"activeModifiers":[],"character":"Enter", "isARepeat":false,"processTimeMs": 61655296.0,"type":"keyDown", "unixTimeMs": 1752298439643.451660 },
{"activeModifiers":[],"character":"Enter", "isARepeat":false,"processTimeMs": 61655359.0,"type":"keyUp", "unixTimeMs": 1752298439700.372314 },
]