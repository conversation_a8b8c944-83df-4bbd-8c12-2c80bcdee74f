## 1. SOLID 原则

> 【注】：由于 Nest 能够以更加面向对象的方式设计和组织依赖，因此我们==强烈建议遵循 SOLID 原则==。

在软件开发中，SOLID 原则是一组设计原则，旨在帮助开发人员创建更易于维护、扩展和理解的代码。

### 1.1. 单一职责原则（Single Responsibility Principle, SRP）

**原则**：一个类应该只有一个引起变化的原因，即==一个类只负责一项职责==。

**遵循的原因**：

- **简化维护**：每个类只负责一项职责，代码更容易理解和维护。
- **降低耦合**：职责单一的类更容易被复用和测试。

**不遵循的后果**：

- **难以理解**：一个类承担多项职责，代码变得复杂，难以理解。
- **难以维护**：修改一个职责可能会影响其他职责，增加了维护的难度和风险。

**示例**：

```typescript
// 不遵循 SRP 的示例
class UserService {
  createUser() { /* 创建用户逻辑 */ }
  validateUser() { /* 验证用户逻辑 */ }
  sendEmail() { /* 发送邮件逻辑 */ }
}

// 遵循 SRP 的示例
class UserService {
  createUser() { /* 创建用户逻辑 */ }
}

class UserValidationService {
  validateUser() { /* 验证用户逻辑 */ }
}

class EmailService {
  sendEmail() { /* 发送邮件逻辑 */ }
}
```

<br/>

### 1.2. 开放/封闭原则（Open/Closed Principle, OCP）

**原则**：软件实体（类、模块、函数等）应该对扩展开放，对修改封闭。

**遵循的原因**：

- **增强扩展性**：可以通过扩展类的方式添加新功能，而不需要修改现有代码。
- **提高稳定性**：减少对现有代码的修改，降低引入新错误的风险。

**不遵循的后果**：

- **难以扩展**：每次添加新功能都需要修改现有代码，容易引入错误。
- **降低稳定性**：频繁修改现有代码，增加了系统的不稳定性。

**示例**：

```typescript
// 不遵循 OCP 的示例
class PaymentService {
  processPayment(type: string) {
    if (type === 'credit') {
      // 处理信用卡支付
    } else if (type === 'paypal') {
      // 处理 PayPal 支付
    }
  }
}

// 遵循 OCP 的示例
interface PaymentMethod {
  processPayment(): void;
}

class CreditCardPayment implements PaymentMethod {
  processPayment() { /* 处理信用卡支付 */ }
}

class PayPalPayment implements PaymentMethod {
  processPayment() { /* 处理 PayPal 支付 */ }
}


// 通过继承付款方式，来实现扩展不同的支付方式
class PaymentService {
  processPayment(paymentMethod: PaymentMethod) {
    paymentMethod.processPayment();
  }
}
```

<br/>

### 1.3. 里氏替换原则（Liskov Substitution Principle, LSP）

**原则**：子类对象==必须能==够替换其父类对象，并且程序行为保持不变。

**遵循的原因**：

- **提高代码的可替换性**：子类可以无缝替换父类，增强代码的灵活性和复用性。
- **确保行为一致**：子类和父类的行为一致，避免意外行为。

**不遵循的后果**：

- **行为不一致**：子类替换父类时，可能会导致程序行为异常。
- **降低代码的灵活性**：无法安全地替换子类和父类，降低了代码的复用性。

**示例**：

```typescript
// 不遵循 LSP 的示例
class Bird {
  fly() { /* 飞行逻辑 */ }
}

class Penguin extends Bird {
  fly() { throw new Error('Penguins cannot fly'); }
}

// 遵循 LSP 的示例
class Bird {
  move() { /* 移动逻辑 */ }
}

class Sparrow extends Bird {
  move() { /* 飞行逻辑 */ }
}

class Penguin extends Bird {
  move() { /* 游泳逻辑 */ }
}
```

<br/>

### 1.4. 接口隔离原则（Interface Segregation Principle, ISP）

**原则**：客户端不应该被迫依赖它们不使用的接口。

**遵循的原因**：

- **提高接口的灵活性**：接口更小、更专注，客户端只依赖它们需要的接口。
- **降低耦合**：减少不必要的依赖，增强代码的灵活性和可维护性。

**不遵循的后果**：

- **接口臃肿**：接口包含过多方法，客户端被迫依赖不需要的方法。
- **难以实现**：实现类需要实现所有方法，即使有些方法不需要，增加了实现的复杂性。

**示例**：

```typescript
// 不遵循 ISP 的示例
interface Animal {
  eat(): void;
  fly(): void;
  swim(): void;
}

class Dog implements Animal {
  eat() { /* 吃东西逻辑 */ }
  fly() { throw new Error('Dogs cannot fly'); }
  swim() { /* 游泳逻辑 */ }
}

// 遵循 ISP 的示例
interface Eater {
  eat(): void;
}

interface Flyer {
  fly(): void;
}

interface Swimmer {
  swim(): void;
}

class Dog implements Eater, Swimmer {
  eat() { /* 吃东西逻辑 */ }
  swim() { /* 游泳逻辑 */ }
}
```

<br/>

### 1.5. 依赖倒置原则（Dependency Inversion Principle, DIP）

**原则**：高层模块不应该依赖低层模块，二者都应该依赖其抽象；抽象不应该依赖细节，细节应该依赖抽象。

**遵循的原因**：

- **提高模块的独立性**：高层模块和低层模块通过抽象进行解耦，增强模块的独立性和可替换性。
- **增强代码的灵活性**：通过依赖注入等方式，可以轻松替换实现，增强代码的灵活性。

**不遵循的后果**：

- **高耦合**：高层模块直接依赖低层模块，修改低层模块会影响高层模块。
- **难以替换**：无法轻松替换实现，降低了代码的灵活性和可维护性。

**示例**：

```typescript
// 不遵循 DIP 的示例
class MySQLDatabase {
  connect() { /* 连接 MySQL 数据库逻辑 */ }
}

class UserService {
  private db: MySQLDatabase;

  constructor() {
    this.db = new MySQLDatabase();
  }

  getUser() {
    this.db.connect();
    // 获取用户逻辑
  }
}

// 遵循 DIP 的示例
interface Database {
  connect(): void;
}

class MySQLDatabase implements Database {
  connect() { /* 连接 MySQL 数据库逻辑 */ }
}

class UserService {
  // 在需要切换数据库的时候，对数据库的影响最小
  constructor(private db: Database) {}

  getUser() {
    this.db.connect();
    // 获取用户逻辑
  }
}
```

<br/>

## 2. 服务（Service）

```typescript
/* interfaces/cat.interface.ts */
export interface Cat {
  name: string;
  age: number;
  breed: string;
}


/* cats.service.ts */
// nest g service cats 创建服务
import { Injectable } from '@nestjs/common';
import { Cat } from './interfaces/cat.interface';

@Injectable()
export class CatsService {
  private readonly cats: Cat[] = [];

  create(cat: Cat) {
    this.cats.push(cat);
  }

  findAll(): Cat[] {
    return this.cats;
  }
}


/* cats.controller.ts */
import { Controller, Get, Post, Body } from '@nestjs/common';
import { CreateCatDto } from './dto/create-cat.dto';
import { CatsService } from './cats.service';
import { Cat } from './interfaces/cat.interface';

@Controller('cats')
export class CatsController {
  // CatsService 通过类构造函数注入。
  // 请注意 private 语法的使用。
  // 这种简写允许我们立即在同一位置声明和初始化 catsService 成员。
  constructor(private catsService: CatsService) {}

  @Post()
  async create(@Body() createCatDto: CreateCatDto) {
    // 在服务中定义接口类，在这里使用实体类
    this.catsService.create(createCatDto);
  }

  @Get()
  async findAll(): Promise<Cat[]> {
    return this.catsService.findAll();
  }
}
```

<br/>

## 3. 依赖注入（Dependency Injection, DI）

### 3.1. 依赖注入概述

依赖注入是一种设计模式，用于将类的依赖项（即类需要的服务或对象）从类中分离出来，并通过构造函数或属性注入的方式提供给类。NestJS 使用依赖注入来管理和提供应用程序中的各种服务和模块。

### 主要概念

1. **提供者（Provider）**：
   - 提供者是一个可以被注入到其他类中的类。它通常是一个服务（Service），但也可以是其他类型的类。
   - 提供者通过 `@Injectable()` 装饰器标记。
2. **模块（Module）**：
   - 模块是一个组织提供者、控制器和其他模块的类。它通过 `@Module()` 装饰器定义。
   - 模块可以导入其他模块，以便共享其提供者。
3. **控制器（Controller）**：
   - 控制器是处理传入请求并返回响应的类。它通过 `@Controller()` 装饰器定义。
   - 控制器可以注入提供者，以便使用其服务。

### 依赖注入的实现

#### 3.1.1. 定义提供者

提供者是一个带有 `@Injectable()` 装饰器的类。它可以包含业务逻辑，并可以被注入到其他类中。

```typescript
import { Injectable } from '@nestjs/common';

@Injectable()
export class CatsService {
  private readonly cats = [];

  findAll(): string[] {
    return this.cats;
  }

  create(cat: string) {
    this.cats.push(cat);
  }
}
```

#### 3.1.2. 定义模块

模块是一个带有 `@Module()` 装饰器的类。它组织和管理提供者、控制器和其他模块。

```typescript
import { Module } from '@nestjs/common';
import { CatsService } from './cats.service';
import { CatsController } from './cats.controller';

@Module({
  providers: [CatsService],
  controllers: [CatsController],
})
export class CatsModule {}
```

#### 3.1.3. 注入提供者

控制器可以通过构造函数注入提供者，以便使用其服务。

```typescript
import { Controller, Get, Post, Body } from '@nestjs/common';
import { CatsService } from './cats.service';

@Controller('cats')
export class CatsController {
  
  constructor(private readonly catsService: CatsService) {}

  @Get()
  findAll(): string[] {
    return this.catsService.findAll();
  }

  @Post()
  create(@Body() cat: string) {
    this.catsService.create(cat);
  }
}
```

### 3.2. 总结

- **提供者**：使用 `@Injectable()` 装饰器标记的类，可以包含业务逻辑。
- **模块**：使用 `@Module()` 装饰器定义的类，组织和管理提供者、控制器和其他模块。
- **控制器**：使用 `@Controller()` 装饰器定义的类，处理传入请求并返回响应，可以通过构造函数注入提供者。

> 通过依赖注入，NestJS 提供了一种灵活且可维护的方式来管理应用程序中的依赖项。希望这个简洁明了的介绍能帮助你理解 NestJS 中的依赖注入。

<br/>

## 4. 作用域（Scope）

提供程序通常具有与应用生命周期同步的生命周期 ("scope")。==启动应用时，必须解析每个依赖，因此必须实例化每个提供程序。同样，当应用关闭时，每个提供器都将被销毁==。但是，也有一些方法可以使你的提供程序生命周期限定在请求范围内。

这个后续会专门有一个章节来介绍作用域的用法。

> 【简言之】：他主要是用来定义注入依赖的一些特性，比如是不是单例，需不需要销毁之类的。

<br/>

## 5. 自定义提供者

Nest 有一个内置的控制反转 ("IoC") 容器，可以解决提供器之间的关系。此功能是上述依赖注入功能的基础，但实际上比我们目前所描述的功能强大得多。有几种定义提供器的方法：你可以使用普通值、类以及异步或同步工厂。

这个后续会专门有一个章节来介绍自定义提供者的用法。

<br/>

## 6. 可选提供者（Optional）

在 NestJS 中，可选提供者（Optional Providers）允许你在某些情况下注入依赖项时，==如果依赖项不存在，不会抛出错误==，而是注入一个 `undefined` 值。这在某些情况下非常有用，例如，当你希望某个依赖项是可选的，或者在某些环境中可能不存在时。

### 6.1. 使用可选提供者

要使用可选提供者，你需要使用 `@Optional()` 装饰器。以下是一个示例，展示了如何在 NestJS 中使用可选提供者。

#### 6.1.1. 示例

假设你有一个可选的日志服务（`LoggingService`），它在某些环境中可能不存在。

1. **定义可选的提供者**

```typescript
import { Injectable, Optional } from '@nestjs/common';

@Injectable()
export class LoggingService {
  log(message: string) {
    console.log(message);
  }
}
```

2. **在需要注入的地方使用 `@Optional()` 装饰器**

```typescript
import { Injectable, Optional } from '@nestjs/common';
import { LoggingService } from './logging.service';

@Injectable()
export class CatsService {
  constructor(@Optional() private readonly loggingService: LoggingService) {}

  findAll(): string[] {
    if (this.loggingService) {
      this.loggingService.log('Fetching all cats');
    }
    return ['cat1', 'cat2'];
  }
}
```

3. **在模块中配置提供者**

```typescript
import { Module } from '@nestjs/common';
import { CatsService } from './cats.service';
import { LoggingService } from './logging.service';

@Module({
  providers: [CatsService, LoggingService], // 可以选择性地注释掉 LoggingService
})
export class CatsModule {}
```

<br/>

#### 6.1.2. 注意事项

1. **确保提供者是可选的**：
   - 使用 `@Optional()` 装饰器来标记依赖项是可选的。
   - 在使用可选提供者时，记得检查其是否存在，以避免 `undefined` 引发的错误。
2. **模块配置**：
   - 在模块中配置提供者时，可以选择性地注释掉某些提供者，以模拟其不存在的情况。
3. **默认值**：
   - 如果需要，可以为可选提供者设置默认值，以确保在依赖项不存在时有一个合理的默认行为。

<br/>

#### 6.1.3.  示例总结

结合你提供的代码片段，我们可以在 [`CatsModule`](command:_github.copilot.openSymbolFromReferences?%5B%22CatsModule%22%2C%5B%7B%22uri%22%3A%7B%22%24mid%22%3A1%2C%22fsPath%22%3A%22d%3A%5C%5CLeaning%5C%5CNode%5C%5CNestJs%5C%5Clearn-nest%5C%5Csrc%5C%5Capp.module.ts%22%2C%22_sep%22%3A1%2C%22external%22%3A%22file%3A%2F%2F%2Fd%253A%2FLeaning%2FNode%2FNestJs%2Flearn-nest%2Fsrc%2Fapp.module.ts%22%2C%22path%22%3A%22%2Fd%3A%2FLeaning%2FNode%2FNestJs%2Flearn-nest%2Fsrc%2Fapp.module.ts%22%2C%22scheme%22%3A%22file%22%7D%2C%22pos%22%3A%7B%22line%22%3A5%2C%22character%22%3A9%7D%7D%5D%5D "Go to definition") 中使用可选提供者：

```typescript
/* cats.service.ts */
import { Injectable, Optional } from '@nestjs/common';
import { LoggingService } from './logging.service';

@Injectable()
export class CatsService {
  constructor(@Optional() private readonly loggingService: LoggingService) {}

  findAll(): string[] {
    if (this.loggingService) {
      this.loggingService.log('Fetching all cats');
    }
    return ['cat1', 'cat2'];
  }
}

/* cats.module.ts */
import { Module } from '@nestjs/common';
import { CatsService } from './cats.service';
import { LoggingService } from './logging.service';

@Module({
  providers: [CatsService, LoggingService], // 可以选择性地注释掉 LoggingService
})
export class CatsModule {}
```

> 通过这种方式，你可以在 NestJS 中灵活地使用可选提供者，以适应不同的环境和需求。如果你有更多问题，请随时提问！

<br/>

## 7. 基于属性的依赖注入

好的，我们将通过具体的示例来更直观地展示传统的构造函数依赖注入和基于属性的依赖注入的优缺点。

### 7.1. 传统的构造函数依赖注入

#### 优点

1. **类型安全**：构造函数注入依赖于 TypeScript 的类型系统，提供更严格的类型检查。
2. **初始化顺序**：依赖项在类实例化时就已经准备好，可以在构造函数中使用这些依赖项。
3. **测试方便**：在单元测试中，可以通过构造函数参数轻松地注入模拟依赖项。

#### 缺点

1. **构造函数参数冗长**：当类需要注入多个依赖项时，构造函数参数列表可能变得非常冗长。
2. **灵活性较低**：依赖项只能在类实例化时注入，无法在类的生命周期中动态注入。

#### 示例

```typescript
import { Injectable } from '@nestjs/common';
import { LoggingService } from './logging.service';
import { ConfigService } from './config.service';

@Injectable()
export class CatsService {
  constructor(
    private readonly loggingService: LoggingService,
    private readonly configService: ConfigService,
  ) {}

  findAll(): string[] {
    this.loggingService.log('Fetching all cats');
    const config = this.configService.getConfig();
    return ['cat1', 'cat2'];
  }
}
```

### 7.2. 基于属性的依赖注入

#### 优点

1. **简洁性**：代码更简洁，避免了在构造函数中声明大量参数。
2. **灵活性**：可以在类的任何地方注入依赖项，而不仅限于构造函数。
3. **可读性**：属性注入使得依赖项一目了然，代码更具可读性。

#### 缺点

1. **测试困难**：基于属性的依赖注入在单元测试中可能不如构造函数注入直观，因为需要手动设置属性。
2. **延迟初始化**：属性注入的依赖项在类实例化后才会被注入，可能导致在构造函数中无法使用这些依赖项。
3. **类型安全**：由于属性注入依赖于装饰器，可能在某些情况下不如构造函数注入的类型检查严格。

#### 示例

```typescript
import { Injectable, Inject } from '@nestjs/common';
import { LoggingService } from './logging.service';
import { ConfigService } from './config.service';

@Injectable()
export class CatsService {
  @Inject()
  private readonly loggingService: LoggingService;

  @Inject()
  private readonly configService: ConfigService;

  findAll(): string[] {
    this.loggingService.log('Fetching all cats');
    const config = this.configService.getConfig();
    return ['cat1', 'cat2'];
  }
}
```

### 7.3. 测试示例

#### 传统的构造函数依赖注入测试

```typescript
import { Test, TestingModule } from '@nestjs/testing';
import { CatsService } from './cats.service';
import { LoggingService } from './logging.service';
import { ConfigService } from './config.service';

describe('CatsService', () => {
  let service: CatsService;
  let loggingService: LoggingService;
  let configService: ConfigService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        CatsService,
        {
          provide: LoggingService,
          useValue: { log: jest.fn() },
        },
        {
          provide: ConfigService,
          useValue: { getConfig: jest.fn().mockReturnValue({}) },
        },
      ],
    }).compile();

    service = module.get<CatsService>(CatsService);
    loggingService = module.get<LoggingService>(LoggingService);
    configService = module.get<ConfigService>(ConfigService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  it('should log and fetch all cats', () => {
    service.findAll();
    expect(loggingService.log).toHaveBeenCalledWith('Fetching all cats');
  });
});
```

#### 基于属性的依赖注入测试

```typescript
import { Test, TestingModule } from '@nestjs/testing';
import { CatsService } from './cats.service';
import { LoggingService } from './logging.service';
import { ConfigService } from './config.service';

describe('CatsService', () => {
  let service: CatsService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        CatsService,
        {
          provide: LoggingService,
          useValue: { log: jest.fn() },
        },
        {
          provide: ConfigService,
          useValue: { getConfig: jest.fn().mockReturnValue({}) },
        },
      ],
    }).compile();

    service = module.get<CatsService>(CatsService);
    // 手动设置属性
    (service as any).loggingService = module.get<LoggingService>(LoggingService);
    (service as any).configService = module.get<ConfigService>(ConfigService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  it('should log and fetch all cats', () => {
    service.findAll();
    expect((service as any).loggingService.log).toHaveBeenCalledWith('Fetching all cats');
  });
});
```

### 7.4. 总结

- **传统的构造函数依赖注入**：类型安全、初始化顺序明确、测试方便，但在处理多个依赖项时可能显得冗长。
- **基于属性的依赖注入**：代码简洁、灵活、可读性高，但在测试和类型安全性上可能存在一些问题。

通过这些示例，你可以更直观地看到两种依赖注入方式的优缺点。如果你有更多问题，请随时提问！

<br/>

## 8. 提供者注册

现在我们已经定义了一个提供器 (CatsService)，并且我们有了该服务的一个消费者 (CatsController)，我们需要向 Nest 注册该服务，以便它可以执行注入。我们通过编辑模块文件 (app.module.ts) 并将服务添加到 @Module() 装饰器的 providers 数组来完成此操作。

```typescript
import { Module } from '@nestjs/common';
import { CatsController } from './cats/cats.controller';
import { CatsService } from './cats/cats.service';

@Module({
  controllers: [CatsController],
  providers: [CatsService],
})
export class AppModule {}
```

> 【目录结构】：
> 
> src
> 
> - cats
>   - dto
>     - create-cat.dto.ts
> - interfaces
>   - cat.interface.ts
> - cats.controller.ts
> - cats.controller.ts
> - app.module.ts
> - main.ts