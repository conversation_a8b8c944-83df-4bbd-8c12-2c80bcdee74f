[

{"activeModifiers":[],"character":"Z", "isARepeat":false,"processTimeMs": 55242937.0,"type":"keyDown", "unixTimeMs": 1752292027264.892090 },
{"activeModifiers":[],"character":"Z", "isARepeat":false,"processTimeMs": 55243031.0,"type":"keyUp", "unixTimeMs": 1752292027371.397705 },
{"activeModifiers":[],"character":"H", "isARepeat":false,"processTimeMs": 55243078.0,"type":"keyDown", "unixTimeMs": 1752292027413.218750 },
{"activeModifiers":[],"character":"E", "isARepeat":false,"processTimeMs": 55243125.0,"type":"keyDown", "unixTimeMs": 1752292027470.886963 },
{"activeModifiers":[],"character":"H", "isARepeat":false,"processTimeMs": 55243187.0,"type":"keyUp", "unixTimeMs": 1752292027527.214111 },
{"activeModifiers":[],"character":"E", "isARepeat":false,"processTimeMs": 55243218.0,"type":"keyUp", "unixTimeMs": 1752292027552.975830 },
{"activeModifiers":[],"character":"Space", "isARepeat":false,"processTimeMs": 55244015.0,"type":"keyDown", "unixTimeMs": 1752292028348.420898 },
{"activeModifiers":[],"character":"Space", "isARepeat":false,"processTimeMs": 55244093.0,"type":"keyUp", "unixTimeMs": 1752292028427.484131 },
{"activeModifiers":[],"character":"Z", "isARepeat":false,"processTimeMs": 55246421.0,"type":"keyDown", "unixTimeMs": 1752292030758.245605 },
{"activeModifiers":[],"character":"Z", "isARepeat":false,"processTimeMs": 55246500.0,"type":"keyUp", "unixTimeMs": 1752292030841.274902 },
{"activeModifiers":[],"character":"H", "isARepeat":false,"processTimeMs": 55246515.0,"type":"keyDown", "unixTimeMs": 1752292030852.348877 },
{"activeModifiers":[],"character":"H", "isARepeat":false,"processTimeMs": 55246578.0,"type":"keyUp", "unixTimeMs": 1752292030921.708008 },
{"activeModifiers":[],"character":"E", "isARepeat":false,"processTimeMs": 55246609.0,"type":"keyDown", "unixTimeMs": 1752292030951.629883 },
{"activeModifiers":[],"character":"E", "isARepeat":false,"processTimeMs": 55246671.0,"type":"keyUp", "unixTimeMs": 1752292031013.845215 },
{"activeModifiers":[],"character":"Y", "isARepeat":false,"processTimeMs": 55247359.0,"type":"keyDown", "unixTimeMs": 1752292031693.197998 },
{"activeModifiers":[],"character":"Y", "isARepeat":false,"processTimeMs": 55247421.0,"type":"keyUp", "unixTimeMs": 1752292031769.939697 },
{"activeModifiers":[],"character":"I", "isARepeat":false,"processTimeMs": 55247546.0,"type":"keyDown", "unixTimeMs": 1752292031888.068115 },
{"activeModifiers":[],"character":"I", "isARepeat":false,"processTimeMs": 55247625.0,"type":"keyUp", "unixTimeMs": 1752292031960.157227 },
{"activeModifiers":[],"character":"Z", "isARepeat":false,"processTimeMs": 55247687.0,"type":"keyDown", "unixTimeMs": 1752292032031.981689 },
{"activeModifiers":[],"character":"H", "isARepeat":false,"processTimeMs": 55247750.0,"type":"keyDown", "unixTimeMs": 1752292032087.694092 },
{"activeModifiers":[],"character":"Z", "isARepeat":false,"processTimeMs": 55247781.0,"type":"keyUp", "unixTimeMs": 1752292032116.335693 },
{"activeModifiers":[],"character":"H", "isARepeat":false,"processTimeMs": 55247828.0,"type":"keyUp", "unixTimeMs": 1752292032162.149902 },
{"activeModifiers":[],"character":"O", "isARepeat":false,"processTimeMs": 55247953.0,"type":"keyDown", "unixTimeMs": 1752292032289.235596 },
{"activeModifiers":[],"character":"O", "isARepeat":false,"processTimeMs": 55248015.0,"type":"keyUp", "unixTimeMs": 1752292032358.900879 },
{"activeModifiers":[],"character":"U", "isARepeat":false,"processTimeMs": 55248093.0,"type":"keyDown", "unixTimeMs": 1752292032441.120117 },
{"activeModifiers":[],"character":"U", "isARepeat":false,"processTimeMs": 55248156.0,"type":"keyUp", "unixTimeMs": 1752292032503.244141 },
{"activeModifiers":[],"character":"Space", "isARepeat":false,"processTimeMs": 55248187.0,"type":"keyDown", "unixTimeMs": 1752292032525.165771 },
{"activeModifiers":[],"character":"Space", "isARepeat":false,"processTimeMs": 55248265.0,"type":"keyUp", "unixTimeMs": 1752292032603.483154 },
{"activeModifiers":[],"character":"N", "isARepeat":false,"processTimeMs": 55248296.0,"type":"keyDown", "unixTimeMs": 1752292032637.355713 },
{"activeModifiers":[],"character":"N", "isARepeat":false,"processTimeMs": 55248375.0,"type":"keyUp", "unixTimeMs": 1752292032709.150391 },
{"activeModifiers":[],"character":"E", "isARepeat":false,"processTimeMs": 55248375.0,"type":"keyDown", "unixTimeMs": 1752292032718.186279 },
{"activeModifiers":[],"character":"E", "isARepeat":false,"processTimeMs": 55248437.0,"type":"keyUp", "unixTimeMs": 1752292032775.581299 },
{"activeModifiers":[],"character":"I", "isARepeat":false,"processTimeMs": 55248484.0,"type":"keyDown", "unixTimeMs": 1752292032826.162354 },
{"activeModifiers":[],"character":"I", "isARepeat":false,"processTimeMs": 55248562.0,"type":"keyUp", "unixTimeMs": 1752292032898.506104 },
{"activeModifiers":[],"character":"Space", "isARepeat":false,"processTimeMs": 55248578.0,"type":"keyDown", "unixTimeMs": 1752292032924.977539 },
{"activeModifiers":[],"character":"Space", "isARepeat":false,"processTimeMs": 55248656.0,"type":"keyUp", "unixTimeMs": 1752292032989.673584 },
{"activeModifiers":[],"character":"Y", "isARepeat":false,"processTimeMs": 55248703.0,"type":"keyDown", "unixTimeMs": 1752292033047.402100 },
{"activeModifiers":[],"character":"Y", "isARepeat":false,"processTimeMs": 55248796.0,"type":"keyUp", "unixTimeMs": 1752292033134.788086 },
{"activeModifiers":[],"character":"O", "isARepeat":false,"processTimeMs": 55248921.0,"type":"keyDown", "unixTimeMs": 1752292033263.272705 },
{"activeModifiers":[],"character":"O", "isARepeat":false,"processTimeMs": 55248984.0,"type":"keyUp", "unixTimeMs": 1752292033332.113281 },
{"activeModifiers":[],"character":"U", "isARepeat":false,"processTimeMs": 55249078.0,"type":"keyDown", "unixTimeMs": 1752292033418.445557 },
{"activeModifiers":[],"character":"U", "isARepeat":false,"processTimeMs": 55249156.0,"type":"keyUp", "unixTimeMs": 1752292033491.208008 },
{"activeModifiers":[],"character":"S", "isARepeat":false,"processTimeMs": 55249203.0,"type":"keyDown", "unixTimeMs": 1752292033541.558105 },
{"activeModifiers":[],"character":"S", "isARepeat":false,"processTimeMs": 55249265.0,"type":"keyUp", "unixTimeMs": 1752292033603.386475 },
{"activeModifiers":[],"character":"H", "isARepeat":false,"processTimeMs": 55249265.0,"type":"keyDown", "unixTimeMs": 1752292033612.510742 },
{"activeModifiers":[],"character":"H", "isARepeat":false,"processTimeMs": 55249343.0,"type":"keyUp", "unixTimeMs": 1752292033689.830078 },
{"activeModifiers":[],"character":"E", "isARepeat":false,"processTimeMs": 55249390.0,"type":"keyDown", "unixTimeMs": 1752292033724.706055 },
{"activeModifiers":[],"character":"E", "isARepeat":false,"processTimeMs": 55249453.0,"type":"keyUp", "unixTimeMs": 1752292033792.650391 },
{"activeModifiers":[],"character":"N", "isARepeat":false,"processTimeMs": 55249468.0,"type":"keyDown", "unixTimeMs": 1752292033804.798096 },
{"activeModifiers":[],"character":"N", "isARepeat":false,"processTimeMs": 55249531.0,"type":"keyUp", "unixTimeMs": 1752292033871.607666 },
{"activeModifiers":[],"character":"M", "isARepeat":false,"processTimeMs": 55249625.0,"type":"keyDown", "unixTimeMs": 1752292033959.828857 },
{"activeModifiers":[],"character":"E", "isARepeat":false,"processTimeMs": 55249687.0,"type":"keyDown", "unixTimeMs": 1752292034026.604492 },
{"activeModifiers":[],"character":"M", "isARepeat":false,"processTimeMs": 55249718.0,"type":"keyUp", "unixTimeMs": 1752292034036.501709 },
{"activeModifiers":[],"character":"E", "isARepeat":false,"processTimeMs": 55249781.0,"type":"keyUp", "unixTimeMs": 1752292034123.359619 },
{"activeModifiers":[],"character":"Space", "isARepeat":false,"processTimeMs": 55250328.0,"type":"keyDown", "unixTimeMs": 1752292034673.650879 },
{"activeModifiers":[],"character":"Space", "isARepeat":false,"processTimeMs": 55250421.0,"type":"keyUp", "unixTimeMs": 1752292034754.633545 },
{"activeModifiers":[],"character":"H", "isARepeat":false,"processTimeMs": 55250734.0,"type":"keyDown", "unixTimeMs": 1752292035082.328369 },
{"activeModifiers":[],"character":"H", "isARepeat":false,"processTimeMs": 55250828.0,"type":"keyUp", "unixTimeMs": 1752292035162.418701 },
{"activeModifiers":[],"character":"I", "isARepeat":false,"processTimeMs": 55250921.0,"type":"keyDown", "unixTimeMs": 1752292035261.052734 },
{"activeModifiers":[],"character":"I", "isARepeat":false,"processTimeMs": 55251015.0,"type":"keyUp", "unixTimeMs": 1752292035352.028076 },
{"activeModifiers":[],"character":"D", "isARepeat":false,"processTimeMs": 55251015.0,"type":"keyDown", "unixTimeMs": 1752292035362.890869 },
{"activeModifiers":[],"character":"E", "isARepeat":false,"processTimeMs": 55251140.0,"type":"keyDown", "unixTimeMs": 1752292035474.144043 },
{"activeModifiers":[],"character":"D", "isARepeat":false,"processTimeMs": 55251156.0,"type":"keyUp", "unixTimeMs": 1752292035501.628174 },
{"activeModifiers":[],"character":"E", "isARepeat":false,"processTimeMs": 55251218.0,"type":"keyUp", "unixTimeMs": 1752292035565.757080 },
{"activeModifiers":[],"character":"Space", "isARepeat":false,"processTimeMs": 55251953.0,"type":"keyDown", "unixTimeMs": 1752292036292.759277 },
{"activeModifiers":[],"character":"Space", "isARepeat":false,"processTimeMs": 55252062.0,"type":"keyUp", "unixTimeMs": 1752292036399.999756 },
{"activeModifiers":[],"character":"Backspace", "isARepeat":false,"processTimeMs": 55252140.0,"type":"keyDown", "unixTimeMs": 1752292036485.739502 },
{"activeModifiers":[],"character":"Backspace", "isARepeat":false,"processTimeMs": 55252203.0,"type":"keyUp", "unixTimeMs": 1752292036545.791992 },
{"activeModifiers":[],"character":"Backspace", "isARepeat":false,"processTimeMs": 55252390.0,"type":"keyDown", "unixTimeMs": 1752292036729.226074 },
{"activeModifiers":[],"character":"Backspace", "isARepeat":false,"processTimeMs": 55252500.0,"type":"keyUp", "unixTimeMs": 1752292036842.387451 },
{"activeModifiers":[],"character":"Z", "isARepeat":false,"processTimeMs": 55252718.0,"type":"keyDown", "unixTimeMs": 1752292037058.370117 },
{"activeModifiers":[],"character":"Z", "isARepeat":false,"processTimeMs": 55252796.0,"type":"keyUp", "unixTimeMs": 1752292037141.097168 },
{"activeModifiers":[],"character":"H", "isARepeat":false,"processTimeMs": 55252843.0,"type":"keyDown", "unixTimeMs": 1752292037181.145508 },
{"activeModifiers":[],"character":"H", "isARepeat":false,"processTimeMs": 55252937.0,"type":"keyUp", "unixTimeMs": 1752292037275.414307 },
{"activeModifiers":[],"character":"I", "isARepeat":false,"processTimeMs": 55253015.0,"type":"keyDown", "unixTimeMs": 1752292037356.123535 },
{"activeModifiers":[],"character":"I", "isARepeat":false,"processTimeMs": 55253078.0,"type":"keyUp", "unixTimeMs": 1752292037420.050537 },
{"activeModifiers":[],"character":"D", "isARepeat":false,"processTimeMs": 55253171.0,"type":"keyDown", "unixTimeMs": 1752292037512.821533 },
{"activeModifiers":[],"character":"E", "isARepeat":false,"processTimeMs": 55253218.0,"type":"keyDown", "unixTimeMs": 1752292037560.907227 },
{"activeModifiers":[],"character":"D", "isARepeat":false,"processTimeMs": 55253281.0,"type":"keyUp", "unixTimeMs": 1752292037618.312988 },
{"activeModifiers":[],"character":"E", "isARepeat":false,"processTimeMs": 55253343.0,"type":"keyUp", "unixTimeMs": 1752292037681.199219 },
{"activeModifiers":[],"character":"Space", "isARepeat":false,"processTimeMs": 55253859.0,"type":"keyDown", "unixTimeMs": 1752292038205.608643 },
{"activeModifiers":[],"character":"Space", "isARepeat":false,"processTimeMs": 55253953.0,"type":"keyUp", "unixTimeMs": 1752292038300.158203 },
{"activeModifiers":[],"character":"W", "isARepeat":false,"processTimeMs": 55254046.0,"type":"keyDown", "unixTimeMs": 1752292038385.490723 },
{"activeModifiers":[],"character":"W", "isARepeat":false,"processTimeMs": 55254125.0,"type":"keyUp", "unixTimeMs": 1752292038459.325195 },
{"activeModifiers":[],"character":"O", "isARepeat":false,"processTimeMs": 55254171.0,"type":"keyDown", "unixTimeMs": 1752292038506.964355 },
{"activeModifiers":[],"character":"O", "isARepeat":false,"processTimeMs": 55254234.0,"type":"keyUp", "unixTimeMs": 1752292038579.352051 },
{"activeModifiers":[],"character":"M", "isARepeat":false,"processTimeMs": 55254343.0,"type":"keyDown", "unixTimeMs": 1752292038689.352051 },
{"activeModifiers":[],"character":"M", "isARepeat":false,"processTimeMs": 55254437.0,"type":"keyUp", "unixTimeMs": 1752292038782.947266 },
{"activeModifiers":[],"character":"E", "isARepeat":false,"processTimeMs": 55254453.0,"type":"keyDown", "unixTimeMs": 1752292038792.143311 },
{"activeModifiers":[],"character":"E", "isARepeat":false,"processTimeMs": 55254531.0,"type":"keyUp", "unixTimeMs": 1752292038863.922607 },
{"activeModifiers":[],"character":"N", "isARepeat":false,"processTimeMs": 55254546.0,"type":"keyDown", "unixTimeMs": 1752292038892.207520 },
{"activeModifiers":[],"character":"N", "isARepeat":false,"processTimeMs": 55254640.0,"type":"keyUp", "unixTimeMs": 1752292038976.970459 },
{"activeModifiers":[],"character":"Space", "isARepeat":false,"processTimeMs": 55254718.0,"type":"keyDown", "unixTimeMs": 1752292039064.848389 },
{"activeModifiers":[],"character":"Space", "isARepeat":false,"processTimeMs": 55254812.0,"type":"keyUp", "unixTimeMs": 1752292039156.701416 },
{"activeModifiers":[],"character":"G", "isARepeat":false,"processTimeMs": 55254953.0,"type":"keyDown", "unixTimeMs": 1752292039288.021729 },
{"activeModifiers":[],"character":"G", "isARepeat":false,"processTimeMs": 55255031.0,"type":"keyUp", "unixTimeMs": 1752292039376.694336 },
{"activeModifiers":[],"character":"U", "isARepeat":false,"processTimeMs": 55255156.0,"type":"keyDown", "unixTimeMs": 1752292039490.709961 },
{"activeModifiers":[],"character":"A", "isARepeat":false,"processTimeMs": 55255218.0,"type":"keyDown", "unixTimeMs": 1752292039551.533203 },
{"activeModifiers":[],"character":"U", "isARepeat":false,"processTimeMs": 55255218.0,"type":"keyUp", "unixTimeMs": 1752292039566.035645 },
{"activeModifiers":[],"character":"A", "isARepeat":false,"processTimeMs": 55255281.0,"type":"keyUp", "unixTimeMs": 1752292039623.543213 },
{"activeModifiers":[],"character":"N", "isARepeat":false,"processTimeMs": 55255328.0,"type":"keyDown", "unixTimeMs": 1752292039660.664795 },
{"activeModifiers":[],"character":"N", "isARepeat":false,"processTimeMs": 55255406.0,"type":"keyUp", "unixTimeMs": 1752292039745.625244 },
{"activeModifiers":[],"character":"Z", "isARepeat":false,"processTimeMs": 55255484.0,"type":"keyDown", "unixTimeMs": 1752292039821.012207 },
{"activeModifiers":[],"character":"Z", "isARepeat":false,"processTimeMs": 55255578.0,"type":"keyUp", "unixTimeMs": 1752292039914.971924 },
{"activeModifiers":[],"character":"H", "isARepeat":false,"processTimeMs": 55255578.0,"type":"keyDown", "unixTimeMs": 1752292039923.069580 },
{"activeModifiers":[],"character":"H", "isARepeat":false,"processTimeMs": 55255656.0,"type":"keyUp", "unixTimeMs": 1752292039992.327881 },
{"activeModifiers":[],"character":"U", "isARepeat":false,"processTimeMs": 55255734.0,"type":"keyDown", "unixTimeMs": 1752292040071.060303 },
{"activeModifiers":[],"character":"U", "isARepeat":false,"processTimeMs": 55255812.0,"type":"keyUp", "unixTimeMs": 1752292040159.219482 },
{"activeModifiers":[],"character":"Space", "isARepeat":false,"processTimeMs": 55255828.0,"type":"keyDown", "unixTimeMs": 1752292040168.766113 },
{"activeModifiers":[],"character":"D", "isARepeat":false,"processTimeMs": 55255890.0,"type":"keyDown", "unixTimeMs": 1752292040229.021973 },
{"activeModifiers":[],"character":"Space", "isARepeat":false,"processTimeMs": 55255953.0,"type":"keyUp", "unixTimeMs": 1752292040286.750244 },
{"activeModifiers":[],"character":"E", "isARepeat":false,"processTimeMs": 55255953.0,"type":"keyDown", "unixTimeMs": 1752292040298.034424 },
{"activeModifiers":[],"character":"D", "isARepeat":false,"processTimeMs": 55256031.0,"type":"keyUp", "unixTimeMs": 1752292040364.699951 },
{"activeModifiers":[],"character":"Space", "isARepeat":false,"processTimeMs": 55256046.0,"type":"keyDown", "unixTimeMs": 1752292040385.949951 },
{"activeModifiers":[],"character":"E", "isARepeat":false,"processTimeMs": 55256093.0,"type":"keyUp", "unixTimeMs": 1752292040436.890137 },
{"activeModifiers":[],"character":"Space", "isARepeat":false,"processTimeMs": 55256156.0,"type":"keyUp", "unixTimeMs": 1752292040496.339111 },
{"activeModifiers":["shift"],"character":"A", "isARepeat":false,"processTimeMs": 55256765.0,"type":"keyDown", "unixTimeMs": 1752292041107.851318 },
{"activeModifiers":["shift"],"character":"I", "isARepeat":false,"processTimeMs": 55256937.0,"type":"keyDown", "unixTimeMs": 1752292041272.292725 },
{"activeModifiers":["shift"],"character":"A", "isARepeat":false,"processTimeMs": 55256984.0,"type":"keyUp", "unixTimeMs": 1752292041329.689941 },
{"activeModifiers":["shift"],"character":"I", "isARepeat":false,"processTimeMs": 55257031.0,"type":"keyUp", "unixTimeMs": 1752292041365.678711 },
{"activeModifiers":[],"character":"Enter", "isARepeat":false,"processTimeMs": 55257281.0,"type":"keyDown", "unixTimeMs": 1752292041621.290771 },
{"activeModifiers":[],"character":"Enter", "isARepeat":false,"processTimeMs": 55257375.0,"type":"keyUp", "unixTimeMs": 1752292041709.890625 },
{"activeModifiers":[],"character":"X", "isARepeat":false,"processTimeMs": 55257593.0,"type":"keyDown", "unixTimeMs": 1752292041929.752197 },
{"activeModifiers":[],"character":"I", "isARepeat":false,"processTimeMs": 55257671.0,"type":"keyDown", "unixTimeMs": 1752292042004.874023 },
{"activeModifiers":[],"character":"X", "isARepeat":false,"processTimeMs": 55257687.0,"type":"keyUp", "unixTimeMs": 1752292042023.661133 },
{"activeModifiers":[],"character":"I", "isARepeat":false,"processTimeMs": 55257750.0,"type":"keyUp", "unixTimeMs": 1752292042088.063965 },
{"activeModifiers":[],"character":"N", "isARepeat":false,"processTimeMs": 55257843.0,"type":"keyDown", "unixTimeMs": 1752292042189.879150 },
{"activeModifiers":[],"character":"N", "isARepeat":false,"processTimeMs": 55257937.0,"type":"keyUp", "unixTimeMs": 1752292042282.980713 },
{"activeModifiers":[],"character":"W", "isARepeat":false,"processTimeMs": 55258031.0,"type":"keyDown", "unixTimeMs": 1752292042373.144043 },
{"activeModifiers":[],"character":"E", "isARepeat":false,"processTimeMs": 55258062.0,"type":"keyDown", "unixTimeMs": 1752292042406.895996 },
{"activeModifiers":[],"character":"W", "isARepeat":false,"processTimeMs": 55258093.0,"type":"keyUp", "unixTimeMs": 1752292042432.327148 },
{"activeModifiers":[],"character":"E", "isARepeat":false,"processTimeMs": 55258187.0,"type":"keyUp", "unixTimeMs": 1752292042520.854004 },
{"activeModifiers":[],"character":"N", "isARepeat":false,"processTimeMs": 55258187.0,"type":"keyDown", "unixTimeMs": 1752292042529.917969 },
{"activeModifiers":[],"character":"N", "isARepeat":false,"processTimeMs": 55258281.0,"type":"keyUp", "unixTimeMs": 1752292042619.090332 },
{"activeModifiers":[],"character":"Space", "isARepeat":false,"processTimeMs": 55258312.0,"type":"keyDown", "unixTimeMs": 1752292042645.949707 },
{"activeModifiers":[],"character":"Space", "isARepeat":false,"processTimeMs": 55258390.0,"type":"keyUp", "unixTimeMs": 1752292042726.130859 },
{"activeModifiers":[],"character":"M", "isARepeat":false,"processTimeMs": 55258468.0,"type":"keyDown", "unixTimeMs": 1752292042801.841309 },
{"activeModifiers":[],"character":"M", "isARepeat":false,"processTimeMs": 55258546.0,"type":"keyUp", "unixTimeMs": 1752292042892.571045 },
{"activeModifiers":[],"character":"A", "isARepeat":false,"processTimeMs": 55258578.0,"type":"keyDown", "unixTimeMs": 1752292042912.488281 },
{"activeModifiers":[],"character":"Space", "isARepeat":false,"processTimeMs": 55258671.0,"type":"keyDown", "unixTimeMs": 1752292043019.100586 },
{"activeModifiers":[],"character":"A", "isARepeat":false,"processTimeMs": 55258687.0,"type":"keyUp", "unixTimeMs": 1752292043033.877930 },
{"activeModifiers":[],"character":"Space", "isARepeat":false,"processTimeMs": 55258781.0,"type":"keyUp", "unixTimeMs": 1752292043127.006104 },
{"activeModifiers":["shift"],"character":"?", "isARepeat":false,"processTimeMs": 55260000.0,"type":"keyDown", "unixTimeMs": 1752292044335.734375 },
{"activeModifiers":["shift"],"character":"?", "isARepeat":false,"processTimeMs": 55260046.0,"type":"keyUp", "unixTimeMs": 1752292044384.508057 },
{"activeModifiers":[],"character":"Enter", "isARepeat":false,"processTimeMs": 55260234.0,"type":"keyDown", "unixTimeMs": 1752292044575.049561 },
{"activeModifiers":[],"character":"Enter", "isARepeat":false,"processTimeMs": 55260296.0,"type":"keyUp", "unixTimeMs": 1752292044638.503906 },
]