<!DOCTYPE html>
<!-- saved from url=(0079)https://cmp-test.cyanmirror.com/console/entity/edit/ItemSpec-247249593509618740 -->
<html lang="en"><head><meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <!--<base href="/">--><base href=".">
    <link href="./商品规格_files/ant-design-blazor.css" rel="stylesheet">
    <link href="./商品规格_files/ant-design-pro-layout-blazor.css" rel="stylesheet">
    <link href="./商品规格_files/index.css" rel="stylesheet">
    <link href="./商品规格_files/index(1).css" rel="stylesheet">
    <link href="./商品规格_files/index(2).css" rel="stylesheet">
    <link rel="stylesheet" href="./商品规格_files/site.css">
    <link rel="stylesheet" href="./商品规格_files/CMP.Server.styles.css">
    <link rel="icon" type="image/png" href="https://cmp-test.cyanmirror.com/favicon.png">
    <!--!--><!--!--><!--!--><title>商品规格</title><!--!--><!--!--><style data-id="immersive-translate-input-injected-css">.immersive-translate-input {
  position: absolute;
  top: 0;
  right: 0;
  left: 0;
  bottom: 0;
  z-index: 2147483647;
  display: flex;
  justify-content: center;
  align-items: center;
}
.immersive-translate-attach-loading::after {
  content: " ";

  --loading-color: #f78fb6;
  width: 6px;
  height: 6px;
  border-radius: 50%;
  display: block;
  margin: 12px auto;
  position: relative;
  color: white;
  left: -100px;
  box-sizing: border-box;
  animation: immersiveTranslateShadowRolling 1.5s linear infinite;

  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-2000%, -50%);
  z-index: 100;
}

.immersive-translate-loading-spinner {
  vertical-align: middle !important;
  width: 10px !important;
  height: 10px !important;
  display: inline-block !important;
  margin: 0 4px !important;
  border: 2px rgba(221, 244, 255, 0.6) solid !important;
  border-top: 2px rgba(0, 0, 0, 0.375) solid !important;
  border-left: 2px rgba(0, 0, 0, 0.375) solid !important;
  border-radius: 50% !important;
  padding: 0 !important;
  -webkit-animation: immersive-translate-loading-animation 0.6s infinite linear !important;
  animation: immersive-translate-loading-animation 0.6s infinite linear !important;
}

@-webkit-keyframes immersive-translate-loading-animation {
  from {
    -webkit-transform: rotate(0deg);
  }

  to {
    -webkit-transform: rotate(359deg);
  }
}

@keyframes immersive-translate-loading-animation {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(359deg);
  }
}

.immersive-translate-input-loading {
  --loading-color: #f78fb6;
  width: 6px;
  height: 6px;
  border-radius: 50%;
  display: block;
  margin: 12px auto;
  position: relative;
  color: white;
  left: -100px;
  box-sizing: border-box;
  animation: immersiveTranslateShadowRolling 1.5s linear infinite;
}

@keyframes immersiveTranslateShadowRolling {
  0% {
    box-shadow: 0px 0 rgba(255, 255, 255, 0), 0px 0 rgba(255, 255, 255, 0),
      0px 0 rgba(255, 255, 255, 0), 0px 0 rgba(255, 255, 255, 0);
  }

  12% {
    box-shadow: 100px 0 var(--loading-color), 0px 0 rgba(255, 255, 255, 0),
      0px 0 rgba(255, 255, 255, 0), 0px 0 rgba(255, 255, 255, 0);
  }

  25% {
    box-shadow: 110px 0 var(--loading-color), 100px 0 var(--loading-color),
      0px 0 rgba(255, 255, 255, 0), 0px 0 rgba(255, 255, 255, 0);
  }

  36% {
    box-shadow: 120px 0 var(--loading-color), 110px 0 var(--loading-color),
      100px 0 var(--loading-color), 0px 0 rgba(255, 255, 255, 0);
  }

  50% {
    box-shadow: 130px 0 var(--loading-color), 120px 0 var(--loading-color),
      110px 0 var(--loading-color), 100px 0 var(--loading-color);
  }

  62% {
    box-shadow: 200px 0 rgba(255, 255, 255, 0), 130px 0 var(--loading-color),
      120px 0 var(--loading-color), 110px 0 var(--loading-color);
  }

  75% {
    box-shadow: 200px 0 rgba(255, 255, 255, 0), 200px 0 rgba(255, 255, 255, 0),
      130px 0 var(--loading-color), 120px 0 var(--loading-color);
  }

  87% {
    box-shadow: 200px 0 rgba(255, 255, 255, 0), 200px 0 rgba(255, 255, 255, 0),
      200px 0 rgba(255, 255, 255, 0), 130px 0 var(--loading-color);
  }

  100% {
    box-shadow: 200px 0 rgba(255, 255, 255, 0), 200px 0 rgba(255, 255, 255, 0),
      200px 0 rgba(255, 255, 255, 0), 200px 0 rgba(255, 255, 255, 0);
  }
}

.immersive-translate-toast {
  display: flex;
  position: fixed;
  z-index: 2147483647;
  left: 0;
  right: 0;
  top: 1%;
  width: fit-content;
  padding: 12px 20px;
  margin: auto;
  overflow: auto;
  background: #fef6f9;
  box-shadow: 0px 4px 10px 0px rgba(0, 10, 30, 0.06);
  font-size: 15px;
  border-radius: 8px;
  color: #333;
}

.immersive-translate-toast-content {
  display: flex;
  flex-direction: row;
  align-items: center;
}

.immersive-translate-toast-hidden {
  margin: 0 20px 0 72px;
  text-decoration: underline;
  cursor: pointer;
}

.immersive-translate-toast-close {
  color: #666666;
  font-size: 20px;
  font-weight: bold;
  padding: 0 10px;
  cursor: pointer;
}

@media screen and (max-width: 768px) {
  .immersive-translate-toast {
    top: 0;
    padding: 12px 0px 0 10px;
  }
  .immersive-translate-toast-content {
    flex-direction: column;
    text-align: center;
  }
  .immersive-translate-toast-hidden {
    margin: 10px auto;
  }
}

.immersive-translate-modal {
  display: none;
  position: fixed;
  z-index: 2147483647;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  overflow: auto;
  background-color: rgb(0, 0, 0);
  background-color: rgba(0, 0, 0, 0.4);
  font-size: 15px;
}

.immersive-translate-modal-content {
  background-color: #fefefe;
  margin: 10% auto;
  padding: 40px 24px 24px;
  border: 1px solid #888;
  border-radius: 10px;
  width: 80%;
  max-width: 270px;
  font-family: system-ui, -apple-system, "Segoe UI", "Roboto", "Ubuntu",
    "Cantarell", "Noto Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji",
    "Segoe UI Symbol", "Noto Color Emoji";
  position: relative;
}

@media screen and (max-width: 768px) {
  .immersive-translate-modal-content {
    margin: 50% auto !important;
  }
}

.immersive-translate-modal .immersive-translate-modal-content-in-input {
  max-width: 500px;
}
.immersive-translate-modal-content-in-input .immersive-translate-modal-body {
  text-align: left;
  max-height: unset;
}

.immersive-translate-modal-title {
  text-align: center;
  font-size: 16px;
  font-weight: 700;
  color: #333333;
}

.immersive-translate-modal-body {
  text-align: center;
  font-size: 14px;
  font-weight: 400;
  color: #333333;
  word-break: break-all;
  margin-top: 24px;
}

@media screen and (max-width: 768px) {
  .immersive-translate-modal-body {
    max-height: 250px;
    overflow-y: auto;
  }
}

.immersive-translate-close {
  color: #666666;
  position: absolute;
  right: 16px;
  top: 16px;
  font-size: 20px;
  font-weight: bold;
}

.immersive-translate-close:hover,
.immersive-translate-close:focus {
  color: black;
  text-decoration: none;
  cursor: pointer;
}

.immersive-translate-modal-footer {
  display: flex;
  justify-content: center;
  flex-wrap: wrap;
  margin-top: 24px;
}

.immersive-translate-btn {
  width: fit-content;
  color: #fff;
  background-color: #ea4c89;
  border: none;
  font-size: 16px;
  margin: 0 8px;
  padding: 9px 30px;
  border-radius: 5px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.immersive-translate-btn:hover {
  background-color: #f082ac;
}
.immersive-translate-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}
.immersive-translate-btn:disabled:hover {
  background-color: #ea4c89;
}

.immersive-translate-cancel-btn {
  /* gray color */
  background-color: rgb(89, 107, 120);
}

.immersive-translate-cancel-btn:hover {
  background-color: hsl(205, 20%, 32%);
}

.immersive-translate-action-btn {
  background-color: transparent;
  color: #ea4c89;
  border: 1px solid #ea4c89;
}

.immersive-translate-btn svg {
  margin-right: 5px;
}

.immersive-translate-link {
  cursor: pointer;
  user-select: none;
  -webkit-user-drag: none;
  text-decoration: none;
  color: #007bff;
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0.1);
}

.immersive-translate-primary-link {
  cursor: pointer;
  user-select: none;
  -webkit-user-drag: none;
  text-decoration: none;
  color: #ea4c89;
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0.1);
}

.immersive-translate-modal input[type="radio"] {
  margin: 0 6px;
  cursor: pointer;
}

.immersive-translate-modal label {
  cursor: pointer;
}

.immersive-translate-close-action {
  position: absolute;
  top: 2px;
  right: 0px;
  cursor: pointer;
}

.imt-image-status {
  background-color: rgba(0, 0, 0, 0.5) !important;
  display: flex !important;
  flex-direction: column !important;
  align-items: center !important;
  justify-content: center !important;
  border-radius: 16px !important;
}
.imt-image-status img,
.imt-image-status svg,
.imt-img-loading {
  width: 28px !important;
  height: 28px !important;
  margin: 0 0 8px 0 !important;
  min-height: 28px !important;
  min-width: 28px !important;
}
.imt-img-loading {
  background-image: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADgAAAA4CAMAAACfWMssAAAAtFBMVEUAAAD////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////oK74hAAAAPHRSTlMABBMIDyQXHwyBfFdDMSw+OjXCb+5RG51IvV/k0rOqlGRM6KKMhdvNyZBz9MaupmxpWyj437iYd/yJVNZeuUC7AAACt0lEQVRIx53T2XKiUBCA4QYOiyCbiAsuuGBcYtxiYtT3f6/pbqoYHVFO5r+iivpo6DpAWYpqeoFfr9f90DsYAuRSWkFnPO50OgR9PwiCUFcl2GEcx+N/YBh6pvKaefHlUgZd1zVe0NbYcQjGBfzrPE8Xz8aF+71D8gG6DHFPpc4a7xFiCDuhaWgKgGIJQ3d5IMGDrpS4S5KgpIm+en9f6PlAhKby4JwEIxlYJV9h5k5nee9GoxHJ2IDSNB0dwdad1NAxDJ/uXDHYmebdk4PdbkS58CIVHdYSUHTYYRWOJblWSyu2lmy3KNFVJNBhxcuGW4YBVCbYGRZwIooipHsNqjM4FbgOQqQqSKQQU9V8xmi1QlgHqQQ6DDBvRUVCDirs+EzGDGOQTCATgtYTnbCVLgsVgRE0T1QE0qHCFAht2z6dLvJQs3Lo2FQoDxWNUiBhaP4eRgwNkI+dAjVOA/kUrIDwf3CG8NfNOE0eiFotSuo+rBiq8tD9oY4Qzc6YJw99hl1wzpQvD7ef2M8QgnOGJfJw+EltQc+oX2yn907QB22WZcvlUpd143dqQu+8pCJZuGE4xCuPXJqqcs5sNpsI93Rmzym1k4Npk+oD1SH3/a3LOK/JpUBpWfqNySxWzCfNCUITuDG5dtuphrUJ1myeIE9bIsPiKrfqTai5WZxbhtNphYx6GEIHihyGFTI69lje/rxajdh0s0msZ0zYxyPLhYCb1CyHm9Qsd2H37Y3lugVwL9kNh8Ot8cha6fUNQ8nuXi5z9/ExsAO4zQrb/ev1yrCB7lGyQzgYDGuxq1toDN/JGvN+HyWNHKB7zEoK+PX11e12G431erGYzwmytAWU56fkMHY5JJnDRR2eZji3AwtIcrEV8Cojat/BdQ7XOwGV1e1hDjGGjXbdArm8uJZtCH5MbcctVX8A1WpqumJHwckAAAAASUVORK5CYII=");
  background-size: 28px 28px;
  animation: image-loading-rotate 1s linear infinite !important;
}

.imt-image-status span {
  color: var(--bg-2, #fff) !important;
  font-size: 14px !important;
  line-height: 14px !important;
  font-weight: 500 !important;
  font-family: "PingFang SC", Arial, sans-serif !important;
}

@keyframes image-loading-rotate {
  from {
    transform: rotate(360deg);
  }
  to {
    transform: rotate(0deg);
  }
}
</style></head>

<body><!--!--><!--!--><!--!--><!--!--><!--!--><!--!--><!--!--><!--!--><!--!--><div class="ant-design-pro ant-pro-basicLayout screen-lg ant-pro-basicLayout-fix-siderbar" style=""><!--!-->
    <!--!--><!--!--><!--!-->
    <section class="ant-layout ant-layout-has-sider" style="min-height: 100%;" id="ant-blazor-7090dea4-ef82-4c01-85a6-5cfe8dce6e55"><!--!-->
        <!--!-->
                <!--!--><!--!-->
                    <!--!--><!--!-->
                        <!--!--><!--!--><aside class="ant-pro-sider ant-pro-sider-fixed ant-pro-sider-layout-mix ant-pro-sider-light ant-layout-sider ant-layout-sider-light" style="flex: 0 0 208px;max-width: 208px;min-width: 208px;width: 208px; overflow: hidden; padding-top: 48px;"><!--!-->
    <!--!--><!--!-->
        <div class="ant-layout-sider-children"><!--!-->
            <!--!-->
    
<!--!-->
    
<!--!-->
    
    <div style="flex: 1; overflow-y: auto; overflow-x: hidden;"><!--!-->
        <!--!--><!--!--><!--!--><!--!-->
    <ul class="ant-pro-sider-menu ant-menu ant-menu-root ant-menu-light ant-menu-inline" style="width: &#39;100%&#39;;" id="ant-blazor-267091f7-2c79-4d61-82e8-acacfa7c6972" direction="ltr" role="menu" _bl_f9f7a90c-d7b3-4bc5-b3c4-2502ba349a1f=""><!--!-->
        <!--!-->
    <!--!--><!--!-->
    <li class="ant-menu-submenu ant-menu-submenu-inline" role="menuitem" style="position:relative;" _bl_c5a35fff-5b24-4c36-b4db-6809f28f112e=""><!--!-->
        <div class="ant-menu-submenu-title" style="padding-left:24px;" role="button" aria-haspopup="true"><!--!-->
            <span class="ant-menu-title-content"><!--!-->
<span><!--!-->
            <span>组织管理</span><!--!-->
            </span><!--!-->
            </span><!--!-->
            <i class="ant-menu-submenu-arrow"></i><!--!-->
        </div><!--!-->
        <ul direction="ltr" class="ant-menu ant-menu-sub ant-menu-light ant-menu-inline ant-menu-hidden" role="menu" _bl_399d3eb6-d258-4a59-8b3e-1bc1cdb633b1=""><!--!-->
            <!--!--><!--!-->
                <!--!-->
                            <!--!--><!--!--><!--!-->
<li class="ant-menu-item" role="menuitem" style="padding-left:48px; " _bl_cfd589e8-26c1-480b-82f5-c05116cb180f=""><!--!-->
            <!--!-->
            <span class="ant-menu-title-content"><!--!-->
                    <!--!--><a href="https://cmp-test.cyanmirror.com/console/entity/Organization" class=""><!--!-->
                            <span><!--!-->
            <span>组织</span><!--!-->
            </span><!--!-->
<!--!-->
                        </a><!--!-->
            </span><!--!-->
        </li><!--!--><!--!--><!--!-->
<li class="ant-menu-item" role="menuitem" style="padding-left:48px; " _bl_250a386e-fdde-4b22-ab54-1e0a3a8e232e=""><!--!-->
            <!--!-->
            <span class="ant-menu-title-content"><!--!-->
                    <!--!--><a href="https://cmp-test.cyanmirror.com/console/entity/OrganizationMember" class=""><!--!-->
                            <span><!--!-->
            <span>组织成员</span><!--!-->
            </span><!--!-->
<!--!-->
                        </a><!--!-->
            </span><!--!-->
        </li><!--!--><!--!--><!--!-->
<li class="ant-menu-item" role="menuitem" style="padding-left:48px; " _bl_9992c4bf-6219-4bb1-9e9e-36a1d3d01977=""><!--!-->
            <!--!-->
            <span class="ant-menu-title-content"><!--!-->
                    <!--!--><a href="https://cmp-test.cyanmirror.com/console/entity/OrganizationRole" class=""><!--!-->
                            <span><!--!-->
            <span>组织角色</span><!--!-->
            </span><!--!-->
<!--!-->
                        </a><!--!-->
            </span><!--!-->
        </li><!--!-->
                        <!--!-->
            <!--!-->
        </ul><!--!-->
    </li><!--!-->
<!--!--><!--!-->
    <li class="ant-menu-submenu ant-menu-submenu-inline" role="menuitem" style="position:relative;" _bl_091cc3fd-86be-478c-9df9-88650fc29295=""><!--!-->
        <div class="ant-menu-submenu-title" style="padding-left:24px;" role="button" aria-haspopup="true"><!--!-->
            <span class="ant-menu-title-content"><!--!-->
<span><!--!-->
            <span>销售管理</span><!--!-->
            </span><!--!-->
            </span><!--!-->
            <i class="ant-menu-submenu-arrow"></i><!--!-->
        </div><!--!-->
        <ul direction="ltr" class="ant-menu ant-menu-sub ant-menu-light ant-menu-inline ant-menu-hidden" role="menu" _bl_0130295e-7291-4c44-86b1-515553b0d51d=""><!--!-->
            <!--!--><!--!-->
                <!--!-->
                            <!--!--><!--!--><!--!-->
<li class="ant-menu-item" role="menuitem" style="padding-left:48px; " _bl_0d05ccfa-7272-41fa-8cb5-39d0cef8afc3=""><!--!-->
            <!--!-->
            <span class="ant-menu-title-content"><!--!-->
                    <!--!--><a href="https://cmp-test.cyanmirror.com/console/list/SalesDistributorItemSpec" class=""><!--!-->
                            <span><!--!-->
            <span>服务商商品规格</span><!--!-->
            </span><!--!-->
<!--!-->
                        </a><!--!-->
            </span><!--!-->
        </li><!--!--><!--!--><!--!-->
<li class="ant-menu-item" role="menuitem" style="padding-left:48px; " _bl_338f67ba-a398-47c8-962c-ded1f8a67e1e=""><!--!-->
            <!--!-->
            <span class="ant-menu-title-content"><!--!-->
                    <!--!--><a href="https://cmp-test.cyanmirror.com/console/entity/SalesSettlementRecord" class=""><!--!-->
                            <span><!--!-->
            <span>结算单</span><!--!-->
            </span><!--!-->
<!--!-->
                        </a><!--!-->
            </span><!--!-->
        </li><!--!--><!--!--><!--!-->
<li class="ant-menu-item" role="menuitem" style="padding-left:48px; " _bl_31eabd9e-ecfa-42ee-b63f-55f0a23ecd52=""><!--!-->
            <!--!-->
            <span class="ant-menu-title-content"><!--!-->
                    <!--!--><a href="https://cmp-test.cyanmirror.com/console/entity/SalesSettlementItem" class=""><!--!-->
                            <span><!--!-->
            <span>结算项目</span><!--!-->
            </span><!--!-->
<!--!-->
                        </a><!--!-->
            </span><!--!-->
        </li><!--!-->
                        <!--!-->
            <!--!-->
        </ul><!--!-->
    </li><!--!-->
<!--!--><!--!-->
    <li class="ant-menu-submenu ant-menu-submenu-inline" role="menuitem" style="position:relative;" _bl_caedfcb5-2ab4-4d41-983e-e9c1ec87d809=""><!--!-->
        <div class="ant-menu-submenu-title" style="padding-left:24px;" role="button" aria-haspopup="true"><!--!-->
            <span class="ant-menu-title-content"><!--!-->
<span><!--!-->
            <span>设备管理</span><!--!-->
            </span><!--!-->
            </span><!--!-->
            <i class="ant-menu-submenu-arrow"></i><!--!-->
        </div><!--!-->
        <ul direction="ltr" class="ant-menu ant-menu-sub ant-menu-light ant-menu-inline ant-menu-hidden" role="menu" _bl_8335a6a0-22ef-46cb-8c6d-9ce40e89ac1f=""><!--!-->
            <!--!--><!--!-->
                <!--!-->
                            <!--!--><!--!--><!--!-->
<li class="ant-menu-item" role="menuitem" style="padding-left:48px; " _bl_5dc0df8d-50dd-4dd0-81d7-1d4bfe522cf4=""><!--!-->
            <!--!-->
            <span class="ant-menu-title-content"><!--!-->
                    <!--!--><a href="https://cmp-test.cyanmirror.com/console/entity/Device" class=""><!--!-->
                            <span><!--!-->
            <span>设备</span><!--!-->
            </span><!--!-->
<!--!-->
                        </a><!--!-->
            </span><!--!-->
        </li><!--!--><!--!--><!--!-->
<li class="ant-menu-item" role="menuitem" style="padding-left:48px; " _bl_a0ac7f97-b6c1-40c1-a11b-44e88e14e500=""><!--!-->
            <!--!-->
            <span class="ant-menu-title-content"><!--!-->
                    <!--!--><a href="https://cmp-test.cyanmirror.com/console/entity/DeviceBatch" class=""><!--!-->
                            <span><!--!-->
            <span>设备批次</span><!--!-->
            </span><!--!-->
<!--!-->
                        </a><!--!-->
            </span><!--!-->
        </li><!--!--><!--!--><!--!-->
<li class="ant-menu-item" role="menuitem" style="padding-left:48px; " _bl_2de6f59b-e6c9-4a13-816f-626305802ad3=""><!--!-->
            <!--!-->
            <span class="ant-menu-title-content"><!--!-->
                    <!--!--><a href="https://cmp-test.cyanmirror.com/console/entity/DeviceModel" class=""><!--!-->
                            <span><!--!-->
            <span>设备型号</span><!--!-->
            </span><!--!-->
<!--!-->
                        </a><!--!-->
            </span><!--!-->
        </li><!--!--><!--!--><!--!-->
<li class="ant-menu-item" role="menuitem" style="padding-left:48px; " _bl_91009932-70c2-4500-99b1-d72e2ab6dc02=""><!--!-->
            <!--!-->
            <span class="ant-menu-title-content"><!--!-->
                    <!--!--><a href="https://cmp-test.cyanmirror.com/console/entity/DeviceProduct" class=""><!--!-->
                            <span><!--!-->
            <span>设备产品</span><!--!-->
            </span><!--!-->
<!--!-->
                        </a><!--!-->
            </span><!--!-->
        </li><!--!--><!--!--><!--!-->
<li class="ant-menu-item" role="menuitem" style="padding-left:48px; " _bl_69b34a44-1b12-43cd-9521-872a88140eaf=""><!--!-->
            <!--!-->
            <span class="ant-menu-title-content"><!--!-->
                    <!--!--><a href="https://cmp-test.cyanmirror.com/console/entity/DeviceProductType" class=""><!--!-->
                            <span><!--!-->
            <span>设备产品类型</span><!--!-->
            </span><!--!-->
<!--!-->
                        </a><!--!-->
            </span><!--!-->
        </li><!--!--><!--!--><!--!-->
<li class="ant-menu-item" role="menuitem" style="padding-left:48px; " _bl_d8fdff19-3256-4cf0-a9f0-65eca2d28252=""><!--!-->
            <!--!-->
            <span class="ant-menu-title-content"><!--!-->
                    <!--!--><a href="https://cmp-test.cyanmirror.com/console/entity/DeviceManufacturer" class=""><!--!-->
                            <span><!--!-->
            <span>设备制造商</span><!--!-->
            </span><!--!-->
<!--!-->
                        </a><!--!-->
            </span><!--!-->
        </li><!--!--><!--!--><!--!-->
<li class="ant-menu-item" role="menuitem" style="padding-left:48px; " _bl_fa803be4-f77d-46d5-8874-311eb38bac8c=""><!--!-->
            <!--!-->
            <span class="ant-menu-title-content"><!--!-->
                    <!--!--><a href="https://cmp-test.cyanmirror.com/console/entity/DeviceOperationRecord" class=""><!--!-->
                            <span><!--!-->
            <span>设备操作记录</span><!--!-->
            </span><!--!-->
<!--!-->
                        </a><!--!-->
            </span><!--!-->
        </li><!--!--><!--!--><!--!-->
<li class="ant-menu-item" role="menuitem" style="padding-left:48px; " _bl_3a8d4765-730e-42d6-ac64-fd234e8de002=""><!--!-->
            <!--!-->
            <span class="ant-menu-title-content"><!--!-->
                    <!--!--><a href="https://cmp-test.cyanmirror.com/console/entity/DeviceOperation" class=""><!--!-->
                            <span><!--!-->
            <span>设备操作</span><!--!-->
            </span><!--!-->
<!--!-->
                        </a><!--!-->
            </span><!--!-->
        </li><!--!--><!--!--><!--!-->
<li class="ant-menu-item" role="menuitem" style="padding-left:48px; " _bl_6be9d753-6f10-434d-96de-23131dfc4347=""><!--!-->
            <!--!-->
            <span class="ant-menu-title-content"><!--!-->
                    <!--!--><a href="https://cmp-test.cyanmirror.com/console/entity/DeviceEvent" class=""><!--!-->
                            <span><!--!-->
            <span>设备事件</span><!--!-->
            </span><!--!-->
<!--!-->
                        </a><!--!-->
            </span><!--!-->
        </li><!--!--><!--!--><!--!-->
<li class="ant-menu-item" role="menuitem" style="padding-left:48px; " _bl_5aabace9-3b9c-4f9c-bdc3-f958de194ca5=""><!--!-->
            <!--!-->
            <span class="ant-menu-title-content"><!--!-->
                    <!--!--><a href="https://cmp-test.cyanmirror.com/console/entity/DeviceEventType" class=""><!--!-->
                            <span><!--!-->
            <span>设备事件类型</span><!--!-->
            </span><!--!-->
<!--!-->
                        </a><!--!-->
            </span><!--!-->
        </li><!--!--><!--!--><!--!-->
<li class="ant-menu-item" role="menuitem" style="padding-left:48px; " _bl_a74c63b1-fbf7-495c-ab69-7d47f4022098=""><!--!-->
            <!--!-->
            <span class="ant-menu-title-content"><!--!-->
                    <!--!--><a href="https://cmp-test.cyanmirror.com/console/entity/DeviceEventSource" class=""><!--!-->
                            <span><!--!-->
            <span>设备事件来源</span><!--!-->
            </span><!--!-->
<!--!-->
                        </a><!--!-->
            </span><!--!-->
        </li><!--!--><!--!--><!--!-->
<li class="ant-menu-item" role="menuitem" style="padding-left:48px; " _bl_e202e928-6f61-452a-9ef3-0bbd79ff0b8f=""><!--!-->
            <!--!-->
            <span class="ant-menu-title-content"><!--!-->
                    <!--!--><a href="https://cmp-test.cyanmirror.com/console/entity/DeviceProperty" class=""><!--!-->
                            <span><!--!-->
            <span>设备属性</span><!--!-->
            </span><!--!-->
<!--!-->
                        </a><!--!-->
            </span><!--!-->
        </li><!--!--><!--!--><!--!-->
<li class="ant-menu-item" role="menuitem" style="padding-left:48px; " _bl_4d394c5a-a17a-4b2c-9a2f-0de4885a2ddc=""><!--!-->
            <!--!-->
            <span class="ant-menu-title-content"><!--!-->
                    <!--!--><a href="https://cmp-test.cyanmirror.com/console/entity/DevicePropertyValue" class=""><!--!-->
                            <span><!--!-->
            <span>设备属性值</span><!--!-->
            </span><!--!-->
<!--!-->
                        </a><!--!-->
            </span><!--!-->
        </li><!--!-->
                        <!--!-->
            <!--!-->
        </ul><!--!-->
    </li><!--!-->
<!--!--><!--!-->
    <li class="ant-menu-submenu ant-menu-submenu-inline" role="menuitem" style="position:relative;" _bl_2a8c2c1e-e881-4c50-8248-0b5df0e6f2d1=""><!--!-->
        <div class="ant-menu-submenu-title" style="padding-left:24px;" role="button" aria-haspopup="true"><!--!-->
            <span class="ant-menu-title-content"><!--!-->
<span><!--!-->
            <span>订单管理</span><!--!-->
            </span><!--!-->
            </span><!--!-->
            <i class="ant-menu-submenu-arrow"></i><!--!-->
        </div><!--!-->
        <ul direction="ltr" class="ant-menu ant-menu-sub ant-menu-light ant-menu-inline ant-menu-hidden" role="menu" _bl_314bf3a5-cc1a-4da1-9ff9-e88be14fdcf1=""><!--!-->
            <!--!--><!--!-->
                <!--!-->
                            <!--!--><!--!--><!--!-->
<li class="ant-menu-item" role="menuitem" style="padding-left:48px; " _bl_a52d9336-105b-4bae-85aa-a8f6b85d36fd=""><!--!-->
            <!--!-->
            <span class="ant-menu-title-content"><!--!-->
                    <!--!--><a href="https://cmp-test.cyanmirror.com/console/entity/SalesOrder" class=""><!--!-->
                            <span><!--!-->
            <span>订单</span><!--!-->
            </span><!--!-->
<!--!-->
                        </a><!--!-->
            </span><!--!-->
        </li><!--!--><!--!--><!--!-->
<li class="ant-menu-item" role="menuitem" style="padding-left:48px; " _bl_4e736421-4bf1-4a93-9b9e-05b42de4213e=""><!--!-->
            <!--!-->
            <span class="ant-menu-title-content"><!--!-->
                    <!--!--><a href="https://cmp-test.cyanmirror.com/console/entity/SalesOrderItem" class=""><!--!-->
                            <span><!--!-->
            <span>订单明细</span><!--!-->
            </span><!--!-->
<!--!-->
                        </a><!--!-->
            </span><!--!-->
        </li><!--!--><!--!--><!--!-->
<li class="ant-menu-item" role="menuitem" style="padding-left:48px; " _bl_1f6cc02f-0c44-48c5-a292-ba6cf517cafe=""><!--!-->
            <!--!-->
            <span class="ant-menu-title-content"><!--!-->
                    <!--!--><a href="https://cmp-test.cyanmirror.com/console/list/SalesPurchaseOrder" class=""><!--!-->
                            <span><!--!-->
            <span>采购订单</span><!--!-->
            </span><!--!-->
<!--!-->
                        </a><!--!-->
            </span><!--!-->
        </li><!--!--><!--!--><!--!-->
<li class="ant-menu-item" role="menuitem" style="padding-left:48px; " _bl_6003b2b0-bec7-4370-aa70-e6d3e2159487=""><!--!-->
            <!--!-->
            <span class="ant-menu-title-content"><!--!-->
                    <!--!--><a href="https://cmp-test.cyanmirror.com/console/list/SalesDistributorOrder" class=""><!--!-->
                            <span><!--!-->
            <span>订单管理</span><!--!-->
            </span><!--!-->
<!--!-->
                        </a><!--!-->
            </span><!--!-->
        </li><!--!--><!--!--><!--!-->
<li class="ant-menu-item" role="menuitem" style="padding-left:48px; " _bl_85871325-bb3c-4179-aa26-4f80638b85b6=""><!--!-->
            <!--!-->
            <span class="ant-menu-title-content"><!--!-->
                    <!--!--><a href="https://cmp-test.cyanmirror.com/console/entity/SalesManufacturerOrder" class=""><!--!-->
                            <span><!--!-->
            <span>销售订单</span><!--!-->
            </span><!--!-->
<!--!-->
                        </a><!--!-->
            </span><!--!-->
        </li><!--!-->
                        <!--!-->
            <!--!-->
        </ul><!--!-->
    </li><!--!-->
<!--!--><!--!-->
    <li class="ant-menu-submenu ant-menu-submenu-inline" role="menuitem" style="position:relative;" _bl_08b3f0e4-c03b-4581-bc2b-88b3f308ea21=""><!--!-->
        <div class="ant-menu-submenu-title" style="padding-left:24px;" role="button" aria-haspopup="true"><!--!-->
            <span class="ant-menu-title-content"><!--!-->
<span><!--!-->
            <span>认证标识</span><!--!-->
            </span><!--!-->
            </span><!--!-->
            <i class="ant-menu-submenu-arrow"></i><!--!-->
        </div><!--!-->
        <ul direction="ltr" class="ant-menu ant-menu-sub ant-menu-light ant-menu-inline ant-menu-hidden" role="menu" _bl_2bf80c22-26a0-4c96-85be-f016c9cc5f3b=""><!--!-->
            <!--!--><!--!-->
                <!--!-->
                            <!--!--><!--!--><!--!-->
<li class="ant-menu-item" role="menuitem" style="padding-left:48px; " _bl_fd12f565-92a9-436b-a992-15f9d79993e8=""><!--!-->
            <!--!-->
            <span class="ant-menu-title-content"><!--!-->
                    <!--!--><a href="https://cmp-test.cyanmirror.com/console/entity/Identity" class=""><!--!-->
                            <span><!--!-->
            <span>认证标识</span><!--!-->
            </span><!--!-->
<!--!-->
                        </a><!--!-->
            </span><!--!-->
        </li><!--!--><!--!--><!--!-->
<li class="ant-menu-item" role="menuitem" style="padding-left:48px; " _bl_22c22849-24aa-440e-88ce-475578c44cb6=""><!--!-->
            <!--!-->
            <span class="ant-menu-title-content"><!--!-->
                    <!--!--><a href="https://cmp-test.cyanmirror.com/console/entity/IdentityRecord" class=""><!--!-->
                            <span><!--!-->
            <span>认证记录</span><!--!-->
            </span><!--!-->
<!--!-->
                        </a><!--!-->
            </span><!--!-->
        </li><!--!--><!--!--><!--!-->
<li class="ant-menu-item" role="menuitem" style="padding-left:48px; " _bl_543d9220-9049-4fa3-8616-c0b511df39b8=""><!--!-->
            <!--!-->
            <span class="ant-menu-title-content"><!--!-->
                    <!--!--><a href="https://cmp-test.cyanmirror.com/console/entity/IdentityRole" class=""><!--!-->
                            <span><!--!-->
            <span>角色</span><!--!-->
            </span><!--!-->
<!--!-->
                        </a><!--!-->
            </span><!--!-->
        </li><!--!--><!--!--><!--!-->
<li class="ant-menu-item" role="menuitem" style="padding-left:48px; " _bl_8f2b8f41-b7f0-4190-9b0a-619c2a9443e7=""><!--!-->
            <!--!-->
            <span class="ant-menu-title-content"><!--!-->
                    <!--!--><a href="https://cmp-test.cyanmirror.com/console/entity/IdentityClaimType" class=""><!--!-->
                            <span><!--!-->
            <span>扩展项类型</span><!--!-->
            </span><!--!-->
<!--!-->
                        </a><!--!-->
            </span><!--!-->
        </li><!--!-->
                        <!--!-->
            <!--!-->
        </ul><!--!-->
    </li><!--!-->
<!--!--><!--!-->
    <li class="ant-menu-submenu ant-menu-submenu-inline" role="menuitem" style="position:relative;" _bl_7a65f7f9-6c5d-4bef-919c-fcf403be4d0a=""><!--!-->
        <div class="ant-menu-submenu-title" style="padding-left:24px;" role="button" aria-haspopup="true"><!--!-->
            <span class="ant-menu-title-content"><!--!-->
<span><!--!-->
            <span>短链接</span><!--!-->
            </span><!--!-->
            </span><!--!-->
            <i class="ant-menu-submenu-arrow"></i><!--!-->
        </div><!--!-->
        <ul direction="ltr" class="ant-menu ant-menu-sub ant-menu-light ant-menu-inline ant-menu-hidden" role="menu" _bl_8865c9cc-985d-44c4-a60b-2aab45fef473=""><!--!-->
            <!--!--><!--!-->
                <!--!-->
                            <!--!--><!--!--><!--!-->
<li class="ant-menu-item" role="menuitem" style="padding-left:48px; " _bl_d97fd312-2db1-4e7c-879e-499ecceb88b5=""><!--!-->
            <!--!-->
            <span class="ant-menu-title-content"><!--!-->
                    <!--!--><a href="https://cmp-test.cyanmirror.com/console/entity/ShortLink" class=""><!--!-->
                            <span><!--!-->
            <span>短链接</span><!--!-->
            </span><!--!-->
<!--!-->
                        </a><!--!-->
            </span><!--!-->
        </li><!--!-->
                        <!--!-->
            <!--!-->
        </ul><!--!-->
    </li><!--!-->
<!--!--><!--!-->
    <li class="ant-menu-submenu ant-menu-submenu-inline" role="menuitem" style="position:relative;" _bl_94bc19d8-c220-4b67-b6f0-e1025dc52fbb=""><!--!-->
        <div class="ant-menu-submenu-title" style="padding-left:24px;" role="button" aria-haspopup="true"><!--!-->
            <span class="ant-menu-title-content"><!--!-->
<span><!--!-->
            <span>知识库</span><!--!-->
            </span><!--!-->
            </span><!--!-->
            <i class="ant-menu-submenu-arrow"></i><!--!-->
        </div><!--!-->
        <ul direction="ltr" class="ant-menu ant-menu-sub ant-menu-light ant-menu-inline ant-menu-hidden" role="menu" _bl_48e336d9-d1ca-4bed-a974-c93482785f83=""><!--!-->
            <!--!--><!--!-->
                <!--!-->
                            <!--!--><!--!--><!--!-->
<li class="ant-menu-item" role="menuitem" style="padding-left:48px; " _bl_9b040848-8587-421a-9b2a-cd8f0d8f1c64=""><!--!-->
            <!--!-->
            <span class="ant-menu-title-content"><!--!-->
                    <!--!--><a href="https://cmp-test.cyanmirror.com/console/entity/Document" class=""><!--!-->
                            <span><!--!-->
            <span>知识库</span><!--!-->
            </span><!--!-->
<!--!-->
                        </a><!--!-->
            </span><!--!-->
        </li><!--!--><!--!--><!--!-->
<li class="ant-menu-item" role="menuitem" style="padding-left:48px; " _bl_bbe40391-ebfc-43e7-ba6f-728595d007e4=""><!--!-->
            <!--!-->
            <span class="ant-menu-title-content"><!--!-->
                    <!--!--><a href="https://cmp-test.cyanmirror.com/console/entity/DocumentType" class=""><!--!-->
                            <span><!--!-->
            <span>文档类型</span><!--!-->
            </span><!--!-->
<!--!-->
                        </a><!--!-->
            </span><!--!-->
        </li><!--!--><!--!--><!--!-->
<li class="ant-menu-item" role="menuitem" style="padding-left:48px; " _bl_5575ea29-68ae-4cf1-9fe7-c3b6c16841c0=""><!--!-->
            <!--!-->
            <span class="ant-menu-title-content"><!--!-->
                    <!--!--><a href="https://cmp-test.cyanmirror.com/console/entity/DocumentCategory" class=""><!--!-->
                            <span><!--!-->
            <span>文档分类</span><!--!-->
            </span><!--!-->
<!--!-->
                        </a><!--!-->
            </span><!--!-->
        </li><!--!-->
                        <!--!-->
            <!--!-->
        </ul><!--!-->
    </li><!--!-->
<!--!--><!--!-->
    <li class="ant-menu-submenu ant-menu-submenu-inline" role="menuitem" style="position:relative;" _bl_3ff4f611-b461-401f-8bf8-3a89ef8aef95=""><!--!-->
        <div class="ant-menu-submenu-title" style="padding-left:24px;" role="button" aria-haspopup="true"><!--!-->
            <span class="ant-menu-title-content"><!--!-->
<span><!--!-->
            <span>通知</span><!--!-->
            </span><!--!-->
            </span><!--!-->
            <i class="ant-menu-submenu-arrow"></i><!--!-->
        </div><!--!-->
        <ul direction="ltr" class="ant-menu ant-menu-sub ant-menu-light ant-menu-inline ant-menu-hidden" role="menu" _bl_8f07b20a-d0e1-4bcc-8a23-ceaa54cd5983=""><!--!-->
            <!--!--><!--!-->
                <!--!-->
                            <!--!--><!--!--><!--!-->
<li class="ant-menu-item" role="menuitem" style="padding-left:48px; " _bl_711ee4d2-51b3-428c-9fff-5247f82390de=""><!--!-->
            <!--!-->
            <span class="ant-menu-title-content"><!--!-->
                    <!--!--><a href="https://cmp-test.cyanmirror.com/console/entity/Notification" class=""><!--!-->
                            <span><!--!-->
            <span>通知</span><!--!-->
            </span><!--!-->
<!--!-->
                        </a><!--!-->
            </span><!--!-->
        </li><!--!--><!--!--><!--!-->
<li class="ant-menu-item" role="menuitem" style="padding-left:48px; " _bl_24a3b475-03c3-4cad-914b-f682c705b90d=""><!--!-->
            <!--!-->
            <span class="ant-menu-title-content"><!--!-->
                    <!--!--><a href="https://cmp-test.cyanmirror.com/console/entity/NotificationSendRecord" class=""><!--!-->
                            <span><!--!-->
            <span>通知发送记录</span><!--!-->
            </span><!--!-->
<!--!-->
                        </a><!--!-->
            </span><!--!-->
        </li><!--!--><!--!--><!--!-->
<li class="ant-menu-item" role="menuitem" style="padding-left:48px; " _bl_d70d8f1b-ec3f-4646-ac64-87f218ed672e=""><!--!-->
            <!--!-->
            <span class="ant-menu-title-content"><!--!-->
                    <!--!--><a href="https://cmp-test.cyanmirror.com/console/entity/NotificationSendPolicy" class=""><!--!-->
                            <span><!--!-->
            <span>通知发送策略</span><!--!-->
            </span><!--!-->
<!--!-->
                        </a><!--!-->
            </span><!--!-->
        </li><!--!-->
                        <!--!-->
            <!--!-->
        </ul><!--!-->
    </li><!--!-->
<!--!--><!--!-->
    <li class="ant-menu-submenu ant-menu-submenu-inline" role="menuitem" style="position:relative;" _bl_3cd82a54-d50c-4c4d-b938-971b446f6530=""><!--!-->
        <div class="ant-menu-submenu-title" style="padding-left:24px;" role="button" aria-haspopup="true"><!--!-->
            <span class="ant-menu-title-content"><!--!-->
<span><!--!-->
            <span>系统提醒</span><!--!-->
            </span><!--!-->
            </span><!--!-->
            <i class="ant-menu-submenu-arrow"></i><!--!-->
        </div><!--!-->
        <ul direction="ltr" class="ant-menu ant-menu-sub ant-menu-light ant-menu-inline ant-menu-hidden" role="menu" _bl_68d39fdc-1c90-4408-9637-d3ea5845b6e7=""><!--!-->
            <!--!--><!--!-->
                <!--!-->
                            <!--!--><!--!--><!--!-->
<li class="ant-menu-item" role="menuitem" style="padding-left:48px; " _bl_3f82de37-1d3e-417e-af6f-46120fa5603a=""><!--!-->
            <!--!-->
            <span class="ant-menu-title-content"><!--!-->
                    <!--!--><a href="https://cmp-test.cyanmirror.com/console/entity/Reminder" class=""><!--!-->
                            <span><!--!-->
            <span>提醒</span><!--!-->
            </span><!--!-->
<!--!-->
                        </a><!--!-->
            </span><!--!-->
        </li><!--!--><!--!--><!--!-->
<li class="ant-menu-item" role="menuitem" style="padding-left:48px; " _bl_a0662a21-3467-42ff-aa59-057bf8f3be4a=""><!--!-->
            <!--!-->
            <span class="ant-menu-title-content"><!--!-->
                    <!--!--><a href="https://cmp-test.cyanmirror.com/console/entity/ReminderRecord" class=""><!--!-->
                            <span><!--!-->
            <span>提醒记录</span><!--!-->
            </span><!--!-->
<!--!-->
                        </a><!--!-->
            </span><!--!-->
        </li><!--!-->
                        <!--!-->
            <!--!-->
        </ul><!--!-->
    </li><!--!-->
<!--!--><!--!-->
    <li class="ant-menu-submenu ant-menu-submenu-inline" role="menuitem" style="position:relative;" _bl_f7430279-8a9c-4a07-b763-175c28819b1a=""><!--!-->
        <div class="ant-menu-submenu-title" style="padding-left:24px;" role="button" aria-haspopup="true"><!--!-->
            <span class="ant-menu-title-content"><!--!-->
<span><!--!-->
            <span>客户端管理</span><!--!-->
            </span><!--!-->
            </span><!--!-->
            <i class="ant-menu-submenu-arrow"></i><!--!-->
        </div><!--!-->
        <ul direction="ltr" class="ant-menu ant-menu-sub ant-menu-light ant-menu-inline ant-menu-hidden" role="menu" _bl_bad2447a-b9d5-479f-8fd2-a48e1d19bbe0=""><!--!-->
            <!--!--><!--!-->
                <!--!-->
                            <!--!--><!--!--><!--!-->
<li class="ant-menu-item" role="menuitem" style="padding-left:48px; " _bl_bcb753ce-3875-41ea-b086-3d2416e684cb=""><!--!-->
            <!--!-->
            <span class="ant-menu-title-content"><!--!-->
                    <!--!--><a href="https://cmp-test.cyanmirror.com/console/entity/ClientDevice" class=""><!--!-->
                            <span><!--!-->
            <span>客户端设备</span><!--!-->
            </span><!--!-->
<!--!-->
                        </a><!--!-->
            </span><!--!-->
        </li><!--!--><!--!--><!--!-->
<li class="ant-menu-item" role="menuitem" style="padding-left:48px; " _bl_ec6717d7-4a4b-4d4e-b909-239d2ced1887=""><!--!-->
            <!--!-->
            <span class="ant-menu-title-content"><!--!-->
                    <!--!--><a href="https://cmp-test.cyanmirror.com/console/entity/ClientApplication" class=""><!--!-->
                            <span><!--!-->
            <span>客户端应用</span><!--!-->
            </span><!--!-->
<!--!-->
                        </a><!--!-->
            </span><!--!-->
        </li><!--!-->
                        <!--!-->
            <!--!-->
        </ul><!--!-->
    </li><!--!-->
<!--!--><!--!-->
    <li class="ant-menu-submenu ant-menu-submenu-inline" role="menuitem" style="position:relative;" _bl_da2eb716-587f-474c-8cde-444c5f536c16=""><!--!-->
        <div class="ant-menu-submenu-title" style="padding-left:24px;" role="button" aria-haspopup="true"><!--!-->
            <span class="ant-menu-title-content"><!--!-->
<span><!--!-->
            <span>支付</span><!--!-->
            </span><!--!-->
            </span><!--!-->
            <i class="ant-menu-submenu-arrow"></i><!--!-->
        </div><!--!-->
        <ul direction="ltr" class="ant-menu ant-menu-sub ant-menu-light ant-menu-inline ant-menu-hidden" role="menu" _bl_0b23c7d2-99a7-464a-abc0-00af09f36d0c=""><!--!-->
            <!--!--><!--!-->
                <!--!-->
                            <!--!--><!--!--><!--!-->
<li class="ant-menu-item" role="menuitem" style="padding-left:48px; " _bl_8821a5f8-3c3f-4276-8c18-9edc6d6e6e07=""><!--!-->
            <!--!-->
            <span class="ant-menu-title-content"><!--!-->
                    <!--!--><a href="https://cmp-test.cyanmirror.com/console/entity/CollectRecord" class=""><!--!-->
                            <span><!--!-->
            <span>收款记录</span><!--!-->
            </span><!--!-->
<!--!-->
                        </a><!--!-->
            </span><!--!-->
        </li><!--!--><!--!--><!--!-->
<li class="ant-menu-item" role="menuitem" style="padding-left:48px; " _bl_44efe086-c4fa-454b-bb37-33b29bab7c08=""><!--!-->
            <!--!-->
            <span class="ant-menu-title-content"><!--!-->
                    <!--!--><a href="https://cmp-test.cyanmirror.com/console/entity/PaymentPlatform" class=""><!--!-->
                            <span><!--!-->
            <span>支付平台</span><!--!-->
            </span><!--!-->
<!--!-->
                        </a><!--!-->
            </span><!--!-->
        </li><!--!--><!--!--><!--!-->
<li class="ant-menu-item" role="menuitem" style="padding-left:48px; " _bl_bcb40231-516d-49bd-82e8-a157330a7d2a=""><!--!-->
            <!--!-->
            <span class="ant-menu-title-content"><!--!-->
                    <!--!--><a href="https://cmp-test.cyanmirror.com/console/entity/RefundRecord" class=""><!--!-->
                            <span><!--!-->
            <span>退款记录</span><!--!-->
            </span><!--!-->
<!--!-->
                        </a><!--!-->
            </span><!--!-->
        </li><!--!-->
                        <!--!-->
            <!--!-->
        </ul><!--!-->
    </li><!--!-->
<!--!--><!--!-->
    <li class="ant-menu-submenu ant-menu-submenu-inline ant-menu-submenu-open" role="menuitem" style="position:relative;" _bl_d63d4813-e593-4b38-b8b3-162b16bc37a0=""><!--!-->
        <div class="ant-menu-submenu-title" style="padding-left:24px;" role="button" aria-haspopup="true"><!--!-->
            <span class="ant-menu-title-content"><!--!-->
<span><!--!-->
            <span>商品管理</span><!--!-->
            </span><!--!-->
            </span><!--!-->
            <i class="ant-menu-submenu-arrow"></i><!--!-->
        </div><!--!-->
        <ul direction="ltr" class="ant-menu ant-menu-sub ant-menu-light ant-menu-inline" role="menu" _bl_33bd2261-e837-4fc4-9b83-059c3cd44899="" aria-expanded=""><!--!-->
            <!--!--><!--!-->
                <!--!-->
                            <!--!--><!--!--><!--!-->
<li class="ant-menu-item" role="menuitem" style="padding-left:48px; " _bl_1d123ddc-ef21-45e3-ad1d-ffdc781f417d=""><!--!-->
            <!--!-->
            <span class="ant-menu-title-content"><!--!-->
                    <!--!--><a href="https://cmp-test.cyanmirror.com/console/entity/Category" class=""><!--!-->
                            <span><!--!-->
            <span>商品橱柜</span><!--!-->
            </span><!--!-->
<!--!-->
                        </a><!--!-->
            </span><!--!-->
        </li><!--!--><!--!--><!--!-->
<li class="ant-menu-item" role="menuitem" style="padding-left:48px; " _bl_37a7acca-cb9f-4503-826d-16ecef61f5ce=""><!--!-->
            <!--!-->
            <span class="ant-menu-title-content"><!--!-->
                    <!--!--><a href="https://cmp-test.cyanmirror.com/console/entity/Item" class=""><!--!-->
                            <span><!--!-->
            <span>商品</span><!--!-->
            </span><!--!-->
<!--!-->
                        </a><!--!-->
            </span><!--!-->
        </li><!--!--><!--!--><!--!-->
<li class="ant-menu-item" role="menuitem" style="padding-left:48px; " _bl_8abebcfc-c655-49cb-bdc5-694afa8a3119=""><!--!-->
            <!--!-->
            <span class="ant-menu-title-content"><!--!-->
                    <!--!--><a href="https://cmp-test.cyanmirror.com/console/entity/ItemType" class=""><!--!-->
                            <span><!--!-->
            <span>商品类型</span><!--!-->
            </span><!--!-->
<!--!-->
                        </a><!--!-->
            </span><!--!-->
        </li><!--!--><!--!--><!--!-->
<li class="ant-menu-item ant-menu-item-selected" role="menuitem" style="padding-left:48px; " _bl_df321fee-1a2d-42fe-b6d6-c62bdfa1becd=""><!--!-->
            <!--!-->
            <span class="ant-menu-title-content"><!--!-->
                    <!--!--><a href="https://cmp-test.cyanmirror.com/console/entity/ItemSpec" class=""><!--!-->
                            <span><!--!-->
            <span>商品规格</span><!--!-->
            </span><!--!-->
<!--!-->
                        </a><!--!-->
            </span><!--!-->
        </li><!--!-->
                        <!--!-->
            <!--!-->
        </ul><!--!-->
    </li><!--!-->
<!--!--><!--!-->
    <li class="ant-menu-submenu ant-menu-submenu-inline" role="menuitem" style="position:relative;" _bl_421c0e85-43cb-42fb-85f5-989fe127d1b1=""><!--!-->
        <div class="ant-menu-submenu-title" style="padding-left:24px;" role="button" aria-haspopup="true"><!--!-->
            <span class="ant-menu-title-content"><!--!-->
<span><!--!-->
            <span>产品管理</span><!--!-->
            </span><!--!-->
            </span><!--!-->
            <i class="ant-menu-submenu-arrow"></i><!--!-->
        </div><!--!-->
        <ul direction="ltr" class="ant-menu ant-menu-sub ant-menu-light ant-menu-inline ant-menu-hidden" role="menu" _bl_b665a048-57a1-4643-9a82-8522cca49d20=""><!--!-->
            <!--!--><!--!-->
                <!--!-->
                            <!--!--><!--!--><!--!-->
<li class="ant-menu-item" role="menuitem" style="padding-left:48px; " _bl_244a01be-0353-4623-ad71-f07e9a491ddd=""><!--!-->
            <!--!-->
            <span class="ant-menu-title-content"><!--!-->
                    <!--!--><a href="https://cmp-test.cyanmirror.com/console/entity/Product" class=""><!--!-->
                            <span><!--!-->
            <span>产品</span><!--!-->
            </span><!--!-->
<!--!-->
                        </a><!--!-->
            </span><!--!-->
        </li><!--!--><!--!--><!--!-->
<li class="ant-menu-item" role="menuitem" style="padding-left:48px; " _bl_909dfeca-d708-4236-95e9-407ed337e5a4=""><!--!-->
            <!--!-->
            <span class="ant-menu-title-content"><!--!-->
                    <!--!--><a href="https://cmp-test.cyanmirror.com/console/entity/ProductFeature" class=""><!--!-->
                            <span><!--!-->
            <span>产品特性</span><!--!-->
            </span><!--!-->
<!--!-->
                        </a><!--!-->
            </span><!--!-->
        </li><!--!--><!--!--><!--!-->
<li class="ant-menu-item" role="menuitem" style="padding-left:48px; " _bl_c80741b2-bb38-4257-87a5-8ec15de3a09c=""><!--!-->
            <!--!-->
            <span class="ant-menu-title-content"><!--!-->
                    <!--!--><a href="https://cmp-test.cyanmirror.com/console/entity/ProductSpec" class=""><!--!-->
                            <span><!--!-->
            <span>产品规格</span><!--!-->
            </span><!--!-->
<!--!-->
                        </a><!--!-->
            </span><!--!-->
        </li><!--!-->
                        <!--!-->
            <!--!-->
        </ul><!--!-->
    </li><!--!-->
<!--!--><!--!-->
    <li class="ant-menu-submenu ant-menu-submenu-inline" role="menuitem" style="position:relative;" _bl_0fcb6879-ecb2-463f-a103-65a8fa2791b6=""><!--!-->
        <div class="ant-menu-submenu-title" style="padding-left:24px;" role="button" aria-haspopup="true"><!--!-->
            <span class="ant-menu-title-content"><!--!-->
<span><!--!-->
            <span>报价单管理</span><!--!-->
            </span><!--!-->
            </span><!--!-->
            <i class="ant-menu-submenu-arrow"></i><!--!-->
        </div><!--!-->
        <ul direction="ltr" class="ant-menu ant-menu-sub ant-menu-light ant-menu-inline ant-menu-hidden" role="menu" _bl_ce014b57-bec0-4c2f-a10f-e5638dcd2500=""><!--!-->
            <!--!--><!--!-->
                <!--!-->
                            <!--!--><!--!--><!--!-->
<li class="ant-menu-item" role="menuitem" style="padding-left:48px; " _bl_69786078-c9e9-4f7c-81e9-f53ac14b8afc=""><!--!-->
            <!--!-->
            <span class="ant-menu-title-content"><!--!-->
                    <!--!--><a href="https://cmp-test.cyanmirror.com/console/entity/PriceSheet" class=""><!--!-->
                            <span><!--!-->
            <span>报价单</span><!--!-->
            </span><!--!-->
<!--!-->
                        </a><!--!-->
            </span><!--!-->
        </li><!--!-->
                        <!--!-->
            <!--!-->
        </ul><!--!-->
    </li><!--!-->
<!--!--><!--!-->
    <li class="ant-menu-submenu ant-menu-submenu-inline" role="menuitem" style="position:relative;" _bl_03ca0e3b-c671-4122-b2ac-14124ac416e8=""><!--!-->
        <div class="ant-menu-submenu-title" style="padding-left:24px;" role="button" aria-haspopup="true"><!--!-->
            <span class="ant-menu-title-content"><!--!-->
<span><!--!-->
            <span>交易管理</span><!--!-->
            </span><!--!-->
            </span><!--!-->
            <i class="ant-menu-submenu-arrow"></i><!--!-->
        </div><!--!-->
        <ul direction="ltr" class="ant-menu ant-menu-sub ant-menu-light ant-menu-inline ant-menu-hidden" role="menu" _bl_6758a7e8-d0ac-426d-adf3-af51f67c4c2a=""><!--!-->
            <!--!--><!--!-->
                <!--!-->
                            <!--!--><!--!--><!--!-->
<li class="ant-menu-item" role="menuitem" style="padding-left:48px; " _bl_42ce2b5e-d97a-4d34-9c10-692fd3b14cd5=""><!--!-->
            <!--!-->
            <span class="ant-menu-title-content"><!--!-->
                    <!--!--><a href="https://cmp-test.cyanmirror.com/console/entity/Trade" class=""><!--!-->
                            <span><!--!-->
            <span>交易</span><!--!-->
            </span><!--!-->
<!--!-->
                        </a><!--!-->
            </span><!--!-->
        </li><!--!--><!--!--><!--!-->
<li class="ant-menu-item" role="menuitem" style="padding-left:48px; " _bl_8335c96b-1fed-40f2-8bec-f5a19035041a=""><!--!-->
            <!--!-->
            <span class="ant-menu-title-content"><!--!-->
                    <!--!--><a href="https://cmp-test.cyanmirror.com/console/entity/TradeSellerGroup" class=""><!--!-->
                            <span><!--!-->
            <span>卖家分组</span><!--!-->
            </span><!--!-->
<!--!-->
                        </a><!--!-->
            </span><!--!-->
        </li><!--!--><!--!--><!--!-->
<li class="ant-menu-item" role="menuitem" style="padding-left:48px; " _bl_9d50fa29-6855-450a-8389-6ea41ca0f2c2=""><!--!-->
            <!--!-->
            <span class="ant-menu-title-content"><!--!-->
                    <!--!--><a href="https://cmp-test.cyanmirror.com/console/entity/TradeItem" class=""><!--!-->
                            <span><!--!-->
            <span>交易项目</span><!--!-->
            </span><!--!-->
<!--!-->
                        </a><!--!-->
            </span><!--!-->
        </li><!--!--><!--!--><!--!-->
<li class="ant-menu-item" role="menuitem" style="padding-left:48px; " _bl_3bb784fb-d2fc-49e0-a7e2-aef2fcd25bb7=""><!--!-->
            <!--!-->
            <span class="ant-menu-title-content"><!--!-->
                    <!--!--><a href="https://cmp-test.cyanmirror.com/console/entity/TradeItemSpec" class=""><!--!-->
                            <span><!--!-->
            <span>交易项目规格</span><!--!-->
            </span><!--!-->
<!--!-->
                        </a><!--!-->
            </span><!--!-->
        </li><!--!--><!--!--><!--!-->
<li class="ant-menu-item" role="menuitem" style="padding-left:48px; " _bl_880681c2-b98e-4027-98f9-f35711a9b925=""><!--!-->
            <!--!-->
            <span class="ant-menu-title-content"><!--!-->
                    <!--!--><a href="https://cmp-test.cyanmirror.com/console/entity/TradeRollbackRecord" class=""><!--!-->
                            <span><!--!-->
            <span>交易撤销记录</span><!--!-->
            </span><!--!-->
<!--!-->
                        </a><!--!-->
            </span><!--!-->
        </li><!--!--><!--!--><!--!-->
<li class="ant-menu-item" role="menuitem" style="padding-left:48px; " _bl_4a9526ec-2d79-4cd6-96cf-1fd9a240a42e=""><!--!-->
            <!--!-->
            <span class="ant-menu-title-content"><!--!-->
                    <!--!--><a href="https://cmp-test.cyanmirror.com/console/entity/TradeRollbackItem" class=""><!--!-->
                            <span><!--!-->
            <span>交易撤销项目</span><!--!-->
            </span><!--!-->
<!--!-->
                        </a><!--!-->
            </span><!--!-->
        </li><!--!-->
                        <!--!-->
            <!--!-->
        </ul><!--!-->
    </li><!--!-->
<!--!--><!--!-->
    <li class="ant-menu-submenu ant-menu-submenu-inline" role="menuitem" style="position:relative;" _bl_565a0b39-e439-45e9-a9d5-f2d301c1f70a=""><!--!-->
        <div class="ant-menu-submenu-title" style="padding-left:24px;" role="button" aria-haspopup="true"><!--!-->
            <span class="ant-menu-title-content"><!--!-->
<span><!--!-->
            <span>财务管理</span><!--!-->
            </span><!--!-->
            </span><!--!-->
            <i class="ant-menu-submenu-arrow"></i><!--!-->
        </div><!--!-->
        <ul direction="ltr" class="ant-menu ant-menu-sub ant-menu-light ant-menu-inline ant-menu-hidden" role="menu" _bl_98da6f10-5d63-4a55-97ce-64ecddd4d8a9=""><!--!-->
            <!--!--><!--!-->
                <!--!-->
                            <!--!--><!--!--><!--!-->
<li class="ant-menu-item" role="menuitem" style="padding-left:48px; " _bl_7453a166-ecda-480f-a3ba-fbdc6e872eb0=""><!--!-->
            <!--!-->
            <span class="ant-menu-title-content"><!--!-->
                    <!--!--><a href="https://cmp-test.cyanmirror.com/console/entity/AccountInternal" class=""><!--!-->
                            <span><!--!-->
            <span>内部账户</span><!--!-->
            </span><!--!-->
<!--!-->
                        </a><!--!-->
            </span><!--!-->
        </li><!--!--><!--!--><!--!-->
<li class="ant-menu-item" role="menuitem" style="padding-left:48px; " _bl_dfae154f-fd7d-4ddb-803d-aa45e808200d=""><!--!-->
            <!--!-->
            <span class="ant-menu-title-content"><!--!-->
                    <!--!--><a href="https://cmp-test.cyanmirror.com/console/entity/DepositRecord" class=""><!--!-->
                            <span><!--!-->
            <span>账户充值记录</span><!--!-->
            </span><!--!-->
<!--!-->
                        </a><!--!-->
            </span><!--!-->
        </li><!--!--><!--!--><!--!-->
<li class="ant-menu-item" role="menuitem" style="padding-left:48px; " _bl_db2ed635-fc96-4de4-8213-646de63a18bc=""><!--!-->
            <!--!-->
            <span class="ant-menu-title-content"><!--!-->
                    <!--!--><a href="https://cmp-test.cyanmirror.com/console/entity/TransferRecord" class=""><!--!-->
                            <span><!--!-->
            <span>账户转账记录</span><!--!-->
            </span><!--!-->
<!--!-->
                        </a><!--!-->
            </span><!--!-->
        </li><!--!--><!--!--><!--!-->
<li class="ant-menu-item" role="menuitem" style="padding-left:48px; " _bl_0f610f2c-78a7-4558-9e93-01797ee8a88b=""><!--!-->
            <!--!-->
            <span class="ant-menu-title-content"><!--!-->
                    <!--!--><a href="https://cmp-test.cyanmirror.com/console/entity/TransferItem" class=""><!--!-->
                            <span><!--!-->
            <span>账户转账项目</span><!--!-->
            </span><!--!-->
<!--!-->
                        </a><!--!-->
            </span><!--!-->
        </li><!--!--><!--!--><!--!-->
<li class="ant-menu-item" role="menuitem" style="padding-left:48px; " _bl_abd91241-7f78-4fdb-b9fa-bdfc8462a830=""><!--!-->
            <!--!-->
            <span class="ant-menu-title-content"><!--!-->
                    <!--!--><a href="https://cmp-test.cyanmirror.com/console/entity/AccountTitle" class=""><!--!-->
                            <span><!--!-->
            <span>账户类型</span><!--!-->
            </span><!--!-->
<!--!-->
                        </a><!--!-->
            </span><!--!-->
        </li><!--!-->
                        <!--!-->
            <!--!-->
        </ul><!--!-->
    </li><!--!-->
<!--!--><!--!-->
    <li class="ant-menu-submenu ant-menu-submenu-inline" role="menuitem" style="position:relative;" _bl_eace406b-8c0b-43f9-b3f7-a16693192a6c=""><!--!-->
        <div class="ant-menu-submenu-title" style="padding-left:24px;" role="button" aria-haspopup="true"><!--!-->
            <span class="ant-menu-title-content"><!--!-->
<span><!--!-->
            <span>结算</span><!--!-->
            </span><!--!-->
            </span><!--!-->
            <i class="ant-menu-submenu-arrow"></i><!--!-->
        </div><!--!-->
        <ul direction="ltr" class="ant-menu ant-menu-sub ant-menu-light ant-menu-inline ant-menu-hidden" role="menu" _bl_2c1e3366-5b62-4935-b740-ac81e814c7f7=""><!--!-->
            <!--!--><!--!-->
                <!--!-->
                            <!--!--><!--!--><!--!-->
<li class="ant-menu-item" role="menuitem" style="padding-left:48px; " _bl_ff4ade45-679d-46af-81c0-9205aae4f8ed=""><!--!-->
            <!--!-->
            <span class="ant-menu-title-content"><!--!-->
                    <!--!--><a href="https://cmp-test.cyanmirror.com/console/entity/SettlementRollbackRecord" class=""><!--!-->
                            <span><!--!-->
            <span>结算回滚记录</span><!--!-->
            </span><!--!-->
<!--!-->
                        </a><!--!-->
            </span><!--!-->
        </li><!--!--><!--!--><!--!-->
<li class="ant-menu-item" role="menuitem" style="padding-left:48px; " _bl_33d0e943-8939-412f-9afd-893dae9b666a=""><!--!-->
            <!--!-->
            <span class="ant-menu-title-content"><!--!-->
                    <!--!--><a href="https://cmp-test.cyanmirror.com/console/entity/DataSettlementRecord" class=""><!--!-->
                            <span><!--!-->
            <span>结算记录</span><!--!-->
            </span><!--!-->
<!--!-->
                        </a><!--!-->
            </span><!--!-->
        </li><!--!--><!--!--><!--!-->
<li class="ant-menu-item" role="menuitem" style="padding-left:48px; " _bl_a8fd31be-8386-4217-baee-d52b78b133c6=""><!--!-->
            <!--!-->
            <span class="ant-menu-title-content"><!--!-->
                    <!--!--><a href="https://cmp-test.cyanmirror.com/console/entity/DataSettlementSource" class=""><!--!-->
                            <span><!--!-->
            <span>结算来源</span><!--!-->
            </span><!--!-->
<!--!-->
                        </a><!--!-->
            </span><!--!-->
        </li><!--!-->
                        <!--!-->
            <!--!-->
        </ul><!--!-->
    </li><!--!-->
<!--!--><!--!-->
    <li class="ant-menu-submenu ant-menu-submenu-inline" role="menuitem" style="position:relative;" _bl_0c5f859c-c9fc-4c93-b9c6-e8494cbe6801=""><!--!-->
        <div class="ant-menu-submenu-title" style="padding-left:24px;" role="button" aria-haspopup="true"><!--!-->
            <span class="ant-menu-title-content"><!--!-->
<span><!--!-->
            <span>服务授权</span><!--!-->
            </span><!--!-->
            </span><!--!-->
            <i class="ant-menu-submenu-arrow"></i><!--!-->
        </div><!--!-->
        <ul direction="ltr" class="ant-menu ant-menu-sub ant-menu-light ant-menu-inline ant-menu-hidden" role="menu" _bl_ba416917-1447-4f37-a6ae-394ab5ebfcdb=""><!--!-->
            <!--!--><!--!-->
                <!--!-->
                            <!--!--><!--!--><!--!-->
<li class="ant-menu-item" role="menuitem" style="padding-left:48px; " _bl_6dab8243-942d-4eca-bf14-e74942154ba0=""><!--!-->
            <!--!-->
            <span class="ant-menu-title-content"><!--!-->
                    <!--!--><a href="https://cmp-test.cyanmirror.com/console/entity/ServiceGrant" class=""><!--!-->
                            <span><!--!-->
            <span>服务授权</span><!--!-->
            </span><!--!-->
<!--!-->
                        </a><!--!-->
            </span><!--!-->
        </li><!--!--><!--!--><!--!-->
<li class="ant-menu-item" role="menuitem" style="padding-left:48px; " _bl_159d3cf6-9cd6-41e2-9f81-3accbdd3eedb=""><!--!-->
            <!--!-->
            <span class="ant-menu-title-content"><!--!-->
                    <!--!--><a href="https://cmp-test.cyanmirror.com/console/entity/ServiceInstance" class=""><!--!-->
                            <span><!--!-->
            <span>服务实例</span><!--!-->
            </span><!--!-->
<!--!-->
                        </a><!--!-->
            </span><!--!-->
        </li><!--!--><!--!--><!--!-->
<li class="ant-menu-item" role="menuitem" style="padding-left:48px; " _bl_ff05f776-5cf8-49fe-9352-0e42f93db605=""><!--!-->
            <!--!-->
            <span class="ant-menu-title-content"><!--!-->
                    <!--!--><a href="https://cmp-test.cyanmirror.com/console/entity/ServiceRecord" class=""><!--!-->
                            <span><!--!-->
            <span>服务记录</span><!--!-->
            </span><!--!-->
<!--!-->
                        </a><!--!-->
            </span><!--!-->
        </li><!--!-->
                        <!--!-->
            <!--!-->
        </ul><!--!-->
    </li><!--!-->
<!--!--><!--!-->
    <li class="ant-menu-submenu ant-menu-submenu-inline" role="menuitem" style="position:relative;" _bl_0e09ad54-f7bf-402a-a758-e8020130a44f=""><!--!-->
        <div class="ant-menu-submenu-title" style="padding-left:24px;" role="button" aria-haspopup="true"><!--!-->
            <span class="ant-menu-title-content"><!--!-->
<span><!--!-->
            <span>数字资产管理</span><!--!-->
            </span><!--!-->
            </span><!--!-->
            <i class="ant-menu-submenu-arrow"></i><!--!-->
        </div><!--!-->
        <ul direction="ltr" class="ant-menu ant-menu-sub ant-menu-light ant-menu-inline ant-menu-hidden" role="menu" _bl_dc743267-dfb4-45a3-8d7f-83ce74e194a7=""><!--!-->
            <!--!--><!--!-->
                <!--!-->
                            <!--!--><!--!--><!--!-->
<li class="ant-menu-item" role="menuitem" style="padding-left:48px; " _bl_d52b4807-a348-4e30-a12b-6eac0f50a7e9=""><!--!-->
            <!--!-->
            <span class="ant-menu-title-content"><!--!-->
                    <!--!--><a href="https://cmp-test.cyanmirror.com/console/entity/FileStorage" class=""><!--!-->
                            <span><!--!-->
            <span>文件存储</span><!--!-->
            </span><!--!-->
<!--!-->
                        </a><!--!-->
            </span><!--!-->
        </li><!--!--><!--!--><!--!-->
<li class="ant-menu-item" role="menuitem" style="padding-left:48px; " _bl_534b2dec-6460-4dff-a7b4-29c26e60368e=""><!--!-->
            <!--!-->
            <span class="ant-menu-title-content"><!--!-->
                    <!--!--><a href="https://cmp-test.cyanmirror.com/console/entity/FileItem" class=""><!--!-->
                            <span><!--!-->
            <span>文件列表</span><!--!-->
            </span><!--!-->
<!--!-->
                        </a><!--!-->
            </span><!--!-->
        </li><!--!--><!--!--><!--!-->
<li class="ant-menu-item" role="menuitem" style="padding-left:48px; " _bl_11d0f5c9-2ba1-4c34-a822-9a993a6aba58=""><!--!-->
            <!--!-->
            <span class="ant-menu-title-content"><!--!-->
                    <!--!--><a href="https://cmp-test.cyanmirror.com/console/entity/FileNode" class=""><!--!-->
                            <span><!--!-->
            <span>文件节点</span><!--!-->
            </span><!--!-->
<!--!-->
                        </a><!--!-->
            </span><!--!-->
        </li><!--!--><!--!--><!--!-->
<li class="ant-menu-item" role="menuitem" style="padding-left:48px; " _bl_58893afc-9e0d-4eed-8877-31d28e851e9f=""><!--!-->
            <!--!-->
            <span class="ant-menu-title-content"><!--!-->
                    <!--!--><a href="https://cmp-test.cyanmirror.com/console/entity/FileSharedItem" class=""><!--!-->
                            <span><!--!-->
            <span>文件分享</span><!--!-->
            </span><!--!-->
<!--!-->
                        </a><!--!-->
            </span><!--!-->
        </li><!--!--><!--!--><!--!-->
<li class="ant-menu-item" role="menuitem" style="padding-left:48px; " _bl_ebcb552f-00d2-4230-b1fc-0bf75dd4ad69=""><!--!-->
            <!--!-->
            <span class="ant-menu-title-content"><!--!-->
                    <!--!--><a href="https://cmp-test.cyanmirror.com/console/entity/FileSharedItemUser" class=""><!--!-->
                            <span><!--!-->
            <span>文件分享用户</span><!--!-->
            </span><!--!-->
<!--!-->
                        </a><!--!-->
            </span><!--!-->
        </li><!--!-->
                        <!--!-->
            <!--!-->
        </ul><!--!-->
    </li><!--!-->
<!--!--><!--!-->
    <li class="ant-menu-submenu ant-menu-submenu-inline" role="menuitem" style="position:relative;" _bl_42e633b0-0d06-4866-9232-54b1929b9388=""><!--!-->
        <div class="ant-menu-submenu-title" style="padding-left:24px;" role="button" aria-haspopup="true"><!--!-->
            <span class="ant-menu-title-content"><!--!-->
<span><!--!-->
            <span>服务商管理</span><!--!-->
            </span><!--!-->
            </span><!--!-->
            <i class="ant-menu-submenu-arrow"></i><!--!-->
        </div><!--!-->
        <ul direction="ltr" class="ant-menu ant-menu-sub ant-menu-light ant-menu-inline ant-menu-hidden" role="menu" _bl_85eff60b-dba4-4b4a-865a-7b0b23809ebb=""><!--!-->
            <!--!--><!--!-->
                <!--!-->
                            <!--!--><!--!--><!--!-->
<li class="ant-menu-item" role="menuitem" style="padding-left:48px; " _bl_07e97459-ad30-44b8-8946-5948e26a8883=""><!--!-->
            <!--!-->
            <span class="ant-menu-title-content"><!--!-->
                    <!--!--><a href="https://cmp-test.cyanmirror.com/console/entity/ServiceMerchantM" class=""><!--!-->
                            <span><!--!-->
            <span>服务商</span><!--!-->
            </span><!--!-->
<!--!-->
                        </a><!--!-->
            </span><!--!-->
        </li><!--!-->
                        <!--!-->
            <!--!-->
        </ul><!--!-->
    </li><!--!-->
<!--!--><!--!-->
    <li class="ant-menu-submenu ant-menu-submenu-inline" role="menuitem" style="position:relative;" _bl_7ce2a167-49f9-4264-9527-af90163b75ab=""><!--!-->
        <div class="ant-menu-submenu-title" style="padding-left:24px;" role="button" aria-haspopup="true"><!--!-->
            <span class="ant-menu-title-content"><!--!-->
<span><!--!-->
            <span>代金券</span><!--!-->
            </span><!--!-->
            </span><!--!-->
            <i class="ant-menu-submenu-arrow"></i><!--!-->
        </div><!--!-->
        <ul direction="ltr" class="ant-menu ant-menu-sub ant-menu-light ant-menu-inline ant-menu-hidden" role="menu" _bl_4972aced-5e49-4e03-8d6f-71bd24bb4558=""><!--!-->
            <!--!--><!--!-->
                <!--!-->
                            <!--!--><!--!--><!--!-->
<li class="ant-menu-item" role="menuitem" style="padding-left:48px; " _bl_c0fd27ac-a50f-4383-adf8-ecc9e9b183e9=""><!--!-->
            <!--!-->
            <span class="ant-menu-title-content"><!--!-->
                    <!--!--><a href="https://cmp-test.cyanmirror.com/console/entity/TempVoucher" class=""><!--!-->
                            <span><!--!-->
            <span>代金券</span><!--!-->
            </span><!--!-->
<!--!-->
                        </a><!--!-->
            </span><!--!-->
        </li><!--!-->
                        <!--!-->
            <!--!-->
        </ul><!--!-->
    </li><!--!-->
<!--!--><!--!-->
    <li class="ant-menu-submenu ant-menu-submenu-inline" role="menuitem" style="position:relative;" _bl_aba6629a-d3bb-4391-a627-6639323dfead=""><!--!-->
        <div class="ant-menu-submenu-title" style="padding-left:24px;" role="button" aria-haspopup="true"><!--!-->
            <span class="ant-menu-title-content"><!--!-->
<span><!--!-->
            <span>合作授权管理</span><!--!-->
            </span><!--!-->
            </span><!--!-->
            <i class="ant-menu-submenu-arrow"></i><!--!-->
        </div><!--!-->
        <ul direction="ltr" class="ant-menu ant-menu-sub ant-menu-light ant-menu-inline ant-menu-hidden" role="menu" _bl_2936cc6a-8c98-435b-b585-a3c439893c6e=""><!--!-->
            <!--!--><!--!-->
                <!--!-->
                            <!--!--><!--!--><!--!-->
<li class="ant-menu-item" role="menuitem" style="padding-left:48px; " _bl_926a05d5-32df-4fbb-aaa8-e8550dce742f=""><!--!-->
            <!--!-->
            <span class="ant-menu-title-content"><!--!-->
                    <!--!--><a href="https://cmp-test.cyanmirror.com/console/list/SalesCooperationGrant" class=""><!--!-->
                            <span><!--!-->
            <span>合作授权</span><!--!-->
            </span><!--!-->
<!--!-->
                        </a><!--!-->
            </span><!--!-->
        </li><!--!--><!--!--><!--!-->
<li class="ant-menu-item" role="menuitem" style="padding-left:48px; " _bl_a06569d4-95bf-4686-acc4-10dd0695d080=""><!--!-->
            <!--!-->
            <span class="ant-menu-title-content"><!--!-->
                    <!--!--><a href="https://cmp-test.cyanmirror.com/console/list/SalesGrantCooperator" class=""><!--!-->
                            <span><!--!-->
            <span>授权合作方</span><!--!-->
            </span><!--!-->
<!--!-->
                        </a><!--!-->
            </span><!--!-->
        </li><!--!--><!--!--><!--!-->
<li class="ant-menu-item" role="menuitem" style="padding-left:48px; " _bl_8f681c53-1aea-4931-8307-053229c707b7=""><!--!-->
            <!--!-->
            <span class="ant-menu-title-content"><!--!-->
                    <!--!--><a href="https://cmp-test.cyanmirror.com/console/entity/SalesCooperator" class=""><!--!-->
                            <span><!--!-->
            <span>合作方</span><!--!-->
            </span><!--!-->
<!--!-->
                        </a><!--!-->
            </span><!--!-->
        </li><!--!-->
                        <!--!-->
            <!--!-->
        </ul><!--!-->
    </li><!--!-->
<!--!--><!--!-->
    <li class="ant-menu-submenu ant-menu-submenu-inline" role="menuitem" style="position:relative;" _bl_0f4c0f9a-cf97-4441-83f8-5c321c7dacc4=""><!--!-->
        <div class="ant-menu-submenu-title" style="padding-left:24px;" role="button" aria-haspopup="true"><!--!-->
            <span class="ant-menu-title-content"><!--!-->
<span><!--!-->
            <span>客户管理</span><!--!-->
            </span><!--!-->
            </span><!--!-->
            <i class="ant-menu-submenu-arrow"></i><!--!-->
        </div><!--!-->
        <ul direction="ltr" class="ant-menu ant-menu-sub ant-menu-light ant-menu-inline ant-menu-hidden" role="menu" _bl_aa3f45cf-11df-48e2-9067-70794661deac=""><!--!-->
            <!--!--><!--!-->
                <!--!-->
                            <!--!--><!--!--><!--!-->
<li class="ant-menu-item" role="menuitem" style="padding-left:48px; " _bl_0b4766c2-8010-456e-b37f-a32dded80609=""><!--!-->
            <!--!-->
            <span class="ant-menu-title-content"><!--!-->
                    <!--!--><a href="https://cmp-test.cyanmirror.com/console/entity/SalesCustomer" class=""><!--!-->
                            <span><!--!-->
            <span>客户</span><!--!-->
            </span><!--!-->
<!--!-->
                        </a><!--!-->
            </span><!--!-->
        </li><!--!-->
                        <!--!-->
            <!--!-->
        </ul><!--!-->
    </li><!--!-->
<!--!-->
<!--!-->
    </ul><!--!-->
<!--!-->
    </div><!--!-->

    
    <div class="ant-pro-sider-links"><!--!-->
        <!--!--><!--!--><!--!-->
    <ul class="ant-pro-sider-link-menu ant-menu ant-menu-root ant-menu-light ant-menu-inline ant-menu-unselectable" id="ant-blazor-112eff5f-3eae-4a5c-919f-d92c7120b690" direction="ltr" role="menu" _bl_7eb9bea1-2e39-4ad4-90af-f20cad97aa39=""><!--!-->
        <!--!-->
<!--!-->
                <!--!--><!--!--><!--!-->
        <!--!--><!--!-->
                <li class="ant-pro-sider-collapsed-button ant-menu-item" role="menuitem" style="padding-left:24px; " _bl_c63d7a09-88a4-4300-a2f9-ecac9d2a53fe=""><!--!-->
            <!--!-->
            <span class="ant-menu-title-content"><!--!-->
<!--!-->
                    <!--!--><span class="anticon anticon-menu-fold" id="ant-blazor-d05c1024-50e9-4cec-9d43-2a6414e3ca94" role="img" _bl_1b5fc9ac-ebd5-4121-96f7-f1ff2468adf6=""><!--!-->
<!--!--><svg focusable="false" width="1em" height="1em" fill="currentColor" style="pointer-events: none;" xmlns="http://www.w3.org/2000/svg" class="icon" viewBox="0 0 1024 1024"><path d="M408 442h480c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8H408c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8zm-8 204c0 4.4 3.6 8 8 8h480c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8H408c-4.4 0-8 3.6-8 8v56zm504-486H120c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zm0 632H120c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zM115.4 518.9L271.7 642c5.8 4.6 14.4.5 14.4-6.9V388.9c0-7.4-8.5-11.5-14.4-6.9L115.4 505.1a8.74 8.74 0 0 0 0 13.8z"></path></svg><!--!-->
</span><!--!-->
                            </span><!--!-->
        </li><!--!-->
            <!--!-->
<!--!--><!--!-->
    <!--!--><!--!-->
        <!--!--><!--!-->
    <!--!-->
<!--!-->
<!--!-->
        <!--!-->
    </ul><!--!-->
<!--!-->
    </div><!--!-->
<!--!-->
        </div><!--!-->
    <!--!-->
</aside><!--!-->
                    <!--!-->
                <!--!-->
<!--!-->
        <!--!--><!--!--><!--!-->
    <section class="ant-layout" style="padding-left: 208px; position: relative;" id="ant-blazor-a12cfff0-c5d4-4af8-b933-74590df0a5aa"><!--!-->
        <!--!-->
                <!--!--><!--!-->
                    <!--!-->    <!--!--><header class="ant-layout-header ant-header" style="height:48px; line-height: 48px; background: transparent;" id="ant-blazor-651f3e81-ba65-47c8-8514-60949c6e3836" _bl_3f28da63-8dee-4739-942f-265ed64f58ea=""><!--!-->
    <!--!-->
</header><!--!-->
<!--!--><header class="ant-pro-fixed-header ant-layout-header ant-header" style="padding: 0;height: 48px;line-height: 48px;width: 100%;z-index: 100;right: 0;" id="ant-blazor-4680e409-19c0-49d6-81fc-31ebb6635e7e" _bl_8abdf3cc-b3a0-4be9-bd1d-6c707ad3d46c=""><!--!-->
    <!--!-->
        <!--!-->    <div class="ant-pro-global-header ant-pro-global-header-layout-mix"><!--!-->
                <div class="ant-pro-global-header-logo" style="min-width:208px"><!--!-->
                    <a href="https://cmp-test.cyanmirror.com/"><!--!-->
                            <img src="./商品规格_files/icon.svg" alt="/images/icon.svg"><!--!-->
<!--!-->
<!--!-->
                            <h1 tabindex="-1">青镜直播</h1><!--!-->
                    </a><!--!-->
                </div><!--!-->
<!--!-->
        <div style="flex: 1"><!--!-->
            <!--!-->
        </div><!--!-->
<!--!--><div style="display:flex;align-content:end"><!--!--><!--!--><!--!-->
<!--!-->
        <span class="action account ant-dropdown-trigger" _bl_660d4ab1-0688-4188-b89d-80344f594606=""><!--!-->

                <!--!-->    <span class="avatar ant-avatar ant-avatar-sm ant-avatar-image" style=" " id="ant-blazor-7f7e567f-ef36-43ed-8ee6-b056a5633fb2" _bl_dfc3ecf8-64b1-44ca-8ec4-9f908c26c4ab=""><!--!-->
            <img src="./商品规格_files/user-white.png" alt="avatar"><!--!-->
    </span><!--!-->
<!--!-->
<!--!-->
            <span class="name anticon">sysadmin</span><!--!-->
        </span><!--!-->
    <!--!-->
<!--!--><!--!-->
    <!--!--><!--!-->
        <!--!--><!--!-->
    <!--!-->
</div>    </div><!--!-->
<!--!-->
<!--!-->
</header><!--!-->
                <!--!-->
<!--!-->
            <!--!--><!--!--><main class="ant-pro-basicLayout-content ant-pro-basicLayout-has-header ant-layout-content" id="ant-blazor-44fae117-6e56-4eb8-a850-d05028bfedd0" _bl_e3cdcd7e-2844-42d4-903b-354d7db26627=""><!--!-->
    <!--!-->
    

    <div class="ant-pro-basicLayout-children-content-wrap"><!--!-->
        <!--!-->
<!--!--><!--!--><!--!--><!--!-->

<!--!--><div class="ant-pro-page-container "><!--!-->
    <div class="ant-pro-page-container-warp"><!--!-->
        
            <!--!--><!--!--><!--!-->
  <div class="ant-page-header has-breadcrumb" id="ant-blazor-078537ed-8c2b-49ec-999f-1cb9370615fb" _bl_61d349cc-3767-4ae4-82c5-43f7a0868aba=""><!--!-->

    <!--!--><nav class="ant-breadcrumb" id="ant-blazor-cc96b9d7-23bc-41c3-8ba7-e7744cd56cca" _bl_6f28b9eb-0e24-454c-aa8b-7d5e24b2cd90=""><!--!-->
    <ol><!--!-->
        <!--!--><!--!-->
            <!--!--><li _bl_532a49a2-348b-4f62-8f29-9aa39d57ab7f=""><!--!-->
        <span class="ant-breadcrumb-link"><!--!-->
            <a href="https://cmp-test.cyanmirror.com/"><!--!-->首页</a><!--!-->
        </span><!--!-->
<!--!-->
        <span class="ant-breadcrumb-separator"><!--!-->
            /<!--!-->
        </span><!--!-->
</li><!--!-->
            <!--!--><li _bl_41296d4d-d932-45c7-a085-7cf9fa3459ca=""><!--!-->
        <span class="ant-breadcrumb-link"><!--!-->
            商品规格<!--!-->
        </span><!--!-->
<!--!-->
        <span class="ant-breadcrumb-separator"><!--!-->
            /<!--!-->
        </span><!--!-->
</li><!--!-->
            <!--!--><li _bl_e9d931d4-e893-4724-8525-3451956a8cc8=""><!--!-->
        <span class="ant-breadcrumb-link"><!--!-->
            <!--!--> 修改 <!--!-->
        </span><!--!-->
<!--!-->
        <span class="ant-breadcrumb-separator"><!--!-->
            /<!--!-->
        </span><!--!-->
</li><!--!-->
        <!--!-->
    </ol><!--!-->
</nav><!--!-->

    <div class="ant-page-header-heading"><!--!-->
      <div class="ant-page-header-heading-left"><!--!-->
        
<!--!-->
        
        <!--!-->

        
          <span class="ant-page-header-heading-title"><!--!-->
编辑商品规格<!--!-->
            </span><!--!-->
<!--!-->
        
      </div><!--!-->

        <div class="ant-page-header-heading-extra"><!--!-->
          <!--!--><!--!--><!--!-->
    <div class="ant-space ant-space-horizontal ant-space-align-center" style=" " id="ant-blazor-ae6ebdad-7ecb-4b07-b821-f0f91df5c0ba" _bl_81a9fa2b-27ff-4a82-8ab9-7f69a45b8513=""><!--!-->
        <!--!--><!--!-->
<div class="ant-space-item" style=" " id="ant-blazor-e73be158-486d-4025-afef-3a2cff99ad89" _bl_9ebba442-878c-4a42-b21e-ae60f43b5e3f=""><!--!-->
    <!--!--><!--!--><!--!-->
    <div class="ant-btn-group" id="ant-blazor-d355de2a-0ba4-4844-b16b-db2f4a639423" _bl_518558e5-2ef3-4214-bb74-d968c6a8eadc=""><!--!-->
        <!--!--><!--!--><!--!-->
    <button class="ant-btn ant-btn-primary" id="ant-blazor-cca6f5c4-fceb-4b60-ad6d-355873273fc5" type="button" ant-click-animating-without-extra-node="false" _bl_efe6159b-92d7-4bc0-b9b8-e603393b7ab7=""><!--!-->
                <span><!--!-->保存后返回</span><!--!-->
    </button><!--!-->
<!--!-->
                    <!--!-->    <div class="" id="ant-blazor-8bcd6bb8-cb6b-43f5-b90e-cf68e6ecc804" style="display: inline-block; " tabindex="0" _bl_d32f76d7-065e-4f21-a25e-2f8bf83cc4d0=""><!--!-->
        <!--!--><!--!--><!--!-->
    <button class="ant-btn ant-btn-default ant-btn-icon-only" id="ant-blazor-700fd5f0-f273-490c-aafe-1ed9d62b5bf6" type="button" ant-click-animating-without-extra-node="false" _bl_3e941b96-f5c4-4572-84d0-26ff8aec06bd=""><!--!-->
            <!--!--><span class="anticon anticon-save" id="ant-blazor-68b6f74b-ba6e-4a91-a7dc-d4193705d8ae" role="img" _bl_4ae3a0a5-4763-4d7f-80d9-6fb11bf96e3f=""><!--!-->
<!--!--><svg focusable="false" width="1em" height="1em" fill="currentColor" style="pointer-events: none;" xmlns="http://www.w3.org/2000/svg" class="icon" viewBox="0 0 1024 1024"><path d="M893.3 293.3L730.7 130.7c-7.5-7.5-16.7-13-26.7-16V112H144c-17.7 0-32 14.3-32 32v736c0 17.7 14.3 32 32 32h736c17.7 0 32-14.3 32-32V338.5c0-17-6.7-33.2-18.7-45.2zM384 184h256v104H384V184zm456 656H184V184h136v136c0 17.7 14.3 32 32 32h320c17.7 0 32-14.3 32-32V205.8l136 136V840zM512 442c-79.5 0-144 64.5-144 144s64.5 144 144 144 144-64.5 144-144-64.5-144-144-144zm0 224c-44.2 0-80-35.8-80-80s35.8-80 80-80 80 35.8 80 80-35.8 80-80 80z"></path></svg><!--!-->
</span><!--!-->
    </button><!--!-->
<!--!-->
    </div><!--!-->
<!--!-->
<!--!--><!--!-->
    <!--!--><!--!-->
        <!--!--><!--!-->
    <!--!-->
<!--!-->
                    <!--!-->    <div class="" id="ant-blazor-8b326f37-e916-45b6-b0ae-30cfe16f37e8" style="display: inline-block; " tabindex="0" _bl_0c2b76eb-39b2-4bfe-a604-cbfc08064e2d=""><!--!-->
        <!--!--><!--!--><!--!-->
    <button class="ant-btn ant-btn-default ant-btn-icon-only" id="ant-blazor-57387cf9-e5fc-45d4-a552-86586869ce79" type="button" ant-click-animating-without-extra-node="false" _bl_4d6141c5-25fd-4ba8-abbb-4145ea0c4416=""><!--!-->
            <!--!--><span class="anticon anticon-undo" id="ant-blazor-1eea7b8f-7cd9-4de0-84ef-338f343628e1" role="img" _bl_d244cac9-55f1-4a8a-957d-1a6fd798b5f1=""><!--!-->
<!--!--><svg focusable="false" width="1em" height="1em" fill="currentColor" style="pointer-events: none;" xmlns="http://www.w3.org/2000/svg" class="icon" viewBox="0 0 1024 1024"><path d="M511.4 124C290.5 124.3 112 303 112 523.9c0 128 60.2 242 153.8 315.2l-37.5 48c-4.1 5.3-.3 13 6.3 12.9l167-.8c5.2 0 9-4.9 7.7-9.9L369.8 727a8 8 0 0 0-14.1-3L315 776.1c-10.2-8-20-16.7-29.3-26a318.64 318.64 0 0 1-68.6-101.7C200.4 609 192 567.1 192 523.9s8.4-85.1 25.1-124.5c16.1-38.1 39.2-72.3 68.6-101.7 29.4-29.4 63.6-52.5 101.7-68.6C426.9 212.4 468.8 204 512 204s85.1 8.4 124.5 25.1c38.1 16.1 72.3 39.2 101.7 68.6 29.4 29.4 52.5 63.6 68.6 101.7 16.7 39.4 25.1 81.3 25.1 124.5s-8.4 85.1-25.1 124.5a318.64 318.64 0 0 1-68.6 101.7c-7.5 7.5-15.3 14.5-23.4 21.2a7.93 7.93 0 0 0-1.2 11.1l39.4 50.5c2.8 3.5 7.9 4.1 11.4 1.3C854.5 760.8 912 649.1 912 523.9c0-221.1-179.4-400.2-400.6-399.9z"></path></svg><!--!-->
</span><!--!-->
    </button><!--!-->
<!--!-->
    </div><!--!-->
<!--!-->
<!--!--><!--!-->
    <!--!--><!--!-->
        <!--!--><!--!-->
    <!--!-->
<!--!-->
                    <!--!-->    <div class="" id="ant-blazor-a1e33d6f-c506-44bf-bc8d-d106a1775bf3" style="display: inline-block; " tabindex="0" _bl_aef89db9-6280-4802-815e-8190b44097fb=""><!--!-->
        <!--!--><!--!--><!--!-->
    <button class="ant-btn ant-btn-default ant-btn-icon-only" id="ant-blazor-ba1d760d-2479-4f78-af92-342243699999" type="button" ant-click-animating-without-extra-node="false" _bl_1b8d1e7a-cb21-4ecd-a8c5-c6928dde5d93=""><!--!-->
            <!--!--><span class="anticon anticon-arrow-left" id="ant-blazor-dd582381-8064-4c75-bcaf-60687132b5dd" role="img" _bl_e69ac010-69c7-4875-851b-c17b07644ed3=""><!--!-->
<!--!--><svg focusable="false" width="1em" height="1em" fill="currentColor" style="pointer-events: none;" xmlns="http://www.w3.org/2000/svg" class="icon" viewBox="0 0 1024 1024"><path d="M872 474H286.9l350.2-304c5.6-4.9 2.2-14-5.2-14h-88.5c-3.9 0-7.6 1.4-10.5 3.9L155 487.8a31.96 31.96 0 0 0 0 48.3L535.1 866c1.5 1.3 3.3 2 5.2 2h91.5c7.4 0 10.8-9.2 5.2-14L286.9 550H872c4.4 0 8-3.6 8-8v-60c0-4.4-3.6-8-8-8z"></path></svg><!--!-->
</span><!--!-->
    </button><!--!-->
<!--!-->
    </div><!--!-->
<!--!-->
<!--!--><!--!-->
    <!--!--><!--!-->
        <!--!--><!--!-->
    <!--!-->
<!--!-->
    </div><!--!-->
<!--!-->
</div><!--!-->
    </div><!--!-->
<!--!-->
        </div><!--!-->
    </div><!--!-->
      <div class="ant-page-header-content"><!--!-->
        <!--!-->
                    <div class="ant-pro-page-container-detail"><!--!-->
                        <div class="ant-pro-page-container-main"><!--!-->
                            <div class="ant-pro-page-container-row"><!--!-->
                            </div><!--!-->
                        </div><!--!-->
                    </div><!--!-->
                <!--!-->
      </div><!--!-->
<!--!-->
<!--!-->
  </div><!--!-->

<!--!-->
    </div><!--!-->
    <!--!--><div class="ant-pro-grid-content" id="ant-blazor-2586b2aa-4a82-404a-958d-88859045a26c"><!--!-->
    <div class="ant-pro-grid-content-children"><!--!-->
        <!--!-->
            <div class="ant-pro-page-container-children-content"><!--!-->
                <!--!--><!--!--><!--!-->
    <div class="ant-card ant-card-bordered" id="ant-blazor-76c5e88b-0dfd-4c91-b4ca-f8342aabd157" _bl_04e755c4-8d84-4e1e-88d2-8a43545e39a8=""><!--!-->
<!--!-->
<!--!-->
            <div class="ant-card-body"><!--!-->
<!--!--><!--!--><!--!--><!--!--><!--!-->    <div class="ant-spin-nested-loading" _bl_83ffea19-4f46-4713-9fd6-d3c76d30e3ab=""><!--!-->
        <div><!--!-->
            <!--!--><!--!-->
<!--!-->
                <div class="ant-spin-container "><!--!-->
                    <!--!-->
    <!--!--><form class="ant-form ant-form-vertical" id="ant-blazor-4fa308a9-3b3d-4c8a-bfd2-3b71c187619b" method="get" autocomplete="off"><!--!--><!--!--><input type="hidden" name="__RequestVerificationToken" value="CfDJ8OMff9ORegtMkqdcIA920BdsDQbE6wOWqx4KxOzgtF-Ch5ti8FmWVR51TUNcLCy0ox1M7EcZh8p6cTrtkAUStvebXkOfB1HpxDf4TSaKjyCcr5EAgGBHP1pItfoQJxTTEe5IItSR_Z4ohDPbwAIR4r4"><!--!--><!--!-->
<!--!-->        <!--!--><!--!-->
<!--!--><div class="layout-row" b-3tqgjllr0h=""><div class="layout-col" style="flex:0.25" b-3tqgjllr0h=""><!--!--><!--!--><!--!--><div class="test ant-form-item" id="ant-blazor-b61caff1-302c-4074-bab3-1266198b63ed"><!--!-->
    <!--!--><!--!--><!--!-->
    <div class="ant-form-item-row ant-row" style="row-gap: 0px;  " id="ant-blazor-2bb1576b-17af-4b45-b53f-b3cd07ef60cb" _bl_2f3054a5-7c0c-450e-b380-44d0d821023c=""><!--!-->
        <!--!-->
            <!--!--><div class="ant-form-item-label ant-col" style="  " id="ant-blazor-7edb50c3-163a-41b1-9fe7-5a70d16928fa" _bl_02af99b1-bc9e-4d97-80ff-e3c9830df89d=""><!--!-->
    <!--!-->
<div class="form-item-head" b-9mwaqr7zgv=""><label class="form-item-label-required" b-9mwaqr7zgv="">ID</label></div>            <!--!-->
</div><!--!-->
<!--!-->
        <!--!--><div class="ant-form-item-control ant-col" style="  " id="ant-blazor-e4850505-b4c6-4165-8396-ec21767258cb" _bl_47977c1d-297f-4040-b8e5-d484382c9b1c=""><!--!-->
    <!--!-->
            <div class="ant-form-item-control-input"><!--!-->
                <div class="ant-form-item-control-input-content"><!--!-->
                    <!--!--><!--!-->
                        247249593509618740<!--!-->
                    <!--!-->
                </div><!--!-->
            </div><!--!-->

        <!--!-->
</div><!--!-->
    <!--!-->
    </div><!--!-->
<!--!-->
</div></div><div class="layout-col" style="flex:0.25" b-3tqgjllr0h=""><!--!--><!--!--><!--!--><div class="test ant-form-item" id="ant-blazor-b63df0db-7cd1-4511-867a-caeeb42b1247"><!--!-->
    <!--!--><!--!--><!--!-->
    <div class="ant-form-item-row ant-row" style="row-gap: 0px;  " id="ant-blazor-6b2aac88-85fe-473f-9e08-df0b1d9efcd8" _bl_1ea4f8cf-8463-4bb3-9b0b-65f66ba3e4b0=""><!--!-->
        <!--!-->
            <!--!--><div class="ant-form-item-label ant-col" style="  " id="ant-blazor-9cdff9d8-4aa1-402c-a9ed-093898bb1eeb" _bl_29f6f3cf-9583-4979-93f8-dce1fdce2b46=""><!--!-->
    <!--!-->
<div class="form-item-head" b-9mwaqr7zgv=""><label class="form-item-label-required" b-9mwaqr7zgv="">创建时间</label></div>            <!--!-->
</div><!--!-->
<!--!-->
        <!--!--><div class="ant-form-item-control ant-col" style="  " id="ant-blazor-ae94aff6-62ac-4bee-888c-28e2f81787f6" _bl_8ee0b406-2a26-4dd7-a536-82b1bfabc4c3=""><!--!-->
    <!--!-->
            <div class="ant-form-item-control-input"><!--!-->
                <div class="ant-form-item-control-input-content"><!--!-->
                    <!--!--><!--!-->
                        11:27:21<!--!-->
                    <!--!-->
                </div><!--!-->
            </div><!--!-->

        <!--!-->
</div><!--!-->
    <!--!-->
    </div><!--!-->
<!--!-->
</div></div><div class="layout-col" style="flex:0.25" b-3tqgjllr0h=""><!--!--><!--!--><!--!--><div class="test ant-form-item" id="ant-blazor-ad25245f-92c0-4a29-ad6d-e9f51a176538"><!--!-->
    <!--!--><!--!--><!--!-->
    <div class="ant-form-item-row ant-row" style="row-gap: 0px;  " id="ant-blazor-5a727548-ab3a-42b0-a8bc-98b2fb56569d" _bl_a96bc748-15a2-48dc-a74d-08301d2544ba=""><!--!-->
        <!--!-->
            <!--!--><div class="ant-form-item-label ant-col" style="  " id="ant-blazor-cc17e749-b56f-4af2-8dd6-5b60c8be12fb" _bl_39493d57-aa75-40d0-bcaf-fb4017914560=""><!--!-->
    <!--!-->
<div class="form-item-head" b-9mwaqr7zgv=""><label class="form-item-label-required" b-9mwaqr7zgv="">修改时间</label></div>            <!--!-->
</div><!--!-->
<!--!-->
        <!--!--><div class="ant-form-item-control ant-col" style="  " id="ant-blazor-d68488b1-af4b-4487-9bf6-9fb5b084ab59" _bl_76ab0aef-12c7-4715-9f89-3bdf751beba9=""><!--!-->
    <!--!-->
            <div class="ant-form-item-control-input"><!--!-->
                <div class="ant-form-item-control-input-content"><!--!-->
                    <!--!--><!--!-->
                        11:35:13<!--!-->
                    <!--!-->
                </div><!--!-->
            </div><!--!-->

        <!--!-->
</div><!--!-->
    <!--!-->
    </div><!--!-->
<!--!-->
</div></div><div class="layout-col" style="flex:0.25" b-3tqgjllr0h=""><!--!--><!--!--><!--!--><div class="test ant-form-item" id="ant-blazor-f4c99cf2-f93b-41e8-953a-bd98aebf9b10"><!--!-->
    <!--!--><!--!--><!--!-->
    <div class="ant-form-item-row ant-row" style="row-gap: 0px;  " id="ant-blazor-d7052f13-0495-4514-b9bb-278407274ed1" _bl_81b9028a-e0ad-4830-9b7b-03fbda714117=""><!--!-->
        <!--!-->
            <!--!--><div class="ant-form-item-label ant-col" style="  " id="ant-blazor-e765cfa7-4cd6-4ac8-91f6-7fb09f23202b" _bl_3d60c5c1-259c-4345-89b2-7a910157bc4b=""><!--!-->
    <!--!-->
<div class="form-item-head" b-9mwaqr7zgv=""><label class="form-item-label-required" b-9mwaqr7zgv="">对象状态</label></div>            <!--!-->
</div><!--!-->
<!--!-->
        <!--!--><div class="ant-form-item-control ant-col" style="  " id="ant-blazor-9b1e8716-3852-4ac4-b662-4e090b942a71" _bl_74af9671-eff3-41a3-ae4e-b3223b6abcfa=""><!--!-->
    <!--!-->
            <div class="ant-form-item-control-input"><!--!-->
                <div class="ant-form-item-control-input-content"><!--!-->
                    <!--!--><!--!-->
                        <!--!--><!--!--><!--!-->
    <!--!--><!--!-->
        <!--!--><!--!-->
<!--!-->
<!--!-->
                    <!--!--><!--!-->
                        <!--!--><!--!-->
                            <!--!--><!--!-->
                                <!--!--><!--!-->
                                    <!--!--><!--!-->
                                        <!--!--><div class="ant-select ant-select-single ant-select-show-arrow ant-select-bordered ant-select-in-form-item " style="min-width:10em;" id="ant-blazor-1a39c4bb-00bb-428f-b5f8-75b6b7242d3c" tabindex="-1" _bl_cb7a3e61-b42c-4f85-845f-32f17446775c=""><!--!-->
      <div class="ant-select-selector" style=""><!--!-->
        <span class="ant-select-selection-search" style=""><!--!-->
          <input id="ant-blazor-1a39c4bb-00bb-428f-b5f8-75b6b7242d3c_list" type="search" readonly="" unselectable="on" role="combobox" class="ant-select-selection-search-input" autocomplete="off" aria-owns="ant-blazor-1a39c4bb-00bb-428f-b5f8-75b6b7242d3c_list" aria-autocomplete="list" aria-controls="ant-blazor-1a39c4bb-00bb-428f-b5f8-75b6b7242d3c_list" aria-haspopup="listbox" style="opacity: 0;" _bl_12fe6017-749f-47d8-85c2-bd677e266992=""><!--!-->
        </span><!--!-->
              <span class="ant-select-selection-item" title="有效"><!--!-->
                有效<!--!-->
              </span><!--!-->
      </div><!--!-->
        <!--!-->        <span class="ant-select-arrow" unselectable="on" aria-hidden="true" style="user-select: none;"><!--!-->
                <!--!--><span class="anticon anticon-down" id="ant-blazor-45ce96f1-3b18-4bfd-a058-ea5c9f4b5d9c" role="img" _bl_06455775-71be-40d7-afe5-02680f3e0741=""><!--!-->
<!--!--><svg focusable="false" width="1em" height="1em" fill="currentColor" style="pointer-events: none;" xmlns="http://www.w3.org/2000/svg" class="icon" viewBox="0 0 1024 1024"><path d="M884 256h-75c-5.1 0-9.9 2.5-12.9 6.6L512 654.2 227.9 262.6c-3-4.1-7.8-6.6-12.9-6.6h-75c-6.5 0-10.3 7.4-6.5 12.7l352.6 486.1c12.8 17.6 39 17.6 51.7 0l352.6-486.1c3.9-5.3.1-12.7-6.4-12.7z"></path></svg><!--!-->
</span><!--!-->
<!--!-->
        </span><!--!-->
<!--!-->
</div><!--!-->
                                    <!--!-->
                                <!--!-->
                            <!--!-->
                        <!--!-->
                    <!--!-->
                <!--!-->
<!--!--><!--!-->
    <!--!-->    <div class="ant-select-dropdown    ant-select-dropdown-placement-bottomLeft ant-select-dropdown-hidden" style="position: absolute;   display: none;" _bl_479f9bdb-8c6f-4dd7-a115-f8285bed8d61=""><!--!-->

        <!--!--><!--!-->
            <!--!--><!--!-->
<!--!-->
                    <div style="min-width: 297.86px;width: 297.86px;"><!--!-->
<div class="" style="max-height: 256px; overflow-y: auto;" _bl_3219d812-5a4a-4610-bd3b-1e0edea11c7c=""><!--!-->
                <div><!--!-->
                    <div class="" role="listbox" style="display: flex; flex-direction: column;"><!--!-->
                        <!--!--><div class="ant-select-item ant-select-item-option ant-select-item-option-selected ant-select-item-option-active" role="option" aria-label="有效" _bl_a3c84dd7-ba7b-4327-ac4d-0df3684081fa="" aria-selected=""><!--!-->
    <div class="ant-select-item-option-content"><!--!-->
有效    </div><!--!-->
            <span class="ant-select-item-option-state" unselectable="on" aria-hidden="true" style="user-select: none;"></span><!--!-->
</div><!--!--><div class="ant-select-item ant-select-item-option" role="option" aria-label="无效" _bl_1eb1cedb-1a96-4e8c-aff9-75ad8c673ab9=""><!--!-->
    <div class="ant-select-item-option-content"><!--!-->
无效    </div><!--!-->
            <span class="ant-select-item-option-state" unselectable="on" aria-hidden="true" style="user-select: none;"></span><!--!-->
</div><!--!--><div class="ant-select-item ant-select-item-option" role="option" aria-label="隐藏" _bl_22f13d0a-7989-444a-8550-c64eb9b39c10=""><!--!-->
    <div class="ant-select-item-option-content"><!--!-->
隐藏    </div><!--!-->
            <span class="ant-select-item-option-state" unselectable="on" aria-hidden="true" style="user-select: none;"></span><!--!-->
</div><!--!-->
                    </div><!--!-->
                </div><!--!-->
            </div><!--!-->
                    </div><!--!-->                    
                            <!--!-->
        <!--!-->
    </div><!--!-->
<!--!-->
<!--!-->
    <!--!-->
<!--!-->
                    <!--!-->
                </div><!--!-->
            </div><!--!-->

        <!--!-->
</div><!--!-->
    <!--!-->
    </div><!--!-->
<!--!-->
</div></div></div><!--!--><!--!--><!--!--><div class="test ant-form-item" id="ant-blazor-6d3f484b-b9e0-410c-89ac-382b4c61f7b7"><!--!-->
    <!--!--><!--!--><!--!-->
    <div class="ant-form-item-row ant-row" style="row-gap: 0px;  " id="ant-blazor-1849ea81-1e02-4aa8-8a13-5f2341ab57b7" _bl_a17dc96c-6b55-4f19-9f2a-4b76bfacb6e5=""><!--!-->
        <!--!-->
            <!--!--><div class="ant-form-item-label ant-col" style="  " id="ant-blazor-8a305076-d3e4-406d-b82b-6c56460aab78" _bl_776bb37b-cd5c-48bd-b846-54ad9b8b46a5=""><!--!-->
    <!--!-->
<div class="form-item-head" b-9mwaqr7zgv=""><label class="form-item-label-required" b-9mwaqr7zgv="">名称</label></div>            <!--!-->
</div><!--!-->
<!--!-->
        <!--!--><div class="ant-form-item-control ant-col" style="  " id="ant-blazor-e05b98e1-5c16-4c9a-a28e-bfbb2ab96f1d" _bl_331dadb6-5c61-4f40-9b7b-10c579d7ff1f=""><!--!-->
    <!--!-->
            <div class="ant-form-item-control-input"><!--!-->
                <div class="ant-form-item-control-input-content"><!--!-->
                    <!--!--><!--!-->
                        <!--!--><input class="ant-input" style=" " id="ant-blazor-26a2867a-f9d3-4afb-b526-b75f2069a226" type="text" placeholder="请输入名称" aria-required="" _bl_3da819df-8fdf-497e-b9f9-263ed44a7f1c=""><!--!-->
                    <!--!-->
                </div><!--!-->
            </div><!--!-->

        <!--!-->
</div><!--!-->
    <!--!-->
    </div><!--!-->
<!--!-->
</div><!--!--><!--!--><!--!--><div class="test ant-form-item" id="ant-blazor-ecab5be4-b2e1-4c85-b1a0-f02431d8b690"><!--!-->
    <!--!--><!--!--><!--!-->
    <div class="ant-form-item-row ant-row" style="row-gap: 0px;  " id="ant-blazor-4950c63f-65a9-4251-ad2b-22226961c5d7" _bl_3e56359e-fede-42e5-8945-1c2a163f044f=""><!--!-->
        <!--!-->
            <!--!--><div class="ant-form-item-label ant-col" style="  " id="ant-blazor-841a8c35-ea3e-4c37-ac27-5b5f2363cb19" _bl_ea8fc7b6-1e41-4e2d-92fc-a085cdf5085a=""><!--!-->
    <!--!-->
<div class="form-item-head" b-9mwaqr7zgv=""><label class="form-item-label" b-9mwaqr7zgv="">服务区</label></div>            <!--!-->
</div><!--!-->
<!--!-->
        <!--!--><div class="ant-form-item-control ant-col" style="  " id="ant-blazor-7171c656-5109-4f47-8c5f-788173b68e09" _bl_5b44e16a-ab08-42af-8231-eef5136555ab=""><!--!-->
    <!--!-->
            <div class="ant-form-item-control-input"><!--!-->
                <div class="ant-form-item-control-input-content"><!--!-->
                    <!--!--><!--!-->
                        <!--!--><span class="ant-input-group-wrapper ant-input-search ant-input-group-wrapper ant-input-search-enter-button" style=" "><span class="ant-input-wrapper ant-input-group"><span class=" ant-input-affix-wrapper   ant-input-affix-wrapper-input-with-clear-btn ant-input-search"><input class="ant-input" style=" " id="ant-blazor-c26d2ce6-f129-4bf9-84c6-b37c8787a409" type="text" placeholder="请输入服务区" readonly="" _bl_13689610-5eb0-4256-9a1c-3e60cce836fa=""><span class="ant-input-suffix"><span class="ant-input-clear-icon "><!--!--><span class="anticon anticon-close-circle" id="ant-blazor-27773079-a0c2-4710-a735-581b4566b8e9" role="img" _bl_66fbf0ac-7238-47a9-a8f3-ee67e9b23c9f=""><!--!-->
<!--!--><svg focusable="false" width="1em" height="1em" fill="currentColor" style="pointer-events: none;" xmlns="http://www.w3.org/2000/svg" class="icon" viewBox="0 0 1024 1024"><path d="M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm165.4 618.2l-66-.3L512 563.4l-99.3 118.4-66.1.3c-4.4 0-8-3.5-8-8 0-1.9.7-3.7 1.9-5.2l130.1-155L340.5 359a8.32 8.32 0 0 1-1.9-5.2c0-4.4 3.6-8 8-8l66.1.3L512 464.6l99.3-118.4 66-.3c4.4 0 8 3.5 8 8 0 1.9-.7 3.7-1.9 5.2L553.5 514l130 155c1.2 1.5 1.9 3.3 1.9 5.2 0 4.4-3.6 8-8 8z"></path></svg><!--!-->
</span></span></span></span><span class="ant-input-group-addon"><!--!--><!--!--><!--!-->
    <button class="ant-input-search-button ant-btn ant-btn-default ant-btn-icon-only" id="ant-blazor-e48d173c-314e-4f08-8c30-65a46eb5baf6" type="button" ant-click-animating-without-extra-node="false" _bl_7fd7f428-3365-43d8-98fe-f260ceb04e41=""><!--!-->
            <!--!--><span class="anticon anticon-search" id="ant-blazor-ff2b5eae-21d2-42d1-91f7-941dfbfddefd" role="img" _bl_03511c9c-63bd-46b1-b430-70c017a6bd52=""><!--!-->
<!--!--><svg focusable="false" width="1em" height="1em" fill="currentColor" style="pointer-events: none;" xmlns="http://www.w3.org/2000/svg" class="icon" viewBox="0 0 1024 1024"><path d="M909.6 854.5L649.9 594.8C690.2 542.7 712 479 712 412c0-80.2-31.3-155.4-87.9-212.1-56.6-56.7-132-87.9-212.1-87.9s-155.5 31.3-212.1 87.9C143.2 256.5 112 331.8 112 412c0 80.1 31.3 155.5 87.9 212.1C256.5 680.8 331.8 712 412 712c67 0 130.6-21.8 182.7-62l259.7 259.6a8.2 8.2 0 0 0 11.6 0l43.6-43.5a8.2 8.2 0 0 0 0-11.6zM570.4 570.4C528 612.7 471.8 636 412 636s-116-23.3-158.4-65.6C211.3 528 188 471.8 188 412s23.3-116.1 65.6-158.4C296 211.3 352.2 188 412 188s116.1 23.2 158.4 65.6S636 352.2 636 412s-23.3 116.1-65.6 158.4z"></path></svg><!--!-->
</span><!--!-->
    </button><!--!-->
</span></span></span><!--!-->
                    <!--!-->
                </div><!--!-->
            </div><!--!-->

        <!--!-->
</div><!--!-->
    <!--!-->
    </div><!--!-->
<!--!-->
</div><!--!--><!--!--><!--!--><div class="test ant-form-item" id="ant-blazor-4225b772-6ab8-4d0c-9977-c4a70727965f"><!--!-->
    <!--!--><!--!--><!--!-->
    <div class="ant-form-item-row ant-row" style="row-gap: 0px;  " id="ant-blazor-f7e049a1-5a4e-4694-8d73-f7fc8c9ad0dc" _bl_a827a143-9de5-426e-89b7-9122847cb2f6=""><!--!-->
        <!--!-->
            <!--!--><div class="ant-form-item-label ant-col" style="  " id="ant-blazor-ff12fa85-8591-4d41-bc25-4a953afc6b6b" _bl_3a3d21f8-fa7f-4a19-899d-727e3ffa6d4d=""><!--!-->
    <!--!-->
<div class="form-item-head" b-9mwaqr7zgv=""><label class="form-item-label-required" b-9mwaqr7zgv="">商品</label></div>            <!--!-->
</div><!--!-->
<!--!-->
        <!--!--><div class="ant-form-item-control ant-col" style="  " id="ant-blazor-41194b5a-3d4c-4f1f-9151-24daa652b03c" _bl_6e142a70-c2cd-4837-ae47-c4ca5b23c5e1=""><!--!-->
    <!--!-->
            <div class="ant-form-item-control-input"><!--!-->
                <div class="ant-form-item-control-input-content"><!--!-->
                    <!--!--><!--!-->
                        <!--!--><span class="ant-input-group-wrapper ant-input-search ant-input-group-wrapper ant-input-search-enter-button" style=" "><span class="ant-input-wrapper ant-input-group"><span class=" ant-input-affix-wrapper   ant-input-affix-wrapper-input-with-clear-btn ant-input-search"><input class="ant-input" style=" " id="ant-blazor-6d8142f1-1b6d-4658-8974-4601e5722746" type="text" placeholder="请输入商品" readonly="" aria-required="" _bl_4261d18e-0454-481f-9176-4f458aac90f4=""><span class="ant-input-suffix"><span class="ant-input-clear-icon "><!--!--><span class="anticon anticon-close-circle" id="ant-blazor-475d1289-f943-4958-89a8-6b1531f31d44" role="img" _bl_167a58c9-7a6a-47ba-8e0a-62998eb8d7ba=""><!--!-->
<!--!--><svg focusable="false" width="1em" height="1em" fill="currentColor" style="pointer-events: none;" xmlns="http://www.w3.org/2000/svg" class="icon" viewBox="0 0 1024 1024"><path d="M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm165.4 618.2l-66-.3L512 563.4l-99.3 118.4-66.1.3c-4.4 0-8-3.5-8-8 0-1.9.7-3.7 1.9-5.2l130.1-155L340.5 359a8.32 8.32 0 0 1-1.9-5.2c0-4.4 3.6-8 8-8l66.1.3L512 464.6l99.3-118.4 66-.3c4.4 0 8 3.5 8 8 0 1.9-.7 3.7-1.9 5.2L553.5 514l130 155c1.2 1.5 1.9 3.3 1.9 5.2 0 4.4-3.6 8-8 8z"></path></svg><!--!-->
</span></span></span></span><span class="ant-input-group-addon"><!--!--><!--!--><!--!-->
    <button class="ant-input-search-button ant-btn ant-btn-default ant-btn-icon-only" id="ant-blazor-1b73835a-6a51-49c3-b6d9-d1544761d502" type="button" ant-click-animating-without-extra-node="false" _bl_06f8d48f-5ba0-46b6-8f24-aa4e15077aec=""><!--!-->
            <!--!--><span class="anticon anticon-search" id="ant-blazor-92e4222b-1e87-4a99-8884-13dffc0091e5" role="img" _bl_4c3b1f1d-31af-4277-ac4e-f627eeae285f=""><!--!-->
<!--!--><svg focusable="false" width="1em" height="1em" fill="currentColor" style="pointer-events: none;" xmlns="http://www.w3.org/2000/svg" class="icon" viewBox="0 0 1024 1024"><path d="M909.6 854.5L649.9 594.8C690.2 542.7 712 479 712 412c0-80.2-31.3-155.4-87.9-212.1-56.6-56.7-132-87.9-212.1-87.9s-155.5 31.3-212.1 87.9C143.2 256.5 112 331.8 112 412c0 80.1 31.3 155.5 87.9 212.1C256.5 680.8 331.8 712 412 712c67 0 130.6-21.8 182.7-62l259.7 259.6a8.2 8.2 0 0 0 11.6 0l43.6-43.5a8.2 8.2 0 0 0 0-11.6zM570.4 570.4C528 612.7 471.8 636 412 636s-116-23.3-158.4-65.6C211.3 528 188 471.8 188 412s23.3-116.1 65.6-158.4C296 211.3 352.2 188 412 188s116.1 23.2 158.4 65.6S636 352.2 636 412s-23.3 116.1-65.6 158.4z"></path></svg><!--!-->
</span><!--!-->
    </button><!--!-->
</span></span></span><!--!-->
                    <!--!-->
                </div><!--!-->
            </div><!--!-->

        <!--!-->
</div><!--!-->
    <!--!-->
    </div><!--!-->
<!--!-->
</div><!--!--><!--!--><!--!--><div class="test ant-form-item" id="ant-blazor-e5458e70-f176-4455-b652-144f81ca188f"><!--!-->
    <!--!--><!--!--><!--!-->
    <div class="ant-form-item-row ant-row" style="row-gap: 0px;  " id="ant-blazor-6ec491e5-bd97-4b42-aaed-3cce7119e57b" _bl_90154d91-60b4-4c78-8206-d1ac2c256b6b=""><!--!-->
        <!--!-->
            <!--!--><div class="ant-form-item-label ant-col" style="  " id="ant-blazor-4b2c6b0e-1f30-4f5c-a627-7b767f01dc0a" _bl_d9330ecb-c7fb-4bee-b4e1-fd53a9de1815=""><!--!-->
    <!--!-->
<div class="form-item-head" b-9mwaqr7zgv=""><label class="form-item-label-required" b-9mwaqr7zgv="">禁用</label></div>            <!--!-->
</div><!--!-->
<!--!-->
        <!--!--><div class="ant-form-item-control ant-col" style="  " id="ant-blazor-a4abda88-ab70-4ca3-a558-bae31df3cdf9" _bl_c5c355de-acbc-4932-a396-3bdb328d1d3a=""><!--!-->
    <!--!-->
            <div class="ant-form-item-control-input"><!--!-->
                <div class="ant-form-item-control-input-content"><!--!-->
                    <!--!--><!--!-->
                        <!--!--><label class="ant-checkbox-wrapper" for="ant-blazor-19eb853a-ba2d-4395-bc9b-01da0e6f84c2" _bl_cfda48fb-0623-4669-a6af-632677e7f86e=""><!--!-->
    <span class="ant-checkbox"><!--!-->
        <input id="ant-blazor-19eb853a-ba2d-4395-bc9b-01da0e6f84c2" type="checkbox" value="true" class="ant-checkbox-input"><!--!-->
        <span class="ant-checkbox-inner"></span>
    </span><!--!-->

        <span></span><!--!-->
<!--!-->
</label><!--!-->
                    <!--!-->
                </div><!--!-->
            </div><!--!-->

        <!--!-->
</div><!--!-->
    <!--!-->
    </div><!--!-->
<!--!-->
</div><!--!--><!--!--><!--!--><div class="test ant-form-item" id="ant-blazor-2b58fd78-7a7a-466e-9076-3fce74b3ad1d"><!--!-->
    <!--!--><!--!--><!--!-->
    <div class="ant-form-item-row ant-row" style="row-gap: 0px;  " id="ant-blazor-3acbe768-c08f-4b53-b421-30b84092d189" _bl_3c08fe98-a81f-4713-8e06-6e5484e596b2=""><!--!-->
        <!--!-->
            <!--!--><div class="ant-form-item-label ant-col" style="  " id="ant-blazor-36f9e1bc-a54d-426c-b3e3-122cc984b2ef" _bl_043041f2-0e85-4180-ab45-d352762878b4=""><!--!-->
    <!--!-->
<div class="form-item-head" b-9mwaqr7zgv=""><label class="form-item-label-required" b-9mwaqr7zgv="">原价</label></div>            <!--!-->
</div><!--!-->
<!--!-->
        <!--!--><div class="ant-form-item-control ant-col" style="  " id="ant-blazor-1fa314fe-f57e-42c0-b354-f9395aeb8b2e" _bl_2ad45132-8c25-4516-9051-f14d2f1cac21=""><!--!-->
    <!--!-->
            <div class="ant-form-item-control-input"><!--!-->
                <div class="ant-form-item-control-input-content"><!--!-->
                    <!--!--><!--!-->
                        <!--!--><div class="ant-input-number" style=" " id="ant-blazor-0b097af3-b225-47c9-ad23-8618c8dc31a1"><!--!-->
        <div class="ant-input-number-handler-wrap"><!--!-->
            <span unselectable="unselectable" role="button" aria-label="Increase Value" class="ant-input-number-handler ant-input-number-handler-up "><!--!-->
                <!--!--><span class="ant-input-number-handler-up-inner anticon anticon-up" id="ant-blazor-5a00867b-32c8-41f9-a8f4-40d488b54087" role="img" _bl_b0954c49-836d-41cf-b047-56c9183ffb98=""><!--!-->
<!--!--><svg focusable="false" width="1em" height="1em" fill="currentColor" style="pointer-events: none;" xmlns="http://www.w3.org/2000/svg" class="icon" viewBox="0 0 1024 1024"><path d="M890.5 755.3L537.9 269.2c-12.8-17.6-39-17.6-51.7 0L133.5 755.3A8 8 0 0 0 140 768h75c5.1 0 9.9-2.5 12.9-6.6L512 369.8l284.1 391.6c3 4.1 7.8 6.6 12.9 6.6h75c6.5 0 10.3-7.4 6.5-12.7z"></path></svg><!--!-->
</span><!--!-->
            </span><!--!-->
            <span unselectable="unselectable" role="button" aria-label="Decrease Value" class="ant-input-number-handler ant-input-number-handler-down "><!--!-->
                <!--!--><span class="ant-input-number-handler-down-inner anticon anticon-down" id="ant-blazor-1cda2891-911c-4c6a-8318-cb84d30ce09d" role="img" _bl_3260d0fa-117f-4ab8-a7f2-bc27bcb4c128=""><!--!-->
<!--!--><svg focusable="false" width="1em" height="1em" fill="currentColor" style="pointer-events: none;" xmlns="http://www.w3.org/2000/svg" class="icon" viewBox="0 0 1024 1024"><path d="M884 256h-75c-5.1 0-9.9 2.5-12.9 6.6L512 654.2 227.9 262.6c-3-4.1-7.8-6.6-12.9-6.6h-75c-6.5 0-10.3 7.4-6.5 12.7l352.6 486.1c12.8 17.6 39 17.6 51.7 0l352.6-486.1c3.9-5.3.1-12.7-6.4-12.7z"></path></svg><!--!-->
</span><!--!-->
            </span><!--!-->
        </div><!--!-->
        <div class="ant-input-number-input-wrap"><!--!-->
            <input id="ant-blazor-0b097af3-b225-47c9-ad23-8618c8dc31a1_input" role="spinbutton" aria-valuemin="-79228162514264337593543950335" aria-valuemax="79228162514264337593543950335" autocomplete="off" max="79228162514264337593543950335" min="-79228162514264337593543950335" step="1" inputmode="decimal" placeholder="请输入原价" aria-valuenow="109" class="ant-input-number-input" name="" _bl_85ce1eb3-b98f-4386-800b-2d841a0115e9=""><!--!-->
        </div><!--!-->
    </div><!--!-->
                    <!--!-->
                </div><!--!-->
            </div><!--!-->

        <!--!-->
</div><!--!-->
    <!--!-->
    </div><!--!-->
<!--!-->
</div><!--!--><!--!--><!--!--><div class="test ant-form-item" id="ant-blazor-73ae463f-8716-4bb2-87af-2608d633c0df"><!--!-->
    <!--!--><!--!--><!--!-->
    <div class="ant-form-item-row ant-row" style="row-gap: 0px;  " id="ant-blazor-b5dbdb4e-ef36-4869-93d4-f4153dac46f0" _bl_af365564-55ae-4c9b-82c0-cee0b782f1b6=""><!--!-->
        <!--!-->
            <!--!--><div class="ant-form-item-label ant-col" style="  " id="ant-blazor-3361c430-acf7-4971-8729-0c66f01f1259" _bl_471f0a82-d5e8-44f6-af73-9ddaaae7c453=""><!--!-->
    <!--!-->
<div class="form-item-head" b-9mwaqr7zgv=""><label class="form-item-label-required" b-9mwaqr7zgv="">售价</label></div>            <!--!-->
</div><!--!-->
<!--!-->
        <!--!--><div class="ant-form-item-control ant-col" style="  " id="ant-blazor-79cf48e4-581f-4fb5-b410-a51c5fa04293" _bl_ef68e88c-b0c2-445b-9de2-eb71ee7fa926=""><!--!-->
    <!--!-->
            <div class="ant-form-item-control-input"><!--!-->
                <div class="ant-form-item-control-input-content"><!--!-->
                    <!--!--><!--!-->
                        <!--!--><div class="ant-input-number" style=" " id="ant-blazor-0e76dbb9-91a7-4b7d-8c4e-0dcfb17d6375"><!--!-->
        <div class="ant-input-number-handler-wrap"><!--!-->
            <span unselectable="unselectable" role="button" aria-label="Increase Value" class="ant-input-number-handler ant-input-number-handler-up "><!--!-->
                <!--!--><span class="ant-input-number-handler-up-inner anticon anticon-up" id="ant-blazor-7f241c8b-18c4-406f-887e-f21f2872e46c" role="img" _bl_c3fad7bd-8c74-487d-8d0a-594cfae5c5f1=""><!--!-->
<!--!--><svg focusable="false" width="1em" height="1em" fill="currentColor" style="pointer-events: none;" xmlns="http://www.w3.org/2000/svg" class="icon" viewBox="0 0 1024 1024"><path d="M890.5 755.3L537.9 269.2c-12.8-17.6-39-17.6-51.7 0L133.5 755.3A8 8 0 0 0 140 768h75c5.1 0 9.9-2.5 12.9-6.6L512 369.8l284.1 391.6c3 4.1 7.8 6.6 12.9 6.6h75c6.5 0 10.3-7.4 6.5-12.7z"></path></svg><!--!-->
</span><!--!-->
            </span><!--!-->
            <span unselectable="unselectable" role="button" aria-label="Decrease Value" class="ant-input-number-handler ant-input-number-handler-down "><!--!-->
                <!--!--><span class="ant-input-number-handler-down-inner anticon anticon-down" id="ant-blazor-fd6e233d-e67c-4361-8643-de31c68a9aa3" role="img" _bl_111aff6c-2464-4d2e-b88b-67ced79f9277=""><!--!-->
<!--!--><svg focusable="false" width="1em" height="1em" fill="currentColor" style="pointer-events: none;" xmlns="http://www.w3.org/2000/svg" class="icon" viewBox="0 0 1024 1024"><path d="M884 256h-75c-5.1 0-9.9 2.5-12.9 6.6L512 654.2 227.9 262.6c-3-4.1-7.8-6.6-12.9-6.6h-75c-6.5 0-10.3 7.4-6.5 12.7l352.6 486.1c12.8 17.6 39 17.6 51.7 0l352.6-486.1c3.9-5.3.1-12.7-6.4-12.7z"></path></svg><!--!-->
</span><!--!-->
            </span><!--!-->
        </div><!--!-->
        <div class="ant-input-number-input-wrap"><!--!-->
            <input id="ant-blazor-0e76dbb9-91a7-4b7d-8c4e-0dcfb17d6375_input" role="spinbutton" aria-valuemin="-79228162514264337593543950335" aria-valuemax="79228162514264337593543950335" autocomplete="off" max="79228162514264337593543950335" min="-79228162514264337593543950335" step="1" inputmode="decimal" placeholder="请输入售价" aria-valuenow="0.01" class="ant-input-number-input" name="" _bl_5ccdb5cf-fb9e-422b-af88-e60c4195821e=""><!--!-->
        </div><!--!-->
    </div><!--!-->
                    <!--!-->
                </div><!--!-->
            </div><!--!-->

        <!--!-->
</div><!--!-->
    <!--!-->
    </div><!--!-->
<!--!-->
</div><!--!--><!--!--><!--!--><div class="test ant-form-item" id="ant-blazor-df9eb9eb-3dd7-4aa2-8aea-df979a975f64"><!--!-->
    <!--!--><!--!--><!--!-->
    <div class="ant-form-item-row ant-row" style="row-gap: 0px;  " id="ant-blazor-3882440d-8953-4185-b2c2-531bd1d240ab" _bl_d8aa3175-7274-4793-a75a-38e9caa6670c=""><!--!-->
        <!--!-->
            <!--!--><div class="ant-form-item-label ant-col" style="  " id="ant-blazor-9e24ae82-0d3d-4dcd-9440-ccce5cab09fd" _bl_8e69544a-e84e-4249-af72-47ceefb79949=""><!--!-->
    <!--!-->
<div class="form-item-head" b-9mwaqr7zgv=""><label class="form-item-label-collection" b-9mwaqr7zgv="">规格明细</label></div>            <!--!-->
</div><!--!-->
<!--!-->
        <!--!--><div class="ant-form-item-control ant-col" style="  " id="ant-blazor-1e6c1e31-ca87-4f2c-ac4e-90801ecaa10c" _bl_089fe8be-d2dc-4736-a640-931cb92bb5be=""><!--!-->
    <!--!-->
            <div class="ant-form-item-control-input"><!--!-->
                <div class="ant-form-item-control-input-content"><!--!-->
                    <!--!--><!--!-->
                        <!--!--><div class="ant-table-wrapper" _bl_fc62c173-fac1-4fd8-950e-4bc59da16b9b=""><!--!-->
    <!--!-->    <div class="ant-spin-nested-loading" _bl_0a65c345-1be8-4643-acff-3edfecbd050b=""><!--!-->
        <div><!--!-->
            <!--!--><!--!-->
<!--!-->
                <div class="ant-spin-container "><!--!-->
                    <!--!-->
<!--!-->
        <!--!--><!--!-->
            <!--!--><!--!-->
                <!--!--><!--!-->
                    <!--!--><!--!-->
<!--!--><!--!--><!--!--><!--!--><!--!--><!--!--><!--!--><!--!--><!--!--><!--!--><!--!-->
         
            <!--!-->                    <!--!-->
                    <div class="ant-table ant-table-small ant-table-has-fix-right" _bl_8ad4ecf5-afdb-435d-a976-4061bbe020b8=""><!--!-->
                        <div class="ant-table-container"><!--!-->
                                <div class="ant-table-content" style="" _bl_318ef976-b3f4-4df0-a05d-fe1c5653987f=""><!--!-->
                                    <table style="table-layout: fixed;;" _bl_0148887f-b95d-4f0f-bcf3-e9f05569895a=""><!--!-->
                                        <colgroup><!--!-->
                <!--!--><!--!-->
<!--!--><!--!-->        <col>
<!--!--><!--!-->        <col>
<!--!--><!--!-->        <col>
<!--!--><!--!-->        <col>
<!--!--><!--!-->        <col>
<!--!--><!--!-->        <col>
<!--!--><!--!-->        <col>
<!--!--><!--!-->        <col>
<!--!--><!--!-->        <col>
<!--!--><!--!-->        <col>
<!--!-->
         
            <!--!-->        <col style="width: 120px; min-width: 120px;"><!--!-->
                <!--!-->
            </colgroup><!--!-->
                                        <thead class="ant-table-thead"><!--!-->
            <!--!--><!--!-->
             <!--!--><!--!-->
                 <!--!--><!--!-->
                     <!--!--><!--!-->
                                    <!--!--><!--!-->
    <tr class=" "><!--!-->
        <!--!-->
                                        <!--!-->    <!--!--><!--!-->
        <th class="ant-table-cell ant-table-cell-ellipsis" style="text-align: center;  " title="产品"><!--!-->
<span class="ant-table-column-title"><!--!-->
产品    </span><!--!-->
        </th><!--!-->
    <!--!-->
<!--!-->    <!--!--><!--!-->
        <th class="ant-table-cell ant-table-cell-ellipsis" style=" " title="产品"><!--!-->
<span class="ant-table-column-title"><!--!-->
产品    </span><!--!-->
        </th><!--!-->
    <!--!-->
<!--!-->    <!--!--><!--!-->
        <th class="ant-table-cell ant-table-cell-ellipsis" style="text-align: center;  " title="商品规格"><!--!-->
<span class="ant-table-column-title"><!--!-->
商品规格    </span><!--!-->
        </th><!--!-->
    <!--!-->
<!--!-->    <!--!--><!--!-->
        <th class="ant-table-cell ant-table-cell-ellipsis" style="text-align: center;  " title="产品规格"><!--!-->
<span class="ant-table-column-title"><!--!-->
产品规格    </span><!--!-->
        </th><!--!-->
    <!--!-->
<!--!-->    <!--!--><!--!-->
        <th class="ant-table-cell ant-table-cell-ellipsis" style=" " title="产品规格"><!--!-->
<span class="ant-table-column-title"><!--!-->
产品规格    </span><!--!-->
        </th><!--!-->
    <!--!-->
<!--!-->    <!--!--><!--!-->
        <th class="ant-table-cell ant-table-cell-ellipsis" style="text-align: right;  " title="数量"><!--!-->
<span class="ant-table-column-title"><!--!-->
数量    </span><!--!-->
        </th><!--!-->
    <!--!-->
<!--!-->    <!--!--><!--!-->
        <th class="ant-table-cell ant-table-cell-ellipsis" style="text-align: right;  " title="授权天数"><!--!-->
<span class="ant-table-column-title"><!--!-->
授权天数    </span><!--!-->
        </th><!--!-->
    <!--!-->
<!--!-->    <!--!--><!--!-->
        <th class="ant-table-cell ant-table-cell-ellipsis" style="text-align: right;  " title="并发限制"><!--!-->
<span class="ant-table-column-title"><!--!-->
并发限制    </span><!--!-->
        </th><!--!-->
    <!--!-->
<!--!-->    <!--!--><!--!-->
        <th class="ant-table-cell ant-table-cell-ellipsis" style="text-align: right;  " title="价格"><!--!-->
<span class="ant-table-column-title"><!--!-->
价格    </span><!--!-->
        </th><!--!-->
    <!--!-->
<!--!-->    <!--!--><!--!-->
        <th class="ant-table-cell ant-table-cell-ellipsis" style="text-align: right;  " title="排位"><!--!-->
<span class="ant-table-column-title"><!--!-->
排位    </span><!--!-->
        </th><!--!-->
    <!--!-->
<!--!-->
         
            <!--!--><!--!-->	<th class="ant-table-cell ant-table-cell-fix-right ant-table-cell-fix-right-first" style="text-align: center; position: sticky; right: 0px;  " colspan="1"><!--!-->
操作<!--!-->	</th><!--!-->
<!--!-->
                                    <!--!-->
    </tr><!--!-->
<!--!-->
                        <!--!-->
                    <!--!-->
                <!--!-->
            <!--!-->
        </thead><!--!-->
                                        <tbody class="ant-table-tbody"><!--!-->
                                            <!--!--><!--!--><!--!-->
    <!--!--><!--!-->
                <!--!--><!--!-->
                    <!--!--><!--!-->
                        <!--!--><!--!-->
                                <!--!--><!--!-->
    <tr data-row-key="0" class="ant-table-row ant-table-row-level-0     "><!--!-->
        <!--!-->
                                    <!--!-->    <!--!--><!--!-->
<!--!-->
        <td class="ant-table-cell ant-table-cell-ellipsis" style="text-align: center;  " rowspan="1" colspan="1" title="" data-label="产品"><!--!-->
<!--!-->
<!--!--><!--!--><!--!--><span class="ant-input-group-wrapper ant-input-search ant-input-group-wrapper ant-input-search-enter-button" style=" "><span class="ant-input-wrapper ant-input-group"><span class=" ant-input-affix-wrapper   ant-input-affix-wrapper-input-with-clear-btn ant-input-search"><input class="ant-input" style=" " id="ant-blazor-897b57dd-eb1b-4dd0-95b8-d16e4e75db9e" type="text" placeholder="请输入产品" readonly="" _bl_2406ea28-0c0f-474e-a659-143cce0ab496=""><span class="ant-input-suffix"><span class="ant-input-clear-icon "><!--!--><span class="anticon anticon-close-circle" id="ant-blazor-7dd50a92-b307-4465-bb07-7229dfee9652" role="img" _bl_216f74b8-6664-4736-80bb-754b885f0ef2=""><!--!-->
<!--!--><svg focusable="false" width="1em" height="1em" fill="currentColor" style="pointer-events: none;" xmlns="http://www.w3.org/2000/svg" class="icon" viewBox="0 0 1024 1024"><path d="M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm165.4 618.2l-66-.3L512 563.4l-99.3 118.4-66.1.3c-4.4 0-8-3.5-8-8 0-1.9.7-3.7 1.9-5.2l130.1-155L340.5 359a8.32 8.32 0 0 1-1.9-5.2c0-4.4 3.6-8 8-8l66.1.3L512 464.6l99.3-118.4 66-.3c4.4 0 8 3.5 8 8 0 1.9-.7 3.7-1.9 5.2L553.5 514l130 155c1.2 1.5 1.9 3.3 1.9 5.2 0 4.4-3.6 8-8 8z"></path></svg><!--!-->
</span></span></span></span><span class="ant-input-group-addon"><!--!--><!--!--><!--!-->
    <button class="ant-input-search-button ant-btn ant-btn-default ant-btn-icon-only" id="ant-blazor-1882a7b6-b95d-4de6-bc38-f45786ca36c9" type="button" ant-click-animating-without-extra-node="false" _bl_e1f1c55a-4be9-485c-8189-e24bf7a0a5d9=""><!--!-->
            <!--!--><span class="anticon anticon-search" id="ant-blazor-f76cb7eb-e70e-41bc-896e-80a144c831df" role="img" _bl_c5b8838e-4062-4399-8705-da0161758c7d=""><!--!-->
<!--!--><svg focusable="false" width="1em" height="1em" fill="currentColor" style="pointer-events: none;" xmlns="http://www.w3.org/2000/svg" class="icon" viewBox="0 0 1024 1024"><path d="M909.6 854.5L649.9 594.8C690.2 542.7 712 479 712 412c0-80.2-31.3-155.4-87.9-212.1-56.6-56.7-132-87.9-212.1-87.9s-155.5 31.3-212.1 87.9C143.2 256.5 112 331.8 112 412c0 80.1 31.3 155.5 87.9 212.1C256.5 680.8 331.8 712 412 712c67 0 130.6-21.8 182.7-62l259.7 259.6a8.2 8.2 0 0 0 11.6 0l43.6-43.5a8.2 8.2 0 0 0 0-11.6zM570.4 570.4C528 612.7 471.8 636 412 636s-116-23.3-158.4-65.6C211.3 528 188 471.8 188 412s23.3-116.1 65.6-158.4C296 211.3 352.2 188 412 188s116.1 23.2 158.4 65.6S636 352.2 636 412s-23.3 116.1-65.6 158.4z"></path></svg><!--!-->
</span><!--!-->
    </button><!--!-->
</span></span></span>        </td><!--!-->
    <!--!-->
<!--!-->    <!--!--><!--!-->
<!--!-->
        <td class="ant-table-cell ant-table-cell-ellipsis" style=" " rowspan="1" colspan="1" title="" data-label="产品"><!--!-->
<!--!-->
<!--!--><!--!--><!--!--><input class="ant-input" style=" " id="ant-blazor-e87ec4c6-7475-4d1a-b212-fab23aee893b" type="text" placeholder="请输入产品" _bl_c17fb509-9229-4f2a-a22e-4d25ba97551d="">        </td><!--!-->
    <!--!-->
<!--!-->    <!--!--><!--!-->
<!--!-->
        <td class="ant-table-cell ant-table-cell-ellipsis" style="text-align: center;  " rowspan="1" colspan="1" title="" data-label="商品规格"><!--!-->
<!--!-->
<!--!--><!--!--><!--!--><span class="ant-input-group-wrapper ant-input-search ant-input-group-wrapper ant-input-search-enter-button" style=" "><span class="ant-input-wrapper ant-input-group"><span class=" ant-input-affix-wrapper   ant-input-affix-wrapper-input-with-clear-btn ant-input-search"><input class="ant-input" style=" " id="ant-blazor-e8cf71ba-26ec-4689-b6a6-84cc095ebb2a" type="text" placeholder="请输入商品规格" readonly="" _bl_0bc36f7b-fc54-4d9a-ac5a-898d05e2a951=""><span class="ant-input-suffix"><span class="ant-input-clear-icon "><!--!--><span class="anticon anticon-close-circle" id="ant-blazor-9b17207e-b2d1-472b-a7e5-5dca004724a3" role="img" _bl_7095abdf-4e17-47ae-b75a-683e5a4792dd=""><!--!-->
<!--!--><svg focusable="false" width="1em" height="1em" fill="currentColor" style="pointer-events: none;" xmlns="http://www.w3.org/2000/svg" class="icon" viewBox="0 0 1024 1024"><path d="M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm165.4 618.2l-66-.3L512 563.4l-99.3 118.4-66.1.3c-4.4 0-8-3.5-8-8 0-1.9.7-3.7 1.9-5.2l130.1-155L340.5 359a8.32 8.32 0 0 1-1.9-5.2c0-4.4 3.6-8 8-8l66.1.3L512 464.6l99.3-118.4 66-.3c4.4 0 8 3.5 8 8 0 1.9-.7 3.7-1.9 5.2L553.5 514l130 155c1.2 1.5 1.9 3.3 1.9 5.2 0 4.4-3.6 8-8 8z"></path></svg><!--!-->
</span></span></span></span><span class="ant-input-group-addon"><!--!--><!--!--><!--!-->
    <button class="ant-input-search-button ant-btn ant-btn-default ant-btn-icon-only" id="ant-blazor-9bc660ac-1136-4f5d-96e2-91e6419669a3" type="button" ant-click-animating-without-extra-node="false" _bl_3eab23b1-0bf0-49ab-b36b-1b3ac7736d56=""><!--!-->
            <!--!--><span class="anticon anticon-search" id="ant-blazor-264d14c4-4efd-4a57-8b08-15977f91dfee" role="img" _bl_6c20396b-6d83-4fdf-ab45-56b9c0719e1b=""><!--!-->
<!--!--><svg focusable="false" width="1em" height="1em" fill="currentColor" style="pointer-events: none;" xmlns="http://www.w3.org/2000/svg" class="icon" viewBox="0 0 1024 1024"><path d="M909.6 854.5L649.9 594.8C690.2 542.7 712 479 712 412c0-80.2-31.3-155.4-87.9-212.1-56.6-56.7-132-87.9-212.1-87.9s-155.5 31.3-212.1 87.9C143.2 256.5 112 331.8 112 412c0 80.1 31.3 155.5 87.9 212.1C256.5 680.8 331.8 712 412 712c67 0 130.6-21.8 182.7-62l259.7 259.6a8.2 8.2 0 0 0 11.6 0l43.6-43.5a8.2 8.2 0 0 0 0-11.6zM570.4 570.4C528 612.7 471.8 636 412 636s-116-23.3-158.4-65.6C211.3 528 188 471.8 188 412s23.3-116.1 65.6-158.4C296 211.3 352.2 188 412 188s116.1 23.2 158.4 65.6S636 352.2 636 412s-23.3 116.1-65.6 158.4z"></path></svg><!--!-->
</span><!--!-->
    </button><!--!-->
</span></span></span>        </td><!--!-->
    <!--!-->
<!--!-->    <!--!--><!--!-->
<!--!-->
        <td class="ant-table-cell ant-table-cell-ellipsis" style="text-align: center;  " rowspan="1" colspan="1" title="" data-label="产品规格"><!--!-->
<!--!-->
<!--!--><!--!--><!--!--><span class="ant-input-group-wrapper ant-input-search ant-input-group-wrapper ant-input-search-enter-button" style=" "><span class="ant-input-wrapper ant-input-group"><span class=" ant-input-affix-wrapper   ant-input-affix-wrapper-input-with-clear-btn ant-input-search"><input class="ant-input" style=" " id="ant-blazor-d8dc9da9-77fd-4d09-8c0a-1a118abf46a9" type="text" placeholder="请输入产品规格" readonly="" _bl_59f7b547-40d8-4e4c-b22b-f9cd54461f48=""><span class="ant-input-suffix"><span class="ant-input-clear-icon "><!--!--><span class="anticon anticon-close-circle" id="ant-blazor-e61e5c97-2e2c-4ba2-b477-919f54c45050" role="img" _bl_8488b1c8-b271-4fbb-beee-dfe3e8b18c05=""><!--!-->
<!--!--><svg focusable="false" width="1em" height="1em" fill="currentColor" style="pointer-events: none;" xmlns="http://www.w3.org/2000/svg" class="icon" viewBox="0 0 1024 1024"><path d="M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm165.4 618.2l-66-.3L512 563.4l-99.3 118.4-66.1.3c-4.4 0-8-3.5-8-8 0-1.9.7-3.7 1.9-5.2l130.1-155L340.5 359a8.32 8.32 0 0 1-1.9-5.2c0-4.4 3.6-8 8-8l66.1.3L512 464.6l99.3-118.4 66-.3c4.4 0 8 3.5 8 8 0 1.9-.7 3.7-1.9 5.2L553.5 514l130 155c1.2 1.5 1.9 3.3 1.9 5.2 0 4.4-3.6 8-8 8z"></path></svg><!--!-->
</span></span></span></span><span class="ant-input-group-addon"><!--!--><!--!--><!--!-->
    <button class="ant-input-search-button ant-btn ant-btn-default ant-btn-icon-only" id="ant-blazor-be5c733e-d139-47ef-b5c8-02b5b8fff2fa" type="button" ant-click-animating-without-extra-node="false" _bl_c3f18da3-38aa-4d6d-bf80-5b2fa5098b41=""><!--!-->
            <!--!--><span class="anticon anticon-search" id="ant-blazor-bfbb5cc7-1bad-4ea4-aed2-35a5572d148f" role="img" _bl_e25ce47d-5112-44e5-8534-35282d28f292=""><!--!-->
<!--!--><svg focusable="false" width="1em" height="1em" fill="currentColor" style="pointer-events: none;" xmlns="http://www.w3.org/2000/svg" class="icon" viewBox="0 0 1024 1024"><path d="M909.6 854.5L649.9 594.8C690.2 542.7 712 479 712 412c0-80.2-31.3-155.4-87.9-212.1-56.6-56.7-132-87.9-212.1-87.9s-155.5 31.3-212.1 87.9C143.2 256.5 112 331.8 112 412c0 80.1 31.3 155.5 87.9 212.1C256.5 680.8 331.8 712 412 712c67 0 130.6-21.8 182.7-62l259.7 259.6a8.2 8.2 0 0 0 11.6 0l43.6-43.5a8.2 8.2 0 0 0 0-11.6zM570.4 570.4C528 612.7 471.8 636 412 636s-116-23.3-158.4-65.6C211.3 528 188 471.8 188 412s23.3-116.1 65.6-158.4C296 211.3 352.2 188 412 188s116.1 23.2 158.4 65.6S636 352.2 636 412s-23.3 116.1-65.6 158.4z"></path></svg><!--!-->
</span><!--!-->
    </button><!--!-->
</span></span></span>        </td><!--!-->
    <!--!-->
<!--!-->    <!--!--><!--!-->
<!--!-->
        <td class="ant-table-cell ant-table-cell-ellipsis" style=" " rowspan="1" colspan="1" title="" data-label="产品规格"><!--!-->
<!--!-->
<!--!--><!--!--><!--!--><input class="ant-input" style=" " id="ant-blazor-dbe7392f-4a43-4f05-b469-92d6a34fa269" type="text" placeholder="请输入产品规格" _bl_ffcaf1b0-6807-4ec2-bf26-59fdbd2ae2b0="">        </td><!--!-->
    <!--!-->
<!--!-->    <!--!--><!--!-->
<!--!-->
        <td class="ant-table-cell ant-table-cell-ellipsis" style="text-align: right;  " rowspan="1" colspan="1" title="" data-label="数量"><!--!-->
<!--!-->
<!--!--><!--!--><!--!--><div class="ant-input-number" style=" " id="ant-blazor-372ca168-6fc6-4963-8002-6de218f15892"><!--!-->
        <div class="ant-input-number-handler-wrap"><!--!-->
            <span unselectable="unselectable" role="button" aria-label="Increase Value" class="ant-input-number-handler ant-input-number-handler-up "><!--!-->
                <!--!--><span class="ant-input-number-handler-up-inner anticon anticon-up" id="ant-blazor-608dc8e2-f16e-4008-a733-6a8e0c5469fa" role="img" _bl_87ce88e3-ed5e-4cfe-b4a8-5c1f8512d148=""><!--!-->
<!--!--><svg focusable="false" width="1em" height="1em" fill="currentColor" style="pointer-events: none;" xmlns="http://www.w3.org/2000/svg" class="icon" viewBox="0 0 1024 1024"><path d="M890.5 755.3L537.9 269.2c-12.8-17.6-39-17.6-51.7 0L133.5 755.3A8 8 0 0 0 140 768h75c5.1 0 9.9-2.5 12.9-6.6L512 369.8l284.1 391.6c3 4.1 7.8 6.6 12.9 6.6h75c6.5 0 10.3-7.4 6.5-12.7z"></path></svg><!--!-->
</span><!--!-->
            </span><!--!-->
            <span unselectable="unselectable" role="button" aria-label="Decrease Value" class="ant-input-number-handler ant-input-number-handler-down "><!--!-->
                <!--!--><span class="ant-input-number-handler-down-inner anticon anticon-down" id="ant-blazor-9f939e38-c6a1-4f25-9b76-ed96b1e725a2" role="img" _bl_7789ea03-594c-467d-b6f1-cc1994f5b18c=""><!--!-->
<!--!--><svg focusable="false" width="1em" height="1em" fill="currentColor" style="pointer-events: none;" xmlns="http://www.w3.org/2000/svg" class="icon" viewBox="0 0 1024 1024"><path d="M884 256h-75c-5.1 0-9.9 2.5-12.9 6.6L512 654.2 227.9 262.6c-3-4.1-7.8-6.6-12.9-6.6h-75c-6.5 0-10.3 7.4-6.5 12.7l352.6 486.1c12.8 17.6 39 17.6 51.7 0l352.6-486.1c3.9-5.3.1-12.7-6.4-12.7z"></path></svg><!--!-->
</span><!--!-->
            </span><!--!-->
        </div><!--!-->
        <div class="ant-input-number-input-wrap"><!--!-->
            <input id="ant-blazor-372ca168-6fc6-4963-8002-6de218f15892_input" role="spinbutton" aria-valuemin="0" aria-valuemax="4294967295" autocomplete="off" max="4294967295" min="0" step="1" inputmode="numeric" placeholder="请输入数量" aria-valuenow="1" class="ant-input-number-input" name="" _bl_e9c41f50-c3b7-498f-9105-78999d18035e=""><!--!-->
        </div><!--!-->
    </div>        </td><!--!-->
    <!--!-->
<!--!-->    <!--!--><!--!-->
<!--!-->
        <td class="ant-table-cell ant-table-cell-ellipsis" style="text-align: right;  " rowspan="1" colspan="1" title="" data-label="授权天数"><!--!-->
<!--!-->
<!--!--><!--!--><!--!--><div class="ant-input-number" style=" " id="ant-blazor-5c9a6a82-3215-4c0b-9e9a-b174f8604e58"><!--!-->
        <div class="ant-input-number-handler-wrap"><!--!-->
            <span unselectable="unselectable" role="button" aria-label="Increase Value" class="ant-input-number-handler ant-input-number-handler-up "><!--!-->
                <!--!--><span class="ant-input-number-handler-up-inner anticon anticon-up" id="ant-blazor-240dd476-a6c7-41be-b171-a526533b47c6" role="img" _bl_072023e1-1c4c-4d29-96c7-af4e692aebcb=""><!--!-->
<!--!--><svg focusable="false" width="1em" height="1em" fill="currentColor" style="pointer-events: none;" xmlns="http://www.w3.org/2000/svg" class="icon" viewBox="0 0 1024 1024"><path d="M890.5 755.3L537.9 269.2c-12.8-17.6-39-17.6-51.7 0L133.5 755.3A8 8 0 0 0 140 768h75c5.1 0 9.9-2.5 12.9-6.6L512 369.8l284.1 391.6c3 4.1 7.8 6.6 12.9 6.6h75c6.5 0 10.3-7.4 6.5-12.7z"></path></svg><!--!-->
</span><!--!-->
            </span><!--!-->
            <span unselectable="unselectable" role="button" aria-label="Decrease Value" class="ant-input-number-handler ant-input-number-handler-down "><!--!-->
                <!--!--><span class="ant-input-number-handler-down-inner anticon anticon-down" id="ant-blazor-356c8339-0257-42a4-9a31-f8f3d7f87022" role="img" _bl_13b722f0-89e1-40b4-a24e-bfe24069a9df=""><!--!-->
<!--!--><svg focusable="false" width="1em" height="1em" fill="currentColor" style="pointer-events: none;" xmlns="http://www.w3.org/2000/svg" class="icon" viewBox="0 0 1024 1024"><path d="M884 256h-75c-5.1 0-9.9 2.5-12.9 6.6L512 654.2 227.9 262.6c-3-4.1-7.8-6.6-12.9-6.6h-75c-6.5 0-10.3 7.4-6.5 12.7l352.6 486.1c12.8 17.6 39 17.6 51.7 0l352.6-486.1c3.9-5.3.1-12.7-6.4-12.7z"></path></svg><!--!-->
</span><!--!-->
            </span><!--!-->
        </div><!--!-->
        <div class="ant-input-number-input-wrap"><!--!-->
            <input id="ant-blazor-5c9a6a82-3215-4c0b-9e9a-b174f8604e58_input" role="spinbutton" aria-valuemin="0" aria-valuemax="4294967295" autocomplete="off" max="4294967295" min="0" step="1" inputmode="numeric" placeholder="请输入授权天数" aria-valuenow="30" class="ant-input-number-input" name="" _bl_5b0244e4-7e76-40dd-9f50-cc935c36f938=""><!--!-->
        </div><!--!-->
    </div>        </td><!--!-->
    <!--!-->
<!--!-->    <!--!--><!--!-->
<!--!-->
        <td class="ant-table-cell ant-table-cell-ellipsis" style="text-align: right;  " rowspan="1" colspan="1" title="" data-label="并发限制"><!--!-->
<!--!-->
<!--!--><!--!--><!--!--><div class="ant-input-number" style=" " id="ant-blazor-20b7cfd9-4174-4561-954e-79a23eb3ac7a"><!--!-->
        <div class="ant-input-number-handler-wrap"><!--!-->
            <span unselectable="unselectable" role="button" aria-label="Increase Value" class="ant-input-number-handler ant-input-number-handler-up "><!--!-->
                <!--!--><span class="ant-input-number-handler-up-inner anticon anticon-up" id="ant-blazor-79ba8c85-2b8d-49e9-a7c9-0723ae17c35c" role="img" _bl_9680df69-f1a3-4971-8c8c-fdcce087a496=""><!--!-->
<!--!--><svg focusable="false" width="1em" height="1em" fill="currentColor" style="pointer-events: none;" xmlns="http://www.w3.org/2000/svg" class="icon" viewBox="0 0 1024 1024"><path d="M890.5 755.3L537.9 269.2c-12.8-17.6-39-17.6-51.7 0L133.5 755.3A8 8 0 0 0 140 768h75c5.1 0 9.9-2.5 12.9-6.6L512 369.8l284.1 391.6c3 4.1 7.8 6.6 12.9 6.6h75c6.5 0 10.3-7.4 6.5-12.7z"></path></svg><!--!-->
</span><!--!-->
            </span><!--!-->
            <span unselectable="unselectable" role="button" aria-label="Decrease Value" class="ant-input-number-handler ant-input-number-handler-down "><!--!-->
                <!--!--><span class="ant-input-number-handler-down-inner anticon anticon-down" id="ant-blazor-0059f5bd-30e5-49a7-837b-b0d65e518715" role="img" _bl_9480365c-beb8-488a-b372-6c550790064e=""><!--!-->
<!--!--><svg focusable="false" width="1em" height="1em" fill="currentColor" style="pointer-events: none;" xmlns="http://www.w3.org/2000/svg" class="icon" viewBox="0 0 1024 1024"><path d="M884 256h-75c-5.1 0-9.9 2.5-12.9 6.6L512 654.2 227.9 262.6c-3-4.1-7.8-6.6-12.9-6.6h-75c-6.5 0-10.3 7.4-6.5 12.7l352.6 486.1c12.8 17.6 39 17.6 51.7 0l352.6-486.1c3.9-5.3.1-12.7-6.4-12.7z"></path></svg><!--!-->
</span><!--!-->
            </span><!--!-->
        </div><!--!-->
        <div class="ant-input-number-input-wrap"><!--!-->
            <input id="ant-blazor-20b7cfd9-4174-4561-954e-79a23eb3ac7a_input" role="spinbutton" aria-valuemin="0" aria-valuemax="4294967295" autocomplete="off" max="4294967295" min="0" step="1" inputmode="numeric" placeholder="请输入并发限制" aria-valuenow="1" class="ant-input-number-input" name="" _bl_2340673b-46f4-4788-b40c-dab0ed542eca=""><!--!-->
        </div><!--!-->
    </div>        </td><!--!-->
    <!--!-->
<!--!-->    <!--!--><!--!-->
<!--!-->
        <td class="ant-table-cell ant-table-cell-ellipsis" style="text-align: right;  " rowspan="1" colspan="1" title="" data-label="价格"><!--!-->
<!--!-->
<!--!--><!--!--><!--!--><div class="ant-input-number ant-input-number-focused" style=" " id="ant-blazor-3681a3cc-1c98-41fb-b544-58274094e052"><!--!-->
        <div class="ant-input-number-handler-wrap"><!--!-->
            <span unselectable="unselectable" role="button" aria-label="Increase Value" class="ant-input-number-handler ant-input-number-handler-up "><!--!-->
                <!--!--><span class="ant-input-number-handler-up-inner anticon anticon-up" id="ant-blazor-370679fd-2128-4d1f-9c58-8421370b02d7" role="img" _bl_58d41815-9d6c-4bf9-8bd3-73519ba30635=""><!--!-->
<!--!--><svg focusable="false" width="1em" height="1em" fill="currentColor" style="pointer-events: none;" xmlns="http://www.w3.org/2000/svg" class="icon" viewBox="0 0 1024 1024"><path d="M890.5 755.3L537.9 269.2c-12.8-17.6-39-17.6-51.7 0L133.5 755.3A8 8 0 0 0 140 768h75c5.1 0 9.9-2.5 12.9-6.6L512 369.8l284.1 391.6c3 4.1 7.8 6.6 12.9 6.6h75c6.5 0 10.3-7.4 6.5-12.7z"></path></svg><!--!-->
</span><!--!-->
            </span><!--!-->
            <span unselectable="unselectable" role="button" aria-label="Decrease Value" class="ant-input-number-handler ant-input-number-handler-down "><!--!-->
                <!--!--><span class="ant-input-number-handler-down-inner anticon anticon-down" id="ant-blazor-20bc178b-46fa-4876-be2e-ed634b2b4500" role="img" _bl_1e1e814f-5600-48d0-80e0-ea57ccc7c39a=""><!--!-->
<!--!--><svg focusable="false" width="1em" height="1em" fill="currentColor" style="pointer-events: none;" xmlns="http://www.w3.org/2000/svg" class="icon" viewBox="0 0 1024 1024"><path d="M884 256h-75c-5.1 0-9.9 2.5-12.9 6.6L512 654.2 227.9 262.6c-3-4.1-7.8-6.6-12.9-6.6h-75c-6.5 0-10.3 7.4-6.5 12.7l352.6 486.1c12.8 17.6 39 17.6 51.7 0l352.6-486.1c3.9-5.3.1-12.7-6.4-12.7z"></path></svg><!--!-->
</span><!--!-->
            </span><!--!-->
        </div><!--!-->
        <div class="ant-input-number-input-wrap"><!--!-->
            <input id="ant-blazor-3681a3cc-1c98-41fb-b544-58274094e052_input" role="spinbutton" aria-valuemin="-79228162514264337593543950335" aria-valuemax="79228162514264337593543950335" autocomplete="off" max="79228162514264337593543950335" min="-79228162514264337593543950335" step="1" inputmode="decimal" placeholder="请输入价格" aria-valuenow="0.01" class="ant-input-number-input" name="" _bl_bf451386-4905-4df3-b3eb-5d7a05d915bd=""><!--!-->
        </div><!--!-->
    </div>        </td><!--!-->
    <!--!-->
<!--!-->    <!--!--><!--!-->
<!--!-->
        <td class="ant-table-cell ant-table-cell-ellipsis" style="text-align: right;  " rowspan="1" colspan="1" title="" data-label="排位"><!--!-->
<!--!-->
<!--!--><!--!--><!--!--><div class="ant-input-number" style=" " id="ant-blazor-61c0b9e8-3fb4-4178-b199-5994514193a1"><!--!-->
        <div class="ant-input-number-handler-wrap"><!--!-->
            <span unselectable="unselectable" role="button" aria-label="Increase Value" class="ant-input-number-handler ant-input-number-handler-up "><!--!-->
                <!--!--><span class="ant-input-number-handler-up-inner anticon anticon-up" id="ant-blazor-167fb931-2bc5-4059-851e-06ec7e9a9e13" role="img" _bl_94d576ec-d1ae-4913-9a66-42ca1c475013=""><!--!-->
<!--!--><svg focusable="false" width="1em" height="1em" fill="currentColor" style="pointer-events: none;" xmlns="http://www.w3.org/2000/svg" class="icon" viewBox="0 0 1024 1024"><path d="M890.5 755.3L537.9 269.2c-12.8-17.6-39-17.6-51.7 0L133.5 755.3A8 8 0 0 0 140 768h75c5.1 0 9.9-2.5 12.9-6.6L512 369.8l284.1 391.6c3 4.1 7.8 6.6 12.9 6.6h75c6.5 0 10.3-7.4 6.5-12.7z"></path></svg><!--!-->
</span><!--!-->
            </span><!--!-->
            <span unselectable="unselectable" role="button" aria-label="Decrease Value" class="ant-input-number-handler ant-input-number-handler-down "><!--!-->
                <!--!--><span class="ant-input-number-handler-down-inner anticon anticon-down" id="ant-blazor-cbffd95a-1588-48b4-82c0-e97cd7a02d72" role="img" _bl_2de82ee8-8fc4-4322-829a-7909df7e73ca=""><!--!-->
<!--!--><svg focusable="false" width="1em" height="1em" fill="currentColor" style="pointer-events: none;" xmlns="http://www.w3.org/2000/svg" class="icon" viewBox="0 0 1024 1024"><path d="M884 256h-75c-5.1 0-9.9 2.5-12.9 6.6L512 654.2 227.9 262.6c-3-4.1-7.8-6.6-12.9-6.6h-75c-6.5 0-10.3 7.4-6.5 12.7l352.6 486.1c12.8 17.6 39 17.6 51.7 0l352.6-486.1c3.9-5.3.1-12.7-6.4-12.7z"></path></svg><!--!-->
</span><!--!-->
            </span><!--!-->
        </div><!--!-->
        <div class="ant-input-number-input-wrap"><!--!-->
            <input id="ant-blazor-61c0b9e8-3fb4-4178-b199-5994514193a1_input" role="spinbutton" aria-valuemin="-2147483648" aria-valuemax="2147483647" autocomplete="off" max="2147483647" min="-2147483648" step="1" inputmode="numeric" placeholder="请输入排位" aria-valuenow="0" class="ant-input-number-input" name="" _bl_e18ba91b-8f03-4e59-b875-47cff3f88f40=""><!--!-->
        </div><!--!-->
    </div>        </td><!--!-->
    <!--!-->
<!--!-->
         
            <!--!-->    <td class="ant-table-cell ant-table-cell-fix-right ant-table-cell-fix-right-first" style="text-align: center; position: sticky; right: 0px;  " rowspan="1" colspan="1" data-label="操作"><!--!-->
<!--!-->
<!--!--><!--!--><!--!-->
    <div class="ant-btn-group" id="ant-blazor-94620010-3d9d-4f13-a826-b318f5cd3b8a" _bl_0974e55c-4051-403c-a37d-b012081a0ebf=""><!--!-->
        <!--!--><!--!--><!--!-->
    <button class="ant-btn ant-btn-link" id="ant-blazor-046dd415-a773-4952-848f-1bd7230bff6c" type="button" disabled="" ant-click-animating-without-extra-node="false" _bl_3ebe8ad3-266f-47bf-9be6-fecbe78a1fc4=""><!--!-->
                <span><!--!--><span class="anticon anticon-up" id="ant-blazor-1550a361-1517-4756-a4da-c33fdeff6c5c" role="img" _bl_635addde-ee18-4c8b-b978-7506d5f82604=""><!--!-->
<!--!--><svg focusable="false" width="1em" height="1em" fill="currentColor" style="pointer-events: none;" xmlns="http://www.w3.org/2000/svg" class="icon" viewBox="0 0 1024 1024"><path d="M890.5 755.3L537.9 269.2c-12.8-17.6-39-17.6-51.7 0L133.5 755.3A8 8 0 0 0 140 768h75c5.1 0 9.9-2.5 12.9-6.6L512 369.8l284.1 391.6c3 4.1 7.8 6.6 12.9 6.6h75c6.5 0 10.3-7.4 6.5-12.7z"></path></svg><!--!-->
</span></span><!--!-->
    </button><!--!-->
<!--!-->
                    <!--!--><!--!--><!--!-->
    <button class="ant-btn ant-btn-link" id="ant-blazor-0414c0c0-5301-4e48-a712-357a314f3dde" type="button" disabled="" ant-click-animating-without-extra-node="false" _bl_16cf816d-d3a8-4ad9-b179-3a9d45649bae=""><!--!-->
                <span><!--!--><span class="anticon anticon-down" id="ant-blazor-b8642f92-fa65-4a63-a38f-c1609f6c20c0" role="img" _bl_279affe0-a7fa-4124-920d-a81d9958e52c=""><!--!-->
<!--!--><svg focusable="false" width="1em" height="1em" fill="currentColor" style="pointer-events: none;" xmlns="http://www.w3.org/2000/svg" class="icon" viewBox="0 0 1024 1024"><path d="M884 256h-75c-5.1 0-9.9 2.5-12.9 6.6L512 654.2 227.9 262.6c-3-4.1-7.8-6.6-12.9-6.6h-75c-6.5 0-10.3 7.4-6.5 12.7l352.6 486.1c12.8 17.6 39 17.6 51.7 0l352.6-486.1c3.9-5.3.1-12.7-6.4-12.7z"></path></svg><!--!-->
</span></span><!--!-->
    </button><!--!-->
<!--!--><!--!--><!--!-->
    <button class="ant-btn ant-btn-link ant-btn-sm" id="ant-blazor-b20a8cca-0ad7-429b-9f4e-c5f2a807d5c7" type="button" ant-click-animating-without-extra-node="false" _bl_6a820ecd-aa44-4a24-b8c7-2a236f5655d6=""><!--!-->
                <span><!--!--><span class="anticon anticon-delete" id="ant-blazor-11c4e80d-ce9c-44d0-b4c0-9cf81868bf5e" role="img" _bl_366c475e-e00b-48c1-9260-c9ef59ae67a7=""><!--!-->
<!--!--><svg focusable="false" width="1em" height="1em" fill="currentColor" style="pointer-events: none;" xmlns="http://www.w3.org/2000/svg" class="icon" viewBox="0 0 1024 1024"><path d="M360 184h-8c4.4 0 8-3.6 8-8v8h304v-8c0 4.4 3.6 8 8 8h-8v72h72v-80c0-35.3-28.7-64-64-64H352c-35.3 0-64 28.7-64 64v80h72v-72zm504 72H160c-17.7 0-32 14.3-32 32v32c0 4.4 3.6 8 8 8h60.4l24.7 523c1.6 34.1 29.8 61 63.9 61h454c34.2 0 62.3-26.8 63.9-61l24.7-523H888c4.4 0 8-3.6 8-8v-32c0-17.7-14.3-32-32-32zM731.3 840H292.7l-24.2-512h487l-24.2 512z"></path></svg><!--!-->
</span></span><!--!-->
    </button><!--!-->
<!--!-->
    </div><!--!-->
    </td><!--!-->
<!--!-->
                                <!--!-->
    </tr><!--!-->
<!--!-->
                        <!--!-->
                    <!--!-->
                <!--!-->
            <!--!-->
<!--!-->

<!--!-->
                                        </tbody><!--!-->
                                        <!--!-->
                                    </table><!--!-->
                                </div><!--!-->
                        </div><!--!-->
                    </div><!--!-->
                <!--!-->
            <!--!-->
        <!--!-->
    <!--!-->
                </div><!--!-->
        <!--!-->
        </div><!--!-->
    </div><!--!-->
<!--!-->
</div><!--!-->
        <!--!--><div class="ant-flex ant-flex-align-normal ant-flex-justify-end ant-flex-gap-small ant-flex-wrap-nowrap" style="flex-wrap: nowrap; align-items: normal; justify-content: end; flex: normal;   " id="ant-blazor-ae6b783c-ac45-4f6c-9d58-d9ebf5f6e87f" _bl_f0a33d75-532d-4d6f-a637-ccc20e735899=""><!--!--><span class="ant-typography ant-typography-secondary" _bl_4be4530a-e233-474b-bf6d-887fd003c29d="">1<!--!-->行</span><!--!-->
            <!--!--><!--!--><!--!-->
    <button class="ant-btn ant-btn-primary ant-btn-sm" id="ant-blazor-97361cdd-f74b-47f5-b365-5f651c33e8aa" type="button" ant-click-animating-without-extra-node="false" _bl_1c820b6c-0453-4dc4-88d0-4e4f24b30762=""><!--!-->
                <span><!--!--><span class="anticon anticon-plus" id="ant-blazor-8414bb86-89b4-41a4-b8cc-23e97a8113f5" role="img" _bl_40756e36-83c2-4f8f-87da-93511459bbe8=""><!--!-->
<!--!--><svg focusable="false" width="1em" height="1em" fill="currentColor" style="pointer-events: none;" xmlns="http://www.w3.org/2000/svg" class="icon" viewBox="0 0 1024 1024"><path d="M474 152m8 0l60 0q8 0 8 8l0 704q0 8-8 8l-60 0q-8 0-8-8l0-704q0-8 8-8Z" pid="10298"></path><path d="M168 474m8 0l672 0q8 0 8 8l0 60q0 8-8 8l-672 0q-8 0-8-8l0-60q0-8 8-8Z" pid="10299"></path></svg><!--!-->
</span></span><!--!-->
    </button><!--!-->
</div><!--!-->
                    <!--!-->
                </div><!--!-->
            </div><!--!-->

        <!--!-->
</div><!--!-->
    <!--!-->
    </div><!--!-->
<!--!-->
</div><!--!--><!--!--><!--!--><div class="test ant-form-item" id="ant-blazor-947709cf-6c36-4239-8fdb-6cc111f17b29"><!--!-->
    <!--!--><!--!--><!--!-->
    <div class="ant-form-item-row ant-row" style="row-gap: 0px;  " id="ant-blazor-93266879-6abb-4c3f-90ff-7bea3a4ebd9d" _bl_c6f61460-c335-4bde-a381-4af486a2b740=""><!--!-->
        <!--!-->
            <!--!--><div class="ant-form-item-label ant-col" style="  " id="ant-blazor-37c5f2b4-870a-40e5-bf76-e85558a9ec95" _bl_3aecf011-441c-4e8e-9801-882ee37d65e0=""><!--!-->
    <!--!-->
<div class="form-item-head" b-9mwaqr7zgv=""><label class="form-item-label-required" b-9mwaqr7zgv="">等级</label></div>            <!--!-->
</div><!--!-->
<!--!-->
        <!--!--><div class="ant-form-item-control ant-col" style="  " id="ant-blazor-57407bc8-f202-43e6-81f4-283bfbdc2eb3" _bl_bf0fc22e-b861-48d1-9177-7eac9042f8f7=""><!--!-->
    <!--!-->
            <div class="ant-form-item-control-input"><!--!-->
                <div class="ant-form-item-control-input-content"><!--!-->
                    <!--!--><!--!-->
                        <!--!--><!--!--><!--!-->
    <!--!--><!--!-->
        <!--!--><!--!-->
<!--!-->
<!--!-->
                    <!--!--><!--!-->
                        <!--!--><!--!-->
                            <!--!--><!--!-->
                                <!--!--><!--!-->
                                    <!--!--><!--!-->
                                        <!--!--><div class="ant-select ant-select-single ant-select-show-arrow ant-select-bordered ant-select-in-form-item " style="min-width:10em;" id="ant-blazor-2f9e0bbc-292f-4ad9-a71c-f6d443b92e0d" tabindex="-1" _bl_27ed3422-d2c7-4ae7-b730-39ab4dd25da1=""><!--!-->
      <div class="ant-select-selector" style=""><!--!-->
        <span class="ant-select-selection-search" style=""><!--!-->
          <input id="ant-blazor-2f9e0bbc-292f-4ad9-a71c-f6d443b92e0d_list" type="search" readonly="" unselectable="on" role="combobox" class="ant-select-selection-search-input" autocomplete="off" aria-owns="ant-blazor-2f9e0bbc-292f-4ad9-a71c-f6d443b92e0d_list" aria-autocomplete="list" aria-controls="ant-blazor-2f9e0bbc-292f-4ad9-a71c-f6d443b92e0d_list" aria-haspopup="listbox" style="opacity: 0;" _bl_db69361c-79bc-4d5a-a55f-5ddaf1343ff3=""><!--!-->
        </span><!--!-->
              <span class="ant-select-selection-item" title="等级一"><!--!-->
                等级一<!--!-->
              </span><!--!-->
      </div><!--!-->
        <!--!-->        <span class="ant-select-arrow" unselectable="on" aria-hidden="true" style="user-select: none;"><!--!-->
                <!--!--><span class="anticon anticon-down" id="ant-blazor-3e9e2800-7a1b-4333-861f-24c086de8aa0" role="img" _bl_b3dbab58-6cc7-4477-a006-710dc2f7a3d6=""><!--!-->
<!--!--><svg focusable="false" width="1em" height="1em" fill="currentColor" style="pointer-events: none;" xmlns="http://www.w3.org/2000/svg" class="icon" viewBox="0 0 1024 1024"><path d="M884 256h-75c-5.1 0-9.9 2.5-12.9 6.6L512 654.2 227.9 262.6c-3-4.1-7.8-6.6-12.9-6.6h-75c-6.5 0-10.3 7.4-6.5 12.7l352.6 486.1c12.8 17.6 39 17.6 51.7 0l352.6-486.1c3.9-5.3.1-12.7-6.4-12.7z"></path></svg><!--!-->
</span><!--!-->
<!--!-->
        </span><!--!-->
<!--!-->
</div><!--!-->
                                    <!--!-->
                                <!--!-->
                            <!--!-->
                        <!--!-->
                    <!--!-->
                <!--!-->
<!--!--><!--!-->
    <!--!-->    <div class="ant-select-dropdown    ant-select-dropdown-placement-bottomLeft ant-select-dropdown-hidden" style="position: absolute;   display: none;" _bl_a38f734a-825e-48b6-aec6-8a3627f158ed=""><!--!-->

        <!--!--><!--!-->
            <!--!--><!--!-->
<!--!-->
                    <div style="min-width: 1231.43px;width: 1231.43px;"><!--!-->
<div class="" style="max-height: 256px; overflow-y: auto;" _bl_83aac830-0297-420b-8dca-7a4e25976dda=""><!--!-->
                <div><!--!-->
                    <div class="" role="listbox" style="display: flex; flex-direction: column;"><!--!-->
                        <!--!--><div class="ant-select-item ant-select-item-option ant-select-item-option-selected ant-select-item-option-active" role="option" aria-label="等级一" _bl_6e5d58a1-47e6-49e7-bb74-c9fd856f8feb="" aria-selected=""><!--!-->
    <div class="ant-select-item-option-content"><!--!-->
等级一    </div><!--!-->
            <span class="ant-select-item-option-state" unselectable="on" aria-hidden="true" style="user-select: none;"></span><!--!-->
</div><!--!--><div class="ant-select-item ant-select-item-option" role="option" aria-label="等级二" _bl_e00cf3eb-ffe1-4a6b-9f18-f9e511a4fd5e=""><!--!-->
    <div class="ant-select-item-option-content"><!--!-->
等级二    </div><!--!-->
            <span class="ant-select-item-option-state" unselectable="on" aria-hidden="true" style="user-select: none;"></span><!--!-->
</div><!--!--><div class="ant-select-item ant-select-item-option" role="option" aria-label="等级三" _bl_d13c8325-7270-435a-8330-5a30a1ff881b=""><!--!-->
    <div class="ant-select-item-option-content"><!--!-->
等级三    </div><!--!-->
            <span class="ant-select-item-option-state" unselectable="on" aria-hidden="true" style="user-select: none;"></span><!--!-->
</div><!--!--><div class="ant-select-item ant-select-item-option" role="option" aria-label="等级四" _bl_eaab981a-2c17-402f-ab6c-cbf685c86d05=""><!--!-->
    <div class="ant-select-item-option-content"><!--!-->
等级四    </div><!--!-->
            <span class="ant-select-item-option-state" unselectable="on" aria-hidden="true" style="user-select: none;"></span><!--!-->
</div><!--!--><div class="ant-select-item ant-select-item-option" role="option" aria-label="等级五" _bl_5760eacf-57fd-4f93-93a3-e18812627acc=""><!--!-->
    <div class="ant-select-item-option-content"><!--!-->
等级五    </div><!--!-->
            <span class="ant-select-item-option-state" unselectable="on" aria-hidden="true" style="user-select: none;"></span><!--!-->
</div><!--!-->
                    </div><!--!-->
                </div><!--!-->
            </div><!--!-->
                    </div><!--!-->                    
                            <!--!-->
        <!--!-->
    </div><!--!-->
<!--!-->
<!--!-->
    <!--!-->
<!--!-->
                    <!--!-->
                </div><!--!-->
            </div><!--!-->

        <!--!-->
</div><!--!-->
    <!--!-->
    </div><!--!-->
<!--!-->
</div>        <!--!-->
    </form><!--!-->
<!--!-->
                </div><!--!-->
        <!--!-->
        </div><!--!-->
    </div><!--!-->
            </div><!--!-->
    </div><!--!-->
<!--!-->
            </div><!--!-->
    <!--!-->
    </div><!--!-->
</div><!--!-->
</div>            <!--!-->
    </div><!--!-->
<!--!-->
</main><!--!-->
            
<!--!--><!--!--><!--!--><footer class="ant-pro-global-footer" id="ant-blazor-546e0921-f83e-48e6-8966-da1239db1a90"><!--!-->
        <div class="ant-pro-global-footer-links"><!--!-->
            <a key="home" title="home" target="_blank" href="http://www.dualmirror.cn/"><!--!-->
双镜官网            </a><!--!-->
        </div><!--!-->
        <div class="ant-pro-global-footer-copyright"><!--!-->
            <div><!--!-->
                Copyright <!--!--><span class="anticon anticon-copyright" id="ant-blazor-14f6e101-7e0f-48c9-9b74-67791a6cc20f" role="img" _bl_55b22356-7e18-409f-9036-81e275155737=""><!--!-->
<!--!--><svg focusable="false" width="1em" height="1em" fill="currentColor" style="pointer-events: none;" xmlns="http://www.w3.org/2000/svg" class="icon" viewBox="0 0 1024 1024"><path d="M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372zm5.6-532.7c53 0 89 33.8 93 83.4.3 4.2 3.8 7.4 8 7.4h56.7c2.6 0 4.7-2.1 4.7-4.7 0-86.7-68.4-147.4-162.7-147.4C407.4 290 344 364.2 344 486.8v52.3C344 660.8 407.4 734 517.3 734c94 0 162.7-58.8 162.7-141.4 0-2.6-2.1-4.7-4.7-4.7h-56.8c-4.2 0-7.6 3.2-8 7.3-4.2 46.1-40.1 77.8-93 77.8-65.3 0-102.1-47.9-102.1-133.6v-52.6c.1-87 37-135.5 102.2-135.5z"></path></svg><!--!-->
</span> 2023 双镜<!--!-->
            </div><!--!-->
    </div><!--!-->
</footer>        <!--!-->
    </section><!--!-->
<!--!-->
    <!--!-->
    </section><!--!-->
<!--!-->
</div><!--!-->
                    <!--!--><!--!-->
    <!--!--><!--!--><!--!-->
<!--!--><!--!-->
<!--!--><!--!-->
<!--!--><!--!-->
<!--!--><!--!-->
<!--!--><!--!-->
<!--!--><div id="components-reconnect-modal" b-6zahsb6t48=""><div class="reconnect-state show" b-6zahsb6t48=""><span class="reconnect-content" b-6zahsb6t48="">服务器<!--!-->连接丢失，正在尝试重新连接，请稍后...</span></div><!--!-->
    <div class="reconnect-state failed" b-6zahsb6t48=""><span class="reconnect-content" b-6zahsb6t48=""><!--!-->无法连接服务器<!--!-->，您可以：<br b-6zahsb6t48=""><!--!--><a href="javascript:window.Blazor.reconnect()" b-6zahsb6t48="">再次连接</a>,  <!--!--><a href="javascript:location.reload()" b-6zahsb6t48="">刷新页面</a></span></div><!--!-->
    <div class="reconnect-state rejected" b-6zahsb6t48=""><span class="reconnect-content" b-6zahsb6t48=""><!--!-->无法连接服务器<!--!-->，请确认网络是否通畅! <br b-6zahsb6t48=""><!--!--><a href="javascript:location.reload()" b-6zahsb6t48="">刷新页面</a></span></div></div><div id="blazor-network-state-bg"></div>
    <div id="blazor-network-state-panel"><div class="content"><div><p class="title">网络异常</p><p class="message">已重新连接!</p></div></div></div>
    <div id="blazor-error-ui"><environment include="Staging,Production">
            未知错误，请<a href="javascript:document.location.reload()">刷新页面</a></environment>
        <environment include="Development">
            An unhandled exception has occurred. See browser dev tools for details.
        </environment>
        <a href="https://cmp-test.cyanmirror.com/" class="reload">刷新</a>
        <a class="dismiss">🗙</a></div>
    <script src="./商品规格_files/ant-design-blazor.js.下载"></script><script src="./商品规格_files/g2plot.min.js.下载"></script><script src="./商品规格_files/ant-design-charts-blazor.js.下载"></script><script src="./商品规格_files/blazor.web.js.下载" autostart="false"></script>
    <script src="./商品规格_files/boot.js.下载"></script></body><div id="immersive-translate-popup" style="all: initial"><template shadowrootmode="open"><style>@charset "UTF-8";
/*!
 * Pico.css v1.5.6 (https://picocss.com)
 * Copyright 2019-2022 - Licensed under MIT
 */
/**
 * Theme: default
 */
#mount {
  --font-family: system-ui, -apple-system, "Segoe UI", "Roboto", "Ubuntu",
    "Cantarell", "Noto Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji",
    "Segoe UI Symbol", "Noto Color Emoji";
  --line-height: 1.5;
  --font-weight: 400;
  --font-size: 16px;
  --border-radius: 0.25rem;
  --border-width: 1px;
  --outline-width: 3px;
  --spacing: 1rem;
  --typography-spacing-vertical: 1.5rem;
  --block-spacing-vertical: calc(var(--spacing) * 2);
  --block-spacing-horizontal: var(--spacing);
  --grid-spacing-vertical: 0;
  --grid-spacing-horizontal: var(--spacing);
  --form-element-spacing-vertical: 0.75rem;
  --form-element-spacing-horizontal: 1rem;
  --nav-element-spacing-vertical: 1rem;
  --nav-element-spacing-horizontal: 0.5rem;
  --nav-link-spacing-vertical: 0.5rem;
  --nav-link-spacing-horizontal: 0.5rem;
  --form-label-font-weight: var(--font-weight);
  --transition: 0.2s ease-in-out;
  --modal-overlay-backdrop-filter: blur(0.25rem);
}
@media (min-width: 576px) {
  #mount {
    --font-size: 17px;
  }
}
@media (min-width: 768px) {
  #mount {
    --font-size: 18px;
  }
}
@media (min-width: 992px) {
  #mount {
    --font-size: 19px;
  }
}
@media (min-width: 1200px) {
  #mount {
    --font-size: 20px;
  }
}

@media (min-width: 576px) {
  #mount > header,
  #mount > main,
  #mount > footer,
  section {
    --block-spacing-vertical: calc(var(--spacing) * 2.5);
  }
}
@media (min-width: 768px) {
  #mount > header,
  #mount > main,
  #mount > footer,
  section {
    --block-spacing-vertical: calc(var(--spacing) * 3);
  }
}
@media (min-width: 992px) {
  #mount > header,
  #mount > main,
  #mount > footer,
  section {
    --block-spacing-vertical: calc(var(--spacing) * 3.5);
  }
}
@media (min-width: 1200px) {
  #mount > header,
  #mount > main,
  #mount > footer,
  section {
    --block-spacing-vertical: calc(var(--spacing) * 4);
  }
}

@media (min-width: 576px) {
  article {
    --block-spacing-horizontal: calc(var(--spacing) * 1.25);
  }
}
@media (min-width: 768px) {
  article {
    --block-spacing-horizontal: calc(var(--spacing) * 1.5);
  }
}
@media (min-width: 992px) {
  article {
    --block-spacing-horizontal: calc(var(--spacing) * 1.75);
  }
}
@media (min-width: 1200px) {
  article {
    --block-spacing-horizontal: calc(var(--spacing) * 2);
  }
}

dialog > article {
  --block-spacing-vertical: calc(var(--spacing) * 2);
  --block-spacing-horizontal: var(--spacing);
}
@media (min-width: 576px) {
  dialog > article {
    --block-spacing-vertical: calc(var(--spacing) * 2.5);
    --block-spacing-horizontal: calc(var(--spacing) * 1.25);
  }
}
@media (min-width: 768px) {
  dialog > article {
    --block-spacing-vertical: calc(var(--spacing) * 3);
    --block-spacing-horizontal: calc(var(--spacing) * 1.5);
  }
}

a {
  --text-decoration: none;
}
a.secondary,
a.contrast {
  --text-decoration: underline;
}

small {
  --font-size: 0.875em;
}

h1,
h2,
h3,
h4,
h5,
h6 {
  --font-weight: 700;
}

h1 {
  --font-size: 2rem;
  --typography-spacing-vertical: 3rem;
}

h2 {
  --font-size: 1.75rem;
  --typography-spacing-vertical: 2.625rem;
}

h3 {
  --font-size: 1.5rem;
  --typography-spacing-vertical: 2.25rem;
}

h4 {
  --font-size: 1.25rem;
  --typography-spacing-vertical: 1.874rem;
}

h5 {
  --font-size: 1.125rem;
  --typography-spacing-vertical: 1.6875rem;
}

[type="checkbox"],
[type="radio"] {
  --border-width: 2px;
}

[type="checkbox"][role="switch"] {
  --border-width: 3px;
}

thead th,
thead td,
tfoot th,
tfoot td {
  --border-width: 3px;
}

:not(thead, tfoot) > * > td {
  --font-size: 0.875em;
}

pre,
code,
kbd,
samp {
  --font-family: "Menlo", "Consolas", "Roboto Mono", "Ubuntu Monospace",
    "Noto Mono", "Oxygen Mono", "Liberation Mono", monospace,
    "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
}

kbd {
  --font-weight: bolder;
}

[data-theme="light"],
#mount:not([data-theme="dark"]) {
  --background-color: #fff;
  --background-light-green: #F5F7F9;
  --color: hsl(205deg, 20%, 32%);
  --h1-color: hsl(205deg, 30%, 15%);
  --h2-color: #24333e;
  --h3-color: hsl(205deg, 25%, 23%);
  --h4-color: #374956;
  --h5-color: hsl(205deg, 20%, 32%);
  --h6-color: #4d606d;
  --muted-color: hsl(205deg, 10%, 50%);
  --muted-border-color: hsl(205deg, 20%, 94%);
  --primary: hsl(195deg, 85%, 41%);
  --primary-hover: hsl(195deg, 90%, 32%);
  --primary-focus: rgba(16, 149, 193, 0.125);
  --primary-inverse: #fff;
  --secondary: hsl(205deg, 15%, 41%);
  --secondary-hover: hsl(205deg, 20%, 32%);
  --secondary-focus: rgba(89, 107, 120, 0.125);
  --secondary-inverse: #fff;
  --contrast: hsl(205deg, 30%, 15%);
  --contrast-hover: #000;
  --contrast-focus: rgba(89, 107, 120, 0.125);
  --contrast-inverse: #fff;
  --mark-background-color: #fff2ca;
  --mark-color: #543a26;
  --ins-color: #388e3c;
  --del-color: #c62828;
  --blockquote-border-color: var(--muted-border-color);
  --blockquote-footer-color: var(--muted-color);
  --button-box-shadow: 0 0 0 rgba(0, 0, 0, 0);
  --button-hover-box-shadow: 0 0 0 rgba(0, 0, 0, 0);
  --form-element-background-color: transparent;
  --form-element-border-color: hsl(205deg, 14%, 68%);
  --form-element-color: var(--color);
  --form-element-placeholder-color: var(--muted-color);
  --form-element-active-background-color: transparent;
  --form-element-active-border-color: var(--primary);
  --form-element-focus-color: var(--primary-focus);
  --form-element-disabled-background-color: hsl(205deg, 18%, 86%);
  --form-element-disabled-border-color: hsl(205deg, 14%, 68%);
  --form-element-disabled-opacity: 0.5;
  --form-element-invalid-border-color: #c62828;
  --form-element-invalid-active-border-color: #d32f2f;
  --form-element-invalid-focus-color: rgba(211, 47, 47, 0.125);
  --form-element-valid-border-color: #388e3c;
  --form-element-valid-active-border-color: #43a047;
  --form-element-valid-focus-color: rgba(67, 160, 71, 0.125);
  --switch-background-color: hsl(205deg, 16%, 77%);
  --switch-color: var(--primary-inverse);
  --switch-checked-background-color: var(--primary);
  --range-border-color: hsl(205deg, 18%, 86%);
  --range-active-border-color: hsl(205deg, 16%, 77%);
  --range-thumb-border-color: var(--background-color);
  --range-thumb-color: var(--secondary);
  --range-thumb-hover-color: var(--secondary-hover);
  --range-thumb-active-color: var(--primary);
  --table-border-color: var(--muted-border-color);
  --table-row-stripped-background-color: #f6f8f9;
  --code-background-color: hsl(205deg, 20%, 94%);
  --code-color: var(--muted-color);
  --code-kbd-background-color: var(--contrast);
  --code-kbd-color: var(--contrast-inverse);
  --code-tag-color: hsl(330deg, 40%, 50%);
  --code-property-color: hsl(185deg, 40%, 40%);
  --code-value-color: hsl(40deg, 20%, 50%);
  --code-comment-color: hsl(205deg, 14%, 68%);
  --accordion-border-color: var(--muted-border-color);
  --accordion-close-summary-color: var(--color);
  --accordion-open-summary-color: var(--muted-color);
  --card-background-color: var(--background-color);
  --card-border-color: var(--muted-border-color);
  --card-box-shadow: 0.0145rem 0.029rem 0.174rem rgba(27, 40, 50, 0.01698),
    0.0335rem 0.067rem 0.402rem rgba(27, 40, 50, 0.024),
    0.0625rem 0.125rem 0.75rem rgba(27, 40, 50, 0.03),
    0.1125rem 0.225rem 1.35rem rgba(27, 40, 50, 0.036),
    0.2085rem 0.417rem 2.502rem rgba(27, 40, 50, 0.04302),
    0.5rem 1rem 6rem rgba(27, 40, 50, 0.06),
    0 0 0 0.0625rem rgba(27, 40, 50, 0.015);
  --card-sectionning-background-color: #fbfbfc;
  --dropdown-background-color: #fbfbfc;
  --dropdown-border-color: #e1e6eb;
  --dropdown-box-shadow: var(--card-box-shadow);
  --dropdown-color: var(--color);
  --dropdown-hover-background-color: hsl(205deg, 20%, 94%);
  --modal-overlay-background-color: rgba(213, 220, 226, 0.7);
  --progress-background-color: hsl(205deg, 18%, 86%);
  --progress-color: var(--primary);
  --loading-spinner-opacity: 0.5;
  --tooltip-background-color: var(--contrast);
  --tooltip-color: var(--contrast-inverse);
  --icon-checkbox: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='rgb(255, 255, 255)' stroke-width='4' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='20 6 9 17 4 12'%3E%3C/polyline%3E%3C/svg%3E");
  --icon-chevron: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='rgb(65, 84, 98)' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
  --icon-chevron-button: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='rgb(255, 255, 255)' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
  --icon-chevron-button-inverse: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='rgb(255, 255, 255)' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
  --icon-close: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='rgb(115, 130, 140)' stroke-width='4' stroke-linecap='round' stroke-linejoin='round'%3E%3Cline x1='18' y1='6' x2='6' y2='18'%3E%3C/line%3E%3Cline x1='6' y1='6' x2='18' y2='18'%3E%3C/line%3E%3C/svg%3E");
  --icon-date: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='rgb(65, 84, 98)' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Crect x='3' y='4' width='18' height='18' rx='2' ry='2'%3E%3C/rect%3E%3Cline x1='16' y1='2' x2='16' y2='6'%3E%3C/line%3E%3Cline x1='8' y1='2' x2='8' y2='6'%3E%3C/line%3E%3Cline x1='3' y1='10' x2='21' y2='10'%3E%3C/line%3E%3C/svg%3E");
  --icon-invalid: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='rgb(198, 40, 40)' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Ccircle cx='12' cy='12' r='10'%3E%3C/circle%3E%3Cline x1='12' y1='8' x2='12' y2='12'%3E%3C/line%3E%3Cline x1='12' y1='16' x2='12.01' y2='16'%3E%3C/line%3E%3C/svg%3E");
  --icon-minus: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='rgb(255, 255, 255)' stroke-width='4' stroke-linecap='round' stroke-linejoin='round'%3E%3Cline x1='5' y1='12' x2='19' y2='12'%3E%3C/line%3E%3C/svg%3E");
  --icon-search: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='rgb(65, 84, 98)' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Ccircle cx='11' cy='11' r='8'%3E%3C/circle%3E%3Cline x1='21' y1='21' x2='16.65' y2='16.65'%3E%3C/line%3E%3C/svg%3E");
  --icon-time: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='rgb(65, 84, 98)' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Ccircle cx='12' cy='12' r='10'%3E%3C/circle%3E%3Cpolyline points='12 6 12 12 16 14'%3E%3C/polyline%3E%3C/svg%3E");
  --icon-valid: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='rgb(56, 142, 60)' stroke-width='3' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='20 6 9 17 4 12'%3E%3C/polyline%3E%3C/svg%3E");
  --icon-share: url("data:image/svg+xml;charset=utf-8;base64,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");
  --float-ball-more-button-border-color: #F6F6F6;
  --float-ball-more-button-background-color: #FCFCFC;
  --float-ball-more-button-svg-color: #6C6F73;
  color-scheme: light;
  --service-bg-hover:#F7FAFF;
}

@media only screen and (prefers-color-scheme: dark) {
  #mount:not([data-theme="light"]) {
    --background-color: #11191f;
    --background-light-green: #141e26;
    --color: hsl(205deg, 16%, 77%);
    --h1-color: hsl(205deg, 20%, 94%);
    --h2-color: #e1e6eb;
    --h3-color: hsl(205deg, 18%, 86%);
    --h4-color: #c8d1d8;
    --h5-color: hsl(205deg, 16%, 77%);
    --h6-color: #afbbc4;
    --muted-color: hsl(205deg, 10%, 50%);
    --muted-border-color: #1f2d38;
    --primary: hsl(195deg, 85%, 41%);
    --primary-hover: hsl(195deg, 80%, 50%);
    --primary-focus: rgba(16, 149, 193, 0.25);
    --primary-inverse: #fff;
    --secondary: hsl(205deg, 15%, 41%);
    --secondary-hover: hsl(205deg, 10%, 50%);
    --secondary-focus: rgba(115, 130, 140, 0.25);
    --secondary-inverse: #fff;
    --contrast: hsl(205deg, 20%, 94%);
    --contrast-hover: #fff;
    --contrast-focus: rgba(115, 130, 140, 0.25);
    --contrast-inverse: #000;
    --mark-background-color: #d1c284;
    --mark-color: #11191f;
    --ins-color: #388e3c;
    --del-color: #c62828;
    --blockquote-border-color: var(--muted-border-color);
    --blockquote-footer-color: var(--muted-color);
    --button-box-shadow: 0 0 0 rgba(0, 0, 0, 0);
    --button-hover-box-shadow: 0 0 0 rgba(0, 0, 0, 0);
    --form-element-background-color: #11191f;
    --form-element-border-color: #374956;
    --form-element-color: var(--color);
    --form-element-placeholder-color: var(--muted-color);
    --form-element-active-background-color: var(
      --form-element-background-color
    );
    --form-element-active-border-color: var(--primary);
    --form-element-focus-color: var(--primary-focus);
    --form-element-disabled-background-color: hsl(205deg, 25%, 23%);
    --form-element-disabled-border-color: hsl(205deg, 20%, 32%);
    --form-element-disabled-opacity: 0.5;
    --form-element-invalid-border-color: #b71c1c;
    --form-element-invalid-active-border-color: #c62828;
    --form-element-invalid-focus-color: rgba(198, 40, 40, 0.25);
    --form-element-valid-border-color: #2e7d32;
    --form-element-valid-active-border-color: #388e3c;
    --form-element-valid-focus-color: rgba(56, 142, 60, 0.25);
    --switch-background-color: #374956;
    --switch-color: var(--primary-inverse);
    --switch-checked-background-color: var(--primary);
    --range-border-color: #24333e;
    --range-active-border-color: hsl(205deg, 25%, 23%);
    --range-thumb-border-color: var(--background-color);
    --range-thumb-color: var(--secondary);
    --range-thumb-hover-color: var(--secondary-hover);
    --range-thumb-active-color: var(--primary);
    --table-border-color: var(--muted-border-color);
    --table-row-stripped-background-color: rgba(115, 130, 140, 0.05);
    --code-background-color: #18232c;
    --code-color: var(--muted-color);
    --code-kbd-background-color: var(--contrast);
    --code-kbd-color: var(--contrast-inverse);
    --code-tag-color: hsl(330deg, 30%, 50%);
    --code-property-color: hsl(185deg, 30%, 50%);
    --code-value-color: hsl(40deg, 10%, 50%);
    --code-comment-color: #4d606d;
    --accordion-border-color: var(--muted-border-color);
    --accordion-active-summary-color: var(--primary);
    --accordion-close-summary-color: var(--color);
    --accordion-open-summary-color: var(--muted-color);
    --card-background-color: #141e26;
    --card-border-color: var(--card-background-color);
    --card-box-shadow: 0.0145rem 0.029rem 0.174rem rgba(0, 0, 0, 0.01698),
      0.0335rem 0.067rem 0.402rem rgba(0, 0, 0, 0.024),
      0.0625rem 0.125rem 0.75rem rgba(0, 0, 0, 0.03),
      0.1125rem 0.225rem 1.35rem rgba(0, 0, 0, 0.036),
      0.2085rem 0.417rem 2.502rem rgba(0, 0, 0, 0.04302),
      0.5rem 1rem 6rem rgba(0, 0, 0, 0.06), 0 0 0 0.0625rem rgba(0, 0, 0, 0.015);
    --card-sectionning-background-color: #18232c;
    --dropdown-background-color: hsl(205deg, 30%, 15%);
    --dropdown-border-color: #24333e;
    --dropdown-box-shadow: var(--card-box-shadow);
    --dropdown-color: var(--color);
    --dropdown-hover-background-color: rgba(36, 51, 62, 0.75);
    --modal-overlay-background-color: rgba(36, 51, 62, 0.8);
    --progress-background-color: #24333e;
    --progress-color: var(--primary);
    --loading-spinner-opacity: 0.5;
    --tooltip-background-color: var(--contrast);
    --tooltip-color: var(--contrast-inverse);
    --icon-checkbox: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='rgb(255, 255, 255)' stroke-width='4' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='20 6 9 17 4 12'%3E%3C/polyline%3E%3C/svg%3E");
    --icon-chevron: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='rgb(162, 175, 185)' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
    --icon-chevron-button: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='rgb(255, 255, 255)' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
    --icon-chevron-button-inverse: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='rgb(0, 0, 0)' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
    --icon-close: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='rgb(115, 130, 140)' stroke-width='4' stroke-linecap='round' stroke-linejoin='round'%3E%3Cline x1='18' y1='6' x2='6' y2='18'%3E%3C/line%3E%3Cline x1='6' y1='6' x2='18' y2='18'%3E%3C/line%3E%3C/svg%3E");
    --icon-date: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='rgb(162, 175, 185)' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Crect x='3' y='4' width='18' height='18' rx='2' ry='2'%3E%3C/rect%3E%3Cline x1='16' y1='2' x2='16' y2='6'%3E%3C/line%3E%3Cline x1='8' y1='2' x2='8' y2='6'%3E%3C/line%3E%3Cline x1='3' y1='10' x2='21' y2='10'%3E%3C/line%3E%3C/svg%3E");
    --icon-invalid: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='rgb(183, 28, 28)' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Ccircle cx='12' cy='12' r='10'%3E%3C/circle%3E%3Cline x1='12' y1='8' x2='12' y2='12'%3E%3C/line%3E%3Cline x1='12' y1='16' x2='12.01' y2='16'%3E%3C/line%3E%3C/svg%3E");
    --icon-minus: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='rgb(255, 255, 255)' stroke-width='4' stroke-linecap='round' stroke-linejoin='round'%3E%3Cline x1='5' y1='12' x2='19' y2='12'%3E%3C/line%3E%3C/svg%3E");
    --icon-search: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='rgb(162, 175, 185)' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Ccircle cx='11' cy='11' r='8'%3E%3C/circle%3E%3Cline x1='21' y1='21' x2='16.65' y2='16.65'%3E%3C/line%3E%3C/svg%3E");
    --icon-time: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='rgb(162, 175, 185)' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Ccircle cx='12' cy='12' r='10'%3E%3C/circle%3E%3Cpolyline points='12 6 12 12 16 14'%3E%3C/polyline%3E%3C/svg%3E");
    --icon-valid: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='rgb(46, 125, 50)' stroke-width='3' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='20 6 9 17 4 12'%3E%3C/polyline%3E%3C/svg%3E");
    --icon-share: url("data:image/svg+xml;charset=utf-8;base64,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");
    color-scheme: dark;
    --service-bg-hover:#22292F;
  }
}
[data-theme="dark"] {
  --background-color: #11191f;
  --background-light-green: #141e26;
  --color: hsl(205deg, 16%, 77%);
  --h1-color: hsl(205deg, 20%, 94%);
  --h2-color: #e1e6eb;
  --h3-color: hsl(205deg, 18%, 86%);
  --h4-color: #c8d1d8;
  --h5-color: hsl(205deg, 16%, 77%);
  --h6-color: #afbbc4;
  --muted-color: hsl(205deg, 10%, 50%);
  --muted-border-color: #1f2d38;
  --primary: hsl(195deg, 85%, 41%);
  --primary-hover: hsl(195deg, 80%, 50%);
  --primary-focus: rgba(16, 149, 193, 0.25);
  --primary-inverse: #fff;
  --secondary: hsl(205deg, 15%, 41%);
  --secondary-hover: hsl(205deg, 10%, 50%);
  --secondary-focus: rgba(115, 130, 140, 0.25);
  --secondary-inverse: #fff;
  --contrast: hsl(205deg, 20%, 94%);
  --contrast-hover: #fff;
  --contrast-focus: rgba(115, 130, 140, 0.25);
  --contrast-inverse: #000;
  --mark-background-color: #d1c284;
  --mark-color: #11191f;
  --ins-color: #388e3c;
  --del-color: #c62828;
  --blockquote-border-color: var(--muted-border-color);
  --blockquote-footer-color: var(--muted-color);
  --button-box-shadow: 0 0 0 rgba(0, 0, 0, 0);
  --button-hover-box-shadow: 0 0 0 rgba(0, 0, 0, 0);
  --form-element-background-color: #11191f;
  --form-element-border-color: #374956;
  --form-element-color: var(--color);
  --form-element-placeholder-color: var(--muted-color);
  --form-element-active-background-color: var(--form-element-background-color);
  --form-element-active-border-color: var(--primary);
  --form-element-focus-color: var(--primary-focus);
  --form-element-disabled-background-color: hsl(205deg, 25%, 23%);
  --form-element-disabled-border-color: hsl(205deg, 20%, 32%);
  --form-element-disabled-opacity: 0.5;
  --form-element-invalid-border-color: #b71c1c;
  --form-element-invalid-active-border-color: #c62828;
  --form-element-invalid-focus-color: rgba(198, 40, 40, 0.25);
  --form-element-valid-border-color: #2e7d32;
  --form-element-valid-active-border-color: #388e3c;
  --form-element-valid-focus-color: rgba(56, 142, 60, 0.25);
  --switch-background-color: #374956;
  --switch-color: var(--primary-inverse);
  --switch-checked-background-color: var(--primary);
  --range-border-color: #24333e;
  --range-active-border-color: hsl(205deg, 25%, 23%);
  --range-thumb-border-color: var(--background-color);
  --range-thumb-color: var(--secondary);
  --range-thumb-hover-color: var(--secondary-hover);
  --range-thumb-active-color: var(--primary);
  --table-border-color: var(--muted-border-color);
  --table-row-stripped-background-color: rgba(115, 130, 140, 0.05);
  --code-background-color: #18232c;
  --code-color: var(--muted-color);
  --code-kbd-background-color: var(--contrast);
  --code-kbd-color: var(--contrast-inverse);
  --code-tag-color: hsl(330deg, 30%, 50%);
  --code-property-color: hsl(185deg, 30%, 50%);
  --code-value-color: hsl(40deg, 10%, 50%);
  --code-comment-color: #4d606d;
  --accordion-border-color: var(--muted-border-color);
  --accordion-active-summary-color: var(--primary);
  --accordion-close-summary-color: var(--color);
  --accordion-open-summary-color: var(--muted-color);
  --card-background-color: #141e26;
  --card-border-color: var(--card-background-color);
  --card-box-shadow: 0.0145rem 0.029rem 0.174rem rgba(0, 0, 0, 0.01698),
    0.0335rem 0.067rem 0.402rem rgba(0, 0, 0, 0.024),
    0.0625rem 0.125rem 0.75rem rgba(0, 0, 0, 0.03),
    0.1125rem 0.225rem 1.35rem rgba(0, 0, 0, 0.036),
    0.2085rem 0.417rem 2.502rem rgba(0, 0, 0, 0.04302),
    0.5rem 1rem 6rem rgba(0, 0, 0, 0.06), 0 0 0 0.0625rem rgba(0, 0, 0, 0.015);
  --card-sectionning-background-color: #18232c;
  --dropdown-background-color: hsl(205deg, 30%, 15%);
  --dropdown-border-color: #24333e;
  --dropdown-box-shadow: var(--card-box-shadow);
  --dropdown-color: var(--color);
  --dropdown-hover-background-color: rgba(36, 51, 62, 0.75);
  --modal-overlay-background-color: rgba(36, 51, 62, 0.8);
  --progress-background-color: #24333e;
  --progress-color: var(--primary);
  --loading-spinner-opacity: 0.5;
  --tooltip-background-color: var(--contrast);
  --tooltip-color: var(--contrast-inverse);
  --icon-checkbox: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='rgb(255, 255, 255)' stroke-width='4' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='20 6 9 17 4 12'%3E%3C/polyline%3E%3C/svg%3E");
  --icon-chevron: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='rgb(162, 175, 185)' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
  --icon-chevron-button: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='rgb(255, 255, 255)' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
  --icon-chevron-button-inverse: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='rgb(0, 0, 0)' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
  --icon-close: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='rgb(115, 130, 140)' stroke-width='4' stroke-linecap='round' stroke-linejoin='round'%3E%3Cline x1='18' y1='6' x2='6' y2='18'%3E%3C/line%3E%3Cline x1='6' y1='6' x2='18' y2='18'%3E%3C/line%3E%3C/svg%3E");
  --icon-date: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='rgb(162, 175, 185)' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Crect x='3' y='4' width='18' height='18' rx='2' ry='2'%3E%3C/rect%3E%3Cline x1='16' y1='2' x2='16' y2='6'%3E%3C/line%3E%3Cline x1='8' y1='2' x2='8' y2='6'%3E%3C/line%3E%3Cline x1='3' y1='10' x2='21' y2='10'%3E%3C/line%3E%3C/svg%3E");
  --icon-invalid: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='rgb(183, 28, 28)' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Ccircle cx='12' cy='12' r='10'%3E%3C/circle%3E%3Cline x1='12' y1='8' x2='12' y2='12'%3E%3C/line%3E%3Cline x1='12' y1='16' x2='12.01' y2='16'%3E%3C/line%3E%3C/svg%3E");
  --icon-minus: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='rgb(255, 255, 255)' stroke-width='4' stroke-linecap='round' stroke-linejoin='round'%3E%3Cline x1='5' y1='12' x2='19' y2='12'%3E%3C/line%3E%3C/svg%3E");
  --icon-search: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='rgb(162, 175, 185)' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Ccircle cx='11' cy='11' r='8'%3E%3C/circle%3E%3Cline x1='21' y1='21' x2='16.65' y2='16.65'%3E%3C/line%3E%3C/svg%3E");
  --icon-time: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='rgb(162, 175, 185)' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Ccircle cx='12' cy='12' r='10'%3E%3C/circle%3E%3Cpolyline points='12 6 12 12 16 14'%3E%3C/polyline%3E%3C/svg%3E");
  --icon-valid: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='rgb(46, 125, 50)' stroke-width='3' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='20 6 9 17 4 12'%3E%3C/polyline%3E%3C/svg%3E");
  --icon-share: url("data:image/svg+xml;charset=utf-8;base64,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");
  color-scheme: dark;
}

progress,
[type="checkbox"],
[type="radio"],
[type="range"] {
  accent-color: var(--primary);
}

/**
 * Document
 * Content-box & Responsive typography
 */
*,
*::before,
*::after {
  box-sizing: border-box;
  background-repeat: no-repeat;
}

::before,
::after {
  text-decoration: inherit;
  vertical-align: inherit;
}

:where(#mount) {
  -webkit-tap-highlight-color: transparent;
  -webkit-text-size-adjust: 100%;
  -moz-text-size-adjust: 100%;
  text-size-adjust: 100%;
  background-color: var(--background-color);
  color: var(--color);
  font-weight: var(--font-weight);
  font-size: var(--font-size);
  line-height: var(--line-height);
  font-family: var(--font-family);
  text-rendering: optimizeLegibility;
  overflow-wrap: break-word;
  cursor: default;
  -moz-tab-size: 4;
  -o-tab-size: 4;
  tab-size: 4;
}

/**
 * Sectioning
 * Container and responsive spacings for header, main, footer
 */
main {
  display: block;
}

#mount {
  width: 100%;
  margin: 0;
}
#mount > header,
#mount > main,
#mount > footer {
  width: 100%;
  margin-right: auto;
  margin-left: auto;
  padding: var(--block-spacing-vertical) var(--block-spacing-horizontal);
}
@media (min-width: 576px) {
  #mount > header,
  #mount > main,
  #mount > footer {
    max-width: 510px;
    padding-right: 0;
    padding-left: 0;
  }
}
@media (min-width: 768px) {
  #mount > header,
  #mount > main,
  #mount > footer {
    max-width: 700px;
  }
}
@media (min-width: 992px) {
  #mount > header,
  #mount > main,
  #mount > footer {
    max-width: 920px;
  }
}
@media (min-width: 1200px) {
  #mount > header,
  #mount > main,
  #mount > footer {
    max-width: 1130px;
  }
}

/**
* Container
*/
.container,
.container-fluid {
  width: 100%;
  margin-right: auto;
  margin-left: auto;
  padding-right: var(--spacing);
  padding-left: var(--spacing);
}

@media (min-width: 576px) {
  .container {
    max-width: 510px;
    padding-right: 0;
    padding-left: 0;
  }
}
@media (min-width: 768px) {
  .container {
    max-width: 700px;
  }
}
@media (min-width: 992px) {
  .container {
    max-width: 920px;
  }
}
@media (min-width: 1200px) {
  .container {
    max-width: 1130px;
  }
}

/**
 * Section
 * Responsive spacings for section
 */
section {
  margin-bottom: var(--block-spacing-vertical);
}

/**
* Grid
* Minimal grid system with auto-layout columns
*/
.grid {
  grid-column-gap: var(--grid-spacing-horizontal);
  grid-row-gap: var(--grid-spacing-vertical);
  display: grid;
  grid-template-columns: 1fr;
  margin: 0;
}
@media (min-width: 992px) {
  .grid {
    grid-template-columns: repeat(auto-fit, minmax(0%, 1fr));
  }
}
.grid > * {
  min-width: 0;
}

/**
 * Horizontal scroller (<figure>)
 */
figure {
  display: block;
  margin: 0;
  padding: 0;
  overflow-x: auto;
}
figure figcaption {
  padding: calc(var(--spacing) * 0.5) 0;
  color: var(--muted-color);
}

/**
 * Typography
 */
b,
strong {
  font-weight: bolder;
}

sub,
sup {
  position: relative;
  font-size: 0.75em;
  line-height: 0;
  vertical-align: baseline;
}

sub {
  bottom: -0.25em;
}

sup {
  top: -0.5em;
}

address,
blockquote,
dl,
figure,
form,
ol,
p,
pre,
table,
ul {
  margin-top: 0;
  margin-bottom: var(--typography-spacing-vertical);
  color: var(--color);
  font-style: normal;
  font-weight: var(--font-weight);
  font-size: var(--font-size);
}

a,
[role="link"] {
  --color: var(--primary);
  --background-color: transparent;
  outline: none;
  background-color: var(--background-color);
  color: var(--color);
  -webkit-text-decoration: var(--text-decoration);
  text-decoration: var(--text-decoration);
  transition: background-color var(--transition), color var(--transition),
    box-shadow var(--transition), -webkit-text-decoration var(--transition);
  transition: background-color var(--transition), color var(--transition),
    text-decoration var(--transition), box-shadow var(--transition);
  transition: background-color var(--transition), color var(--transition),
    text-decoration var(--transition), box-shadow var(--transition),
    -webkit-text-decoration var(--transition);
}
a:is([aria-current], :hover, :active, :focus),
[role="link"]:is([aria-current], :hover, :active, :focus) {
  --color: var(--primary-hover);
  --text-decoration: underline;
}
a:focus,
[role="link"]:focus {
  --background-color: var(--primary-focus);
}
a.secondary,
[role="link"].secondary {
  --color: var(--secondary);
}
a.secondary:is([aria-current], :hover, :active, :focus),
[role="link"].secondary:is([aria-current], :hover, :active, :focus) {
  --color: var(--secondary-hover);
}
a.secondary:focus,
[role="link"].secondary:focus {
  --background-color: var(--secondary-focus);
}
a.contrast,
[role="link"].contrast {
  --color: var(--contrast);
}
a.contrast:is([aria-current], :hover, :active, :focus),
[role="link"].contrast:is([aria-current], :hover, :active, :focus) {
  --color: var(--contrast-hover);
}
a.contrast:focus,
[role="link"].contrast:focus {
  --background-color: var(--contrast-focus);
}

h1,
h2,
h3,
h4,
h5,
h6 {
  margin-top: 0;
  margin-bottom: var(--typography-spacing-vertical);
  color: var(--color);
  font-weight: var(--font-weight);
  font-size: var(--font-size);
  font-family: var(--font-family);
}

h1 {
  --color: var(--h1-color);
}

h2 {
  --color: var(--h2-color);
}

h3 {
  --color: var(--h3-color);
}

h4 {
  --color: var(--h4-color);
}

h5 {
  --color: var(--h5-color);
}

h6 {
  --color: var(--h6-color);
}

:where(address, blockquote, dl, figure, form, ol, p, pre, table, ul)
  ~ :is(h1, h2, h3, h4, h5, h6) {
  margin-top: var(--typography-spacing-vertical);
}

hgroup,
.headings {
  margin-bottom: var(--typography-spacing-vertical);
}
hgroup > *,
.headings > * {
  margin-bottom: 0;
}
hgroup > *:last-child,
.headings > *:last-child {
  --color: var(--muted-color);
  --font-weight: unset;
  font-size: 1rem;
  font-family: unset;
}

p {
  margin-bottom: var(--typography-spacing-vertical);
}

small {
  font-size: var(--font-size);
}

:where(dl, ol, ul) {
  padding-right: 0;
  padding-left: var(--spacing);
  -webkit-padding-start: var(--spacing);
  padding-inline-start: var(--spacing);
  -webkit-padding-end: 0;
  padding-inline-end: 0;
}
:where(dl, ol, ul) li {
  margin-bottom: calc(var(--typography-spacing-vertical) * 0.25);
}

:where(dl, ol, ul) :is(dl, ol, ul) {
  margin: 0;
  margin-top: calc(var(--typography-spacing-vertical) * 0.25);
}

ul li {
  list-style: square;
}

mark {
  padding: 0.125rem 0.25rem;
  background-color: var(--mark-background-color);
  color: var(--mark-color);
  vertical-align: baseline;
}

blockquote {
  display: block;
  margin: var(--typography-spacing-vertical) 0;
  padding: var(--spacing);
  border-right: none;
  border-left: 0.25rem solid var(--blockquote-border-color);
  -webkit-border-start: 0.25rem solid var(--blockquote-border-color);
  border-inline-start: 0.25rem solid var(--blockquote-border-color);
  -webkit-border-end: none;
  border-inline-end: none;
}
blockquote footer {
  margin-top: calc(var(--typography-spacing-vertical) * 0.5);
  color: var(--blockquote-footer-color);
}

abbr[title] {
  border-bottom: 1px dotted;
  text-decoration: none;
  cursor: help;
}

ins {
  color: var(--ins-color);
  text-decoration: none;
}

del {
  color: var(--del-color);
}

::-moz-selection {
  background-color: var(--primary-focus);
}

::selection {
  background-color: var(--primary-focus);
}

/**
 * Embedded content
 */
:where(audio, canvas, iframe, img, svg, video) {
  vertical-align: middle;
}

audio,
video {
  display: inline-block;
}

audio:not([controls]) {
  display: none;
  height: 0;
}

:where(iframe) {
  border-style: none;
}

img {
  max-width: 100%;
  height: auto;
  border-style: none;
}

:where(svg:not([fill])) {
  fill: currentColor;
}

svg:not(#mount) {
  overflow: hidden;
}

/**
 * Button
 */
button {
  margin: 0;
  overflow: visible;
  font-family: inherit;
  text-transform: none;
}

button,
[type="button"],
[type="reset"],
[type="submit"] {
  -webkit-appearance: button;
}

button {
  display: block;
  width: 100%;
  margin-bottom: var(--spacing);
}

[role="button"] {
  display: inline-block;
  text-decoration: none;
}

button,
input[type="submit"],
input[type="button"],
input[type="reset"],
[role="button"] {
  --background-color: var(--primary);
  --border-color: var(--primary);
  --color: var(--primary-inverse);
  --box-shadow: var(--button-box-shadow, 0 0 0 rgba(0, 0, 0, 0));
  padding: var(--form-element-spacing-vertical)
    var(--form-element-spacing-horizontal);
  border: var(--border-width) solid var(--border-color);
  border-radius: var(--border-radius);
  outline: none;
  background-color: var(--background-color);
  box-shadow: var(--box-shadow);
  color: var(--color);
  font-weight: var(--font-weight);
  font-size: 1rem;
  line-height: var(--line-height);
  text-align: center;
  cursor: pointer;
  transition: background-color var(--transition), border-color var(--transition),
    color var(--transition), box-shadow var(--transition);
}
button:is([aria-current], :hover, :active, :focus),
input[type="submit"]:is([aria-current], :hover, :active, :focus),
input[type="button"]:is([aria-current], :hover, :active, :focus),
input[type="reset"]:is([aria-current], :hover, :active, :focus),
[role="button"]:is([aria-current], :hover, :active, :focus) {
  --background-color: var(--primary-hover);
  --border-color: var(--primary-hover);
  --box-shadow: var(--button-hover-box-shadow, 0 0 0 rgba(0, 0, 0, 0));
  --color: var(--primary-inverse);
}
button:focus,
input[type="submit"]:focus,
input[type="button"]:focus,
input[type="reset"]:focus,
[role="button"]:focus {
  --box-shadow: var(--button-hover-box-shadow, 0 0 0 rgba(0, 0, 0, 0)),
    0 0 0 var(--outline-width) var(--primary-focus);
}

:is(
    button,
    input[type="submit"],
    input[type="button"],
    [role="button"]
  ).secondary,
input[type="reset"] {
  --background-color: var(--secondary);
  --border-color: var(--secondary);
  --color: var(--secondary-inverse);
  cursor: pointer;
}
:is(
    button,
    input[type="submit"],
    input[type="button"],
    [role="button"]
  ).secondary:is([aria-current], :hover, :active, :focus),
input[type="reset"]:is([aria-current], :hover, :active, :focus) {
  --background-color: var(--secondary-hover);
  --border-color: var(--secondary-hover);
  --color: var(--secondary-inverse);
}
:is(
    button,
    input[type="submit"],
    input[type="button"],
    [role="button"]
  ).secondary:focus,
input[type="reset"]:focus {
  --box-shadow: var(--button-hover-box-shadow, 0 0 0 rgba(0, 0, 0, 0)),
    0 0 0 var(--outline-width) var(--secondary-focus);
}

:is(
    button,
    input[type="submit"],
    input[type="button"],
    [role="button"]
  ).contrast {
  --background-color: var(--contrast);
  --border-color: var(--contrast);
  --color: var(--contrast-inverse);
}
:is(
    button,
    input[type="submit"],
    input[type="button"],
    [role="button"]
  ).contrast:is([aria-current], :hover, :active, :focus) {
  --background-color: var(--contrast-hover);
  --border-color: var(--contrast-hover);
  --color: var(--contrast-inverse);
}
:is(
    button,
    input[type="submit"],
    input[type="button"],
    [role="button"]
  ).contrast:focus {
  --box-shadow: var(--button-hover-box-shadow, 0 0 0 rgba(0, 0, 0, 0)),
    0 0 0 var(--outline-width) var(--contrast-focus);
}

:is(
    button,
    input[type="submit"],
    input[type="button"],
    [role="button"]
  ).outline,
input[type="reset"].outline {
  --background-color: transparent;
  --color: var(--primary);
}
:is(
    button,
    input[type="submit"],
    input[type="button"],
    [role="button"]
  ).outline:is([aria-current], :hover, :active, :focus),
input[type="reset"].outline:is([aria-current], :hover, :active, :focus) {
  --background-color: transparent;
  --color: var(--primary-hover);
}

:is(
    button,
    input[type="submit"],
    input[type="button"],
    [role="button"]
  ).outline.secondary,
input[type="reset"].outline {
  --color: var(--secondary);
}
:is(
    button,
    input[type="submit"],
    input[type="button"],
    [role="button"]
  ).outline.secondary:is([aria-current], :hover, :active, :focus),
input[type="reset"].outline:is([aria-current], :hover, :active, :focus) {
  --color: var(--secondary-hover);
}

:is(
    button,
    input[type="submit"],
    input[type="button"],
    [role="button"]
  ).outline.contrast {
  --color: var(--contrast);
}
:is(
    button,
    input[type="submit"],
    input[type="button"],
    [role="button"]
  ).outline.contrast:is([aria-current], :hover, :active, :focus) {
  --color: var(--contrast-hover);
}

:where(
    button,
    [type="submit"],
    [type="button"],
    [type="reset"],
    [role="button"]
  )[disabled],
:where(fieldset[disabled])
  :is(
    button,
    [type="submit"],
    [type="button"],
    [type="reset"],
    [role="button"]
  ),
a[role="button"]:not([href]) {
  opacity: 0.5;
  pointer-events: none;
}

/**
 * Form elements
 */
input,
optgroup,
select,
textarea {
  margin: 0;
  font-size: 1rem;
  line-height: var(--line-height);
  font-family: inherit;
  letter-spacing: inherit;
}

input {
  overflow: visible;
}

select {
  text-transform: none;
}

legend {
  max-width: 100%;
  padding: 0;
  color: inherit;
  white-space: normal;
}

textarea {
  overflow: auto;
}

[type="checkbox"],
[type="radio"] {
  padding: 0;
}

::-webkit-inner-spin-button,
::-webkit-outer-spin-button {
  height: auto;
}

[type="search"] {
  -webkit-appearance: textfield;
  outline-offset: -2px;
}

[type="search"]::-webkit-search-decoration {
  -webkit-appearance: none;
}

::-webkit-file-upload-button {
  -webkit-appearance: button;
  font: inherit;
}

::-moz-focus-inner {
  padding: 0;
  border-style: none;
}

:-moz-focusring {
  outline: none;
}

:-moz-ui-invalid {
  box-shadow: none;
}

::-ms-expand {
  display: none;
}

[type="file"],
[type="range"] {
  padding: 0;
  border-width: 0;
}

input:not([type="checkbox"], [type="radio"], [type="range"]) {
  height: calc(
    1rem * var(--line-height) + var(--form-element-spacing-vertical) * 2 +
      var(--border-width) * 2
  );
}

fieldset {
  margin: 0;
  margin-bottom: var(--spacing);
  padding: 0;
  border: 0;
}

label,
fieldset legend {
  display: block;
  margin-bottom: calc(var(--spacing) * 0.25);
  font-weight: var(--form-label-font-weight, var(--font-weight));
}

input:not([type="checkbox"], [type="radio"]),
select,
textarea {
  width: 100%;
}

input:not([type="checkbox"], [type="radio"], [type="range"], [type="file"]),
select,
textarea {
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  padding: var(--form-element-spacing-vertical)
    var(--form-element-spacing-horizontal);
}

input,
select,
textarea {
  --background-color: var(--form-element-background-color);
  --border-color: var(--form-element-border-color);
  --color: var(--form-element-color);
  --box-shadow: none;
  border: var(--border-width) solid var(--border-color);
  border-radius: var(--border-radius);
  outline: none;
  background-color: var(--background-color);
  box-shadow: var(--box-shadow);
  color: var(--color);
  font-weight: var(--font-weight);
  transition: background-color var(--transition), border-color var(--transition),
    color var(--transition), box-shadow var(--transition);
}

input:not(
    [type="submit"],
    [type="button"],
    [type="reset"],
    [type="checkbox"],
    [type="radio"],
    [readonly]
  ):is(:active, :focus),
:where(select, textarea):is(:active, :focus) {
  --background-color: var(--form-element-active-background-color);
}

input:not(
    [type="submit"],
    [type="button"],
    [type="reset"],
    [role="switch"],
    [readonly]
  ):is(:active, :focus),
:where(select, textarea):is(:active, :focus) {
  --border-color: var(--form-element-active-border-color);
}

input:not(
    [type="submit"],
    [type="button"],
    [type="reset"],
    [type="range"],
    [type="file"],
    [readonly]
  ):focus,
select:focus,
textarea:focus {
  --box-shadow: 0 0 0 var(--outline-width) var(--form-element-focus-color);
}

input:not([type="submit"], [type="button"], [type="reset"])[disabled],
select[disabled],
textarea[disabled],
:where(fieldset[disabled])
  :is(
    input:not([type="submit"], [type="button"], [type="reset"]),
    select,
    textarea
  ) {
  --background-color: var(--form-element-disabled-background-color);
  --border-color: var(--form-element-disabled-border-color);
  opacity: var(--form-element-disabled-opacity);
  pointer-events: none;
}

:where(input, select, textarea):not(
    [type="checkbox"],
    [type="radio"],
    [type="date"],
    [type="datetime-local"],
    [type="month"],
    [type="time"],
    [type="week"]
  )[aria-invalid] {
  padding-right: calc(
    var(--form-element-spacing-horizontal) + 1.5rem
  ) !important;
  padding-left: var(--form-element-spacing-horizontal);
  -webkit-padding-start: var(--form-element-spacing-horizontal) !important;
  padding-inline-start: var(--form-element-spacing-horizontal) !important;
  -webkit-padding-end: calc(
    var(--form-element-spacing-horizontal) + 1.5rem
  ) !important;
  padding-inline-end: calc(
    var(--form-element-spacing-horizontal) + 1.5rem
  ) !important;
  background-position: center right 0.75rem;
  background-size: 1rem auto;
  background-repeat: no-repeat;
}
:where(input, select, textarea):not(
    [type="checkbox"],
    [type="radio"],
    [type="date"],
    [type="datetime-local"],
    [type="month"],
    [type="time"],
    [type="week"]
  )[aria-invalid="false"] {
  background-image: var(--icon-valid);
}
:where(input, select, textarea):not(
    [type="checkbox"],
    [type="radio"],
    [type="date"],
    [type="datetime-local"],
    [type="month"],
    [type="time"],
    [type="week"]
  )[aria-invalid="true"] {
  background-image: var(--icon-invalid);
}
:where(input, select, textarea)[aria-invalid="false"] {
  --border-color: var(--form-element-valid-border-color);
}
:where(input, select, textarea)[aria-invalid="false"]:is(:active, :focus) {
  --border-color: var(--form-element-valid-active-border-color) !important;
  --box-shadow: 0 0 0 var(--outline-width) var(--form-element-valid-focus-color) !important;
}
:where(input, select, textarea)[aria-invalid="true"] {
  --border-color: var(--form-element-invalid-border-color);
}
:where(input, select, textarea)[aria-invalid="true"]:is(:active, :focus) {
  --border-color: var(--form-element-invalid-active-border-color) !important;
  --box-shadow: 0 0 0 var(--outline-width)
    var(--form-element-invalid-focus-color) !important;
}

[dir="rtl"]
  :where(input, select, textarea):not([type="checkbox"], [type="radio"]):is(
    [aria-invalid],
    [aria-invalid="true"],
    [aria-invalid="false"]
  ) {
  background-position: center left 0.75rem;
}

input::placeholder,
input::-webkit-input-placeholder,
textarea::placeholder,
textarea::-webkit-input-placeholder,
select:invalid {
  color: var(--form-element-placeholder-color);
  opacity: 1;
}

input:not([type="checkbox"], [type="radio"]),
select,
textarea {
  margin-bottom: var(--spacing);
}

select::-ms-expand {
  border: 0;
  background-color: transparent;
}
select:not([multiple], [size]) {
  padding-right: calc(var(--form-element-spacing-horizontal) + 1.5rem);
  padding-left: var(--form-element-spacing-horizontal);
  -webkit-padding-start: var(--form-element-spacing-horizontal);
  padding-inline-start: var(--form-element-spacing-horizontal);
  -webkit-padding-end: calc(var(--form-element-spacing-horizontal) + 1.5rem);
  padding-inline-end: calc(var(--form-element-spacing-horizontal) + 1.5rem);
  background-image: var(--icon-chevron);
  background-position: center right 0.75rem;
  background-size: 1rem auto;
  background-repeat: no-repeat;
}

[dir="rtl"] select:not([multiple], [size]) {
  background-position: center left 0.75rem;
}

:where(input, select, textarea) + small {
  display: block;
  width: 100%;
  margin-top: calc(var(--spacing) * -0.75);
  margin-bottom: var(--spacing);
  color: var(--muted-color);
}

label > :where(input, select, textarea) {
  margin-top: calc(var(--spacing) * 0.25);
}

/**
 * Form elements
 * Checkboxes & Radios
 */
[type="checkbox"],
[type="radio"] {
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  width: 1.25em;
  height: 1.25em;
  margin-top: -0.125em;
  margin-right: 0.375em;
  margin-left: 0;
  -webkit-margin-start: 0;
  margin-inline-start: 0;
  -webkit-margin-end: 0.375em;
  margin-inline-end: 0.375em;
  border-width: var(--border-width);
  font-size: inherit;
  vertical-align: middle;
  cursor: pointer;
}
[type="checkbox"]::-ms-check,
[type="radio"]::-ms-check {
  display: none;
}
[type="checkbox"]:checked,
[type="checkbox"]:checked:active,
[type="checkbox"]:checked:focus,
[type="radio"]:checked,
[type="radio"]:checked:active,
[type="radio"]:checked:focus {
  --background-color: var(--primary);
  --border-color: var(--primary);
  background-image: var(--icon-checkbox);
  background-position: center;
  background-size: 0.75em auto;
  background-repeat: no-repeat;
}
[type="checkbox"] ~ label,
[type="radio"] ~ label {
  display: inline-block;
  margin-right: 0.375em;
  margin-bottom: 0;
  cursor: pointer;
}

[type="checkbox"]:indeterminate {
  --background-color: var(--primary);
  --border-color: var(--primary);
  background-image: var(--icon-minus);
  background-position: center;
  background-size: 0.75em auto;
  background-repeat: no-repeat;
}

[type="radio"] {
  border-radius: 50%;
}
[type="radio"]:checked,
[type="radio"]:checked:active,
[type="radio"]:checked:focus {
  --background-color: var(--primary-inverse);
  border-width: 0.35em;
  background-image: none;
}

[type="checkbox"][role="switch"] {
  --background-color: var(--switch-background-color);
  --border-color: var(--switch-background-color);
  --color: var(--switch-color);
  width: 2.25em;
  height: 1.25em;
  border: var(--border-width) solid var(--border-color);
  border-radius: 1.25em;
  background-color: var(--background-color);
  line-height: 1.25em;
}
[type="checkbox"][role="switch"]:focus {
  --background-color: var(--switch-background-color);
  --border-color: var(--switch-background-color);
}
[type="checkbox"][role="switch"]:checked {
  --background-color: var(--switch-checked-background-color);
  --border-color: var(--switch-checked-background-color);
}
[type="checkbox"][role="switch"]:before {
  display: block;
  width: calc(1.25em - (var(--border-width) * 2));
  height: 100%;
  border-radius: 50%;
  background-color: var(--color);
  content: "";
  transition: margin 0.1s ease-in-out;
}
[type="checkbox"][role="switch"]:checked {
  background-image: none;
}
[type="checkbox"][role="switch"]:checked::before {
  margin-left: calc(1.125em - var(--border-width));
  -webkit-margin-start: calc(1.125em - var(--border-width));
  margin-inline-start: calc(1.125em - var(--border-width));
}

[type="checkbox"][aria-invalid="false"],
[type="checkbox"]:checked[aria-invalid="false"],
[type="radio"][aria-invalid="false"],
[type="radio"]:checked[aria-invalid="false"],
[type="checkbox"][role="switch"][aria-invalid="false"],
[type="checkbox"][role="switch"]:checked[aria-invalid="false"] {
  --border-color: var(--form-element-valid-border-color);
}
[type="checkbox"][aria-invalid="true"],
[type="checkbox"]:checked[aria-invalid="true"],
[type="radio"][aria-invalid="true"],
[type="radio"]:checked[aria-invalid="true"],
[type="checkbox"][role="switch"][aria-invalid="true"],
[type="checkbox"][role="switch"]:checked[aria-invalid="true"] {
  --border-color: var(--form-element-invalid-border-color);
}

/**
 * Form elements
 * Alternatives input types (Not Checkboxes & Radios)
 */
[type="color"]::-webkit-color-swatch-wrapper {
  padding: 0;
}
[type="color"]::-moz-focus-inner {
  padding: 0;
}
[type="color"]::-webkit-color-swatch {
  border: 0;
  border-radius: calc(var(--border-radius) * 0.5);
}
[type="color"]::-moz-color-swatch {
  border: 0;
  border-radius: calc(var(--border-radius) * 0.5);
}

input:not([type="checkbox"], [type="radio"], [type="range"], [type="file"]):is(
    [type="date"],
    [type="datetime-local"],
    [type="month"],
    [type="time"],
    [type="week"]
  ) {
  --icon-position: 0.75rem;
  --icon-width: 1rem;
  padding-right: calc(var(--icon-width) + var(--icon-position));
  background-image: var(--icon-date);
  background-position: center right var(--icon-position);
  background-size: var(--icon-width) auto;
  background-repeat: no-repeat;
}
input:not(
    [type="checkbox"],
    [type="radio"],
    [type="range"],
    [type="file"]
  )[type="time"] {
  background-image: var(--icon-time);
}

[type="date"]::-webkit-calendar-picker-indicator,
[type="datetime-local"]::-webkit-calendar-picker-indicator,
[type="month"]::-webkit-calendar-picker-indicator,
[type="time"]::-webkit-calendar-picker-indicator,
[type="week"]::-webkit-calendar-picker-indicator {
  width: var(--icon-width);
  margin-right: calc(var(--icon-width) * -1);
  margin-left: var(--icon-position);
  opacity: 0;
}

[dir="rtl"]
  :is(
    [type="date"],
    [type="datetime-local"],
    [type="month"],
    [type="time"],
    [type="week"]
  ) {
  text-align: right;
}

[type="file"] {
  --color: var(--muted-color);
  padding: calc(var(--form-element-spacing-vertical) * 0.5) 0;
  border: 0;
  border-radius: 0;
  background: none;
}
[type="file"]::file-selector-button {
  --background-color: var(--secondary);
  --border-color: var(--secondary);
  --color: var(--secondary-inverse);
  margin-right: calc(var(--spacing) / 2);
  margin-left: 0;
  -webkit-margin-start: 0;
  margin-inline-start: 0;
  -webkit-margin-end: calc(var(--spacing) / 2);
  margin-inline-end: calc(var(--spacing) / 2);
  padding: calc(var(--form-element-spacing-vertical) * 0.5)
    calc(var(--form-element-spacing-horizontal) * 0.5);
  border: var(--border-width) solid var(--border-color);
  border-radius: var(--border-radius);
  outline: none;
  background-color: var(--background-color);
  box-shadow: var(--box-shadow);
  color: var(--color);
  font-weight: var(--font-weight);
  font-size: 1rem;
  line-height: var(--line-height);
  text-align: center;
  cursor: pointer;
  transition: background-color var(--transition), border-color var(--transition),
    color var(--transition), box-shadow var(--transition);
}
[type="file"]::file-selector-button:is(:hover, :active, :focus) {
  --background-color: var(--secondary-hover);
  --border-color: var(--secondary-hover);
}
[type="file"]::-webkit-file-upload-button {
  --background-color: var(--secondary);
  --border-color: var(--secondary);
  --color: var(--secondary-inverse);
  margin-right: calc(var(--spacing) / 2);
  margin-left: 0;
  -webkit-margin-start: 0;
  margin-inline-start: 0;
  -webkit-margin-end: calc(var(--spacing) / 2);
  margin-inline-end: calc(var(--spacing) / 2);
  padding: calc(var(--form-element-spacing-vertical) * 0.5)
    calc(var(--form-element-spacing-horizontal) * 0.5);
  border: var(--border-width) solid var(--border-color);
  border-radius: var(--border-radius);
  outline: none;
  background-color: var(--background-color);
  box-shadow: var(--box-shadow);
  color: var(--color);
  font-weight: var(--font-weight);
  font-size: 1rem;
  line-height: var(--line-height);
  text-align: center;
  cursor: pointer;
  -webkit-transition: background-color var(--transition),
    border-color var(--transition), color var(--transition),
    box-shadow var(--transition);
  transition: background-color var(--transition), border-color var(--transition),
    color var(--transition), box-shadow var(--transition);
}
[type="file"]::-webkit-file-upload-button:is(:hover, :active, :focus) {
  --background-color: var(--secondary-hover);
  --border-color: var(--secondary-hover);
}
[type="file"]::-ms-browse {
  --background-color: var(--secondary);
  --border-color: var(--secondary);
  --color: var(--secondary-inverse);
  margin-right: calc(var(--spacing) / 2);
  margin-left: 0;
  margin-inline-start: 0;
  margin-inline-end: calc(var(--spacing) / 2);
  padding: calc(var(--form-element-spacing-vertical) * 0.5)
    calc(var(--form-element-spacing-horizontal) * 0.5);
  border: var(--border-width) solid var(--border-color);
  border-radius: var(--border-radius);
  outline: none;
  background-color: var(--background-color);
  box-shadow: var(--box-shadow);
  color: var(--color);
  font-weight: var(--font-weight);
  font-size: 1rem;
  line-height: var(--line-height);
  text-align: center;
  cursor: pointer;
  -ms-transition: background-color var(--transition),
    border-color var(--transition), color var(--transition),
    box-shadow var(--transition);
  transition: background-color var(--transition), border-color var(--transition),
    color var(--transition), box-shadow var(--transition);
}
[type="file"]::-ms-browse:is(:hover, :active, :focus) {
  --background-color: var(--secondary-hover);
  --border-color: var(--secondary-hover);
}

[type="range"] {
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  width: 100%;
  height: 1.25rem;
  background: none;
}
[type="range"]::-webkit-slider-runnable-track {
  width: 100%;
  height: 0.25rem;
  border-radius: var(--border-radius);
  background-color: var(--range-border-color);
  -webkit-transition: background-color var(--transition),
    box-shadow var(--transition);
  transition: background-color var(--transition), box-shadow var(--transition);
}
[type="range"]::-moz-range-track {
  width: 100%;
  height: 0.25rem;
  border-radius: var(--border-radius);
  background-color: var(--range-border-color);
  -moz-transition: background-color var(--transition),
    box-shadow var(--transition);
  transition: background-color var(--transition), box-shadow var(--transition);
}
[type="range"]::-ms-track {
  width: 100%;
  height: 0.25rem;
  border-radius: var(--border-radius);
  background-color: var(--range-border-color);
  -ms-transition: background-color var(--transition),
    box-shadow var(--transition);
  transition: background-color var(--transition), box-shadow var(--transition);
}
[type="range"]::-webkit-slider-thumb {
  -webkit-appearance: none;
  width: 1.25rem;
  height: 1.25rem;
  margin-top: -0.5rem;
  border: 2px solid var(--range-thumb-border-color);
  border-radius: 50%;
  background-color: var(--range-thumb-color);
  cursor: pointer;
  -webkit-transition: background-color var(--transition),
    transform var(--transition);
  transition: background-color var(--transition), transform var(--transition);
}
[type="range"]::-moz-range-thumb {
  -webkit-appearance: none;
  width: 1.25rem;
  height: 1.25rem;
  margin-top: -0.5rem;
  border: 2px solid var(--range-thumb-border-color);
  border-radius: 50%;
  background-color: var(--range-thumb-color);
  cursor: pointer;
  -moz-transition: background-color var(--transition),
    transform var(--transition);
  transition: background-color var(--transition), transform var(--transition);
}
[type="range"]::-ms-thumb {
  -webkit-appearance: none;
  width: 1.25rem;
  height: 1.25rem;
  margin-top: -0.5rem;
  border: 2px solid var(--range-thumb-border-color);
  border-radius: 50%;
  background-color: var(--range-thumb-color);
  cursor: pointer;
  -ms-transition: background-color var(--transition),
    transform var(--transition);
  transition: background-color var(--transition), transform var(--transition);
}
[type="range"]:hover,
[type="range"]:focus {
  --range-border-color: var(--range-active-border-color);
  --range-thumb-color: var(--range-thumb-hover-color);
}
[type="range"]:active {
  --range-thumb-color: var(--range-thumb-active-color);
}
[type="range"]:active::-webkit-slider-thumb {
  transform: scale(1.25);
}
[type="range"]:active::-moz-range-thumb {
  transform: scale(1.25);
}
[type="range"]:active::-ms-thumb {
  transform: scale(1.25);
}

input:not(
    [type="checkbox"],
    [type="radio"],
    [type="range"],
    [type="file"]
  )[type="search"] {
  -webkit-padding-start: calc(var(--form-element-spacing-horizontal) + 1.75rem);
  padding-inline-start: calc(var(--form-element-spacing-horizontal) + 1.75rem);
  border-radius: 5rem;
  background-image: var(--icon-search);
  background-position: center left 1.125rem;
  background-size: 1rem auto;
  background-repeat: no-repeat;
}
input:not(
    [type="checkbox"],
    [type="radio"],
    [type="range"],
    [type="file"]
  )[type="search"][aria-invalid] {
  -webkit-padding-start: calc(
    var(--form-element-spacing-horizontal) + 1.75rem
  ) !important;
  padding-inline-start: calc(
    var(--form-element-spacing-horizontal) + 1.75rem
  ) !important;
  background-position: center left 1.125rem, center right 0.75rem;
}
input:not(
    [type="checkbox"],
    [type="radio"],
    [type="range"],
    [type="file"]
  )[type="search"][aria-invalid="false"] {
  background-image: var(--icon-search), var(--icon-valid);
}
input:not(
    [type="checkbox"],
    [type="radio"],
    [type="range"],
    [type="file"]
  )[type="search"][aria-invalid="true"] {
  background-image: var(--icon-search), var(--icon-invalid);
}

[type="search"]::-webkit-search-cancel-button {
  -webkit-appearance: none;
  display: none;
}

[dir="rtl"]
  :where(input):not(
    [type="checkbox"],
    [type="radio"],
    [type="range"],
    [type="file"]
  )[type="search"] {
  background-position: center right 1.125rem;
}
[dir="rtl"]
  :where(input):not(
    [type="checkbox"],
    [type="radio"],
    [type="range"],
    [type="file"]
  )[type="search"][aria-invalid] {
  background-position: center right 1.125rem, center left 0.75rem;
}

/**
 * Table
 */
:where(table) {
  width: 100%;
  border-collapse: collapse;
  border-spacing: 0;
  text-indent: 0;
}

th,
td {
  padding: calc(var(--spacing) / 2) var(--spacing);
  border-bottom: var(--border-width) solid var(--table-border-color);
  color: var(--color);
  font-weight: var(--font-weight);
  font-size: var(--font-size);
  text-align: left;
  text-align: start;
}

tfoot th,
tfoot td {
  border-top: var(--border-width) solid var(--table-border-color);
  border-bottom: 0;
}

table[role="grid"] tbody tr:nth-child(odd) {
  background-color: var(--table-row-stripped-background-color);
}

/**
 * Code
 */
pre,
code,
kbd,
samp {
  font-size: 0.875em;
  font-family: var(--font-family);
}

pre {
  -ms-overflow-style: scrollbar;
  overflow: auto;
}

pre,
code,
kbd {
  border-radius: var(--border-radius);
  background: var(--code-background-color);
  color: var(--code-color);
  font-weight: var(--font-weight);
  line-height: initial;
}

code,
kbd {
  display: inline-block;
  padding: 0.375rem 0.5rem;
}

pre {
  display: block;
  margin-bottom: var(--spacing);
  overflow-x: auto;
}
pre > code {
  display: block;
  padding: var(--spacing);
  background: none;
  font-size: 14px;
  line-height: var(--line-height);
}

code b {
  color: var(--code-tag-color);
  font-weight: var(--font-weight);
}
code i {
  color: var(--code-property-color);
  font-style: normal;
}
code u {
  color: var(--code-value-color);
  text-decoration: none;
}
code em {
  color: var(--code-comment-color);
  font-style: normal;
}

kbd {
  background-color: var(--code-kbd-background-color);
  color: var(--code-kbd-color);
  vertical-align: baseline;
}

/**
 * Miscs
 */
hr {
  height: 0;
  border: 0;
  border-top: 1px solid var(--muted-border-color);
  color: inherit;
}

[hidden],
template {
  display: none !important;
}

canvas {
  display: inline-block;
}

/**
 * Accordion (<details>)
 */
details {
  display: block;
  margin-bottom: var(--spacing);
  padding-bottom: var(--spacing);
  border-bottom: var(--border-width) solid var(--accordion-border-color);
}
details summary {
  line-height: 1rem;
  list-style-type: none;
  cursor: pointer;
  transition: color var(--transition);
}
details summary:not([role]) {
  color: var(--accordion-close-summary-color);
}
details summary::-webkit-details-marker {
  display: none;
}
details summary::marker {
  display: none;
}
details summary::-moz-list-bullet {
  list-style-type: none;
}
details summary::after {
  display: block;
  width: 1rem;
  height: 1rem;
  -webkit-margin-start: calc(var(--spacing, 1rem) * 0.5);
  margin-inline-start: calc(var(--spacing, 1rem) * 0.5);
  float: right;
  transform: rotate(-90deg);
  background-image: var(--icon-chevron);
  background-position: right center;
  background-size: 1rem auto;
  background-repeat: no-repeat;
  content: "";
  transition: transform var(--transition);
}
details summary:focus {
  outline: none;
}
details summary:focus:not([role="button"]) {
  color: var(--accordion-active-summary-color);
}
details summary[role="button"] {
  width: 100%;
  text-align: left;
}
details summary[role="button"]::after {
  height: calc(1rem * var(--line-height, 1.5));
  background-image: var(--icon-chevron-button);
}
details summary[role="button"]:not(.outline).contrast::after {
  background-image: var(--icon-chevron-button-inverse);
}
details[open] > summary {
  margin-bottom: calc(var(--spacing));
}
details[open] > summary:not([role]):not(:focus) {
  color: var(--accordion-open-summary-color);
}
details[open] > summary::after {
  transform: rotate(0);
}

[dir="rtl"] details summary {
  text-align: right;
}
[dir="rtl"] details summary::after {
  float: left;
  background-position: left center;
}

/**
 * Card (<article>)
 */
article {
  margin: var(--block-spacing-vertical) 0;
  padding: var(--block-spacing-vertical) var(--block-spacing-horizontal);
  border-radius: var(--border-radius);
  background: var(--card-background-color);
  box-shadow: var(--card-box-shadow);
}
article > header,
article > footer {
  margin-right: calc(var(--block-spacing-horizontal) * -1);
  margin-left: calc(var(--block-spacing-horizontal) * -1);
  padding: calc(var(--block-spacing-vertical) * 0.66)
    var(--block-spacing-horizontal);
  background-color: var(--card-sectionning-background-color);
}
article > header {
  margin-top: calc(var(--block-spacing-vertical) * -1);
  margin-bottom: var(--block-spacing-vertical);
  border-bottom: var(--border-width) solid var(--card-border-color);
  border-top-right-radius: var(--border-radius);
  border-top-left-radius: var(--border-radius);
}
article > footer {
  margin-top: var(--block-spacing-vertical);
  margin-bottom: calc(var(--block-spacing-vertical) * -1);
  border-top: var(--border-width) solid var(--card-border-color);
  border-bottom-right-radius: var(--border-radius);
  border-bottom-left-radius: var(--border-radius);
}

/**
 * Modal (<dialog>)
 */
#mount {
  --scrollbar-width: 0px;
}

dialog {
  display: flex;
  z-index: 999;
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  align-items: center;
  justify-content: center;
  width: inherit;
  min-width: 100%;
  height: inherit;
  min-height: 100%;
  padding: var(--spacing);
  border: 0;
  -webkit-backdrop-filter: var(--modal-overlay-backdrop-filter);
  backdrop-filter: var(--modal-overlay-backdrop-filter);
  background-color: var(--modal-overlay-background-color);
  color: var(--color);
}
dialog article {
  max-height: calc(100vh - var(--spacing) * 2);
  overflow: auto;
}
@media (min-width: 576px) {
  dialog article {
    max-width: 510px;
  }
}
@media (min-width: 768px) {
  dialog article {
    max-width: 700px;
  }
}
dialog article > header,
dialog article > footer {
  padding: calc(var(--block-spacing-vertical) * 0.5)
    var(--block-spacing-horizontal);
}
dialog article > header .close {
  margin: 0;
  margin-left: var(--spacing);
  float: right;
}
dialog article > footer {
  text-align: right;
}
dialog article > footer [role="button"] {
  margin-bottom: 0;
}
dialog article > footer [role="button"]:not(:first-of-type) {
  margin-left: calc(var(--spacing) * 0.5);
}
dialog article p:last-of-type {
  margin: 0;
}
dialog article .close {
  display: block;
  width: 1rem;
  height: 1rem;
  margin-top: calc(var(--block-spacing-vertical) * -0.5);
  margin-bottom: var(--typography-spacing-vertical);
  margin-left: auto;
  background-image: var(--icon-close);
  background-position: center;
  background-size: auto 1rem;
  background-repeat: no-repeat;
  opacity: 0.5;
  transition: opacity var(--transition);
}
dialog article .close:is([aria-current], :hover, :active, :focus) {
  opacity: 1;
}
dialog:not([open]),
dialog[open="false"] {
  display: none;
}

.modal-is-open {
  padding-right: var(--scrollbar-width, 0px);
  overflow: hidden;
  pointer-events: none;
}
.modal-is-open dialog {
  pointer-events: auto;
}

:where(.modal-is-opening, .modal-is-closing) dialog,
:where(.modal-is-opening, .modal-is-closing) dialog > article {
  animation-duration: 0.2s;
  animation-timing-function: ease-in-out;
  animation-fill-mode: both;
}
:where(.modal-is-opening, .modal-is-closing) dialog {
  animation-duration: 0.8s;
  animation-name: modal-overlay;
}
:where(.modal-is-opening, .modal-is-closing) dialog > article {
  animation-delay: 0.2s;
  animation-name: modal;
}

.modal-is-closing dialog,
.modal-is-closing dialog > article {
  animation-delay: 0s;
  animation-direction: reverse;
}

@keyframes modal-overlay {
  from {
    -webkit-backdrop-filter: none;
    backdrop-filter: none;
    background-color: transparent;
  }
}
@keyframes modal {
  from {
    transform: translateY(-100%);
    opacity: 0;
  }
}
/**
 * Nav
 */
:where(nav li)::before {
  float: left;
  content: "​";
}

nav,
nav ul {
  display: flex;
}

nav {
  justify-content: space-between;
}
nav ol,
nav ul {
  align-items: center;
  margin-bottom: 0;
  padding: 0;
  list-style: none;
}
nav ol:first-of-type,
nav ul:first-of-type {
  margin-left: calc(var(--nav-element-spacing-horizontal) * -1);
}
nav ol:last-of-type,
nav ul:last-of-type {
  margin-right: calc(var(--nav-element-spacing-horizontal) * -1);
}
nav li {
  display: inline-block;
  margin: 0;
  padding: var(--nav-element-spacing-vertical)
    var(--nav-element-spacing-horizontal);
}
nav li > * {
  --spacing: 0;
}
nav :where(a, [role="link"]) {
  display: inline-block;
  margin: calc(var(--nav-link-spacing-vertical) * -1)
    calc(var(--nav-link-spacing-horizontal) * -1);
  padding: var(--nav-link-spacing-vertical) var(--nav-link-spacing-horizontal);
  border-radius: var(--border-radius);
  text-decoration: none;
}
nav :where(a, [role="link"]):is([aria-current], :hover, :active, :focus) {
  text-decoration: none;
}
nav[aria-label="breadcrumb"] {
  align-items: center;
  justify-content: start;
}
nav[aria-label="breadcrumb"] ul li:not(:first-child) {
  -webkit-margin-start: var(--nav-link-spacing-horizontal);
  margin-inline-start: var(--nav-link-spacing-horizontal);
}
nav[aria-label="breadcrumb"] ul li:not(:last-child) ::after {
  position: absolute;
  width: calc(var(--nav-link-spacing-horizontal) * 2);
  -webkit-margin-start: calc(var(--nav-link-spacing-horizontal) / 2);
  margin-inline-start: calc(var(--nav-link-spacing-horizontal) / 2);
  content: "/";
  color: var(--muted-color);
  text-align: center;
}
nav[aria-label="breadcrumb"] a[aria-current] {
  background-color: transparent;
  color: inherit;
  text-decoration: none;
  pointer-events: none;
}
nav [role="button"] {
  margin-right: inherit;
  margin-left: inherit;
  padding: var(--nav-link-spacing-vertical) var(--nav-link-spacing-horizontal);
}

aside nav,
aside ol,
aside ul,
aside li {
  display: block;
}
aside li {
  padding: calc(var(--nav-element-spacing-vertical) * 0.5)
    var(--nav-element-spacing-horizontal);
}
aside li a {
  display: block;
}
aside li [role="button"] {
  margin: inherit;
}

[dir="rtl"] nav[aria-label="breadcrumb"] ul li:not(:last-child) ::after {
  content: "\\";
}

/**
 * Progress
 */
progress {
  display: inline-block;
  vertical-align: baseline;
}

progress {
  -webkit-appearance: none;
  -moz-appearance: none;
  display: inline-block;
  appearance: none;
  width: 100%;
  height: 0.5rem;
  margin-bottom: calc(var(--spacing) * 0.5);
  overflow: hidden;
  border: 0;
  border-radius: var(--border-radius);
  background-color: var(--progress-background-color);
  color: var(--progress-color);
}
progress::-webkit-progress-bar {
  border-radius: var(--border-radius);
  background: none;
}
progress[value]::-webkit-progress-value {
  background-color: var(--progress-color);
}
progress::-moz-progress-bar {
  background-color: var(--progress-color);
}
@media (prefers-reduced-motion: no-preference) {
  progress:indeterminate {
    background: var(--progress-background-color)
      linear-gradient(
        to right,
        var(--progress-color) 30%,
        var(--progress-background-color) 30%
      )
      top left/150% 150% no-repeat;
    animation: progress-indeterminate 1s linear infinite;
  }
  progress:indeterminate[value]::-webkit-progress-value {
    background-color: transparent;
  }
  progress:indeterminate::-moz-progress-bar {
    background-color: transparent;
  }
}

@media (prefers-reduced-motion: no-preference) {
  [dir="rtl"] progress:indeterminate {
    animation-direction: reverse;
  }
}

@keyframes progress-indeterminate {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}
/**
 * Dropdown ([role="list"])
 */
details[role="list"],
li[role="list"] {
  position: relative;
}

details[role="list"] summary + ul,
li[role="list"] > ul {
  display: flex;
  z-index: 99;
  position: absolute;
  top: auto;
  right: 0;
  left: 0;
  flex-direction: column;
  margin: 0;
  padding: 0;
  border: var(--border-width) solid var(--dropdown-border-color);
  border-radius: var(--border-radius);
  border-top-right-radius: 0;
  border-top-left-radius: 0;
  background-color: var(--dropdown-background-color);
  box-shadow: var(--card-box-shadow);
  color: var(--dropdown-color);
  white-space: nowrap;
}
details[role="list"] summary + ul li,
li[role="list"] > ul li {
  width: 100%;
  margin-bottom: 0;
  padding: calc(var(--form-element-spacing-vertical) * 0.5)
    var(--form-element-spacing-horizontal);
  list-style: none;
}
details[role="list"] summary + ul li:first-of-type,
li[role="list"] > ul li:first-of-type {
  margin-top: calc(var(--form-element-spacing-vertical) * 0.5);
}
details[role="list"] summary + ul li:last-of-type,
li[role="list"] > ul li:last-of-type {
  margin-bottom: calc(var(--form-element-spacing-vertical) * 0.5);
}
details[role="list"] summary + ul li a,
li[role="list"] > ul li a {
  display: block;
  margin: calc(var(--form-element-spacing-vertical) * -0.5)
    calc(var(--form-element-spacing-horizontal) * -1);
  padding: calc(var(--form-element-spacing-vertical) * 0.5)
    var(--form-element-spacing-horizontal);
  overflow: hidden;
  color: var(--dropdown-color);
  text-decoration: none;
  text-overflow: ellipsis;
}
details[role="list"] summary + ul li a:hover,
li[role="list"] > ul li a:hover {
  background-color: var(--dropdown-hover-background-color);
}

details[role="list"] summary::after,
li[role="list"] > a::after {
  display: block;
  width: 1rem;
  height: calc(1rem * var(--line-height, 1.5));
  -webkit-margin-start: 0.5rem;
  margin-inline-start: 0.5rem;
  float: right;
  transform: rotate(0deg);
  background-position: right center;
  background-size: 1rem auto;
  background-repeat: no-repeat;
  content: "";
}

details[role="list"] {
  padding: 0;
  border-bottom: none;
}
details[role="list"] summary {
  margin-bottom: 0;
}
details[role="list"] summary:not([role]) {
  height: calc(
    1rem * var(--line-height) + var(--form-element-spacing-vertical) * 2 +
      var(--border-width) * 2
  );
  padding: var(--form-element-spacing-vertical)
    var(--form-element-spacing-horizontal);
  border: var(--border-width) solid var(--form-element-border-color);
  border-radius: var(--border-radius);
  background-color: var(--form-element-background-color);
  color: var(--form-element-placeholder-color);
  line-height: inherit;
  cursor: pointer;
  transition: background-color var(--transition), border-color var(--transition),
    color var(--transition), box-shadow var(--transition);
}
details[role="list"] summary:not([role]):active,
details[role="list"] summary:not([role]):focus {
  border-color: var(--form-element-active-border-color);
  background-color: var(--form-element-active-background-color);
}
details[role="list"] summary:not([role]):focus {
  box-shadow: 0 0 0 var(--outline-width) var(--form-element-focus-color);
}
details[role="list"][open] summary {
  border-bottom-right-radius: 0;
  border-bottom-left-radius: 0;
}
details[role="list"][open] summary::before {
  display: block;
  z-index: 1;
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  background: none;
  content: "";
  cursor: default;
}

nav details[role="list"] summary,
nav li[role="list"] a {
  display: flex;
  direction: ltr;
}

nav details[role="list"] summary + ul,
nav li[role="list"] > ul {
  min-width: -moz-fit-content;
  min-width: fit-content;
  border-radius: var(--border-radius);
}
nav details[role="list"] summary + ul li a,
nav li[role="list"] > ul li a {
  border-radius: 0;
}

nav details[role="list"] summary,
nav details[role="list"] summary:not([role]) {
  height: auto;
  padding: var(--nav-link-spacing-vertical) var(--nav-link-spacing-horizontal);
}
nav details[role="list"][open] summary {
  border-radius: var(--border-radius);
}
nav details[role="list"] summary + ul {
  margin-top: var(--outline-width);
  -webkit-margin-start: 0;
  margin-inline-start: 0;
}
nav details[role="list"] summary[role="link"] {
  margin-bottom: calc(var(--nav-link-spacing-vertical) * -1);
  line-height: var(--line-height);
}
nav details[role="list"] summary[role="link"] + ul {
  margin-top: calc(var(--nav-link-spacing-vertical) + var(--outline-width));
  -webkit-margin-start: calc(var(--nav-link-spacing-horizontal) * -1);
  margin-inline-start: calc(var(--nav-link-spacing-horizontal) * -1);
}

li[role="list"]:hover > ul,
li[role="list"] a:active ~ ul,
li[role="list"] a:focus ~ ul {
  display: flex;
}
li[role="list"] > ul {
  display: none;
  margin-top: calc(var(--nav-link-spacing-vertical) + var(--outline-width));
  -webkit-margin-start: calc(
    var(--nav-element-spacing-horizontal) - var(--nav-link-spacing-horizontal)
  );
  margin-inline-start: calc(
    var(--nav-element-spacing-horizontal) - var(--nav-link-spacing-horizontal)
  );
}
li[role="list"] > a::after {
  background-image: var(--icon-chevron);
}

/**
 * Loading ([aria-busy=true])
 */
[aria-busy="true"] {
  cursor: progress;
}

[aria-busy="true"]:not(input, select, textarea)::before {
  display: inline-block;
  width: 1em;
  height: 1em;
  border: 0.1875em solid currentColor;
  border-radius: 1em;
  border-right-color: transparent;
  content: "";
  vertical-align: text-bottom;
  vertical-align: -0.125em;
  animation: spinner 0.75s linear infinite;
  opacity: var(--loading-spinner-opacity);
}
[aria-busy="true"]:not(input, select, textarea):not(:empty)::before {
  margin-right: calc(var(--spacing) * 0.5);
  margin-left: 0;
  -webkit-margin-start: 0;
  margin-inline-start: 0;
  -webkit-margin-end: calc(var(--spacing) * 0.5);
  margin-inline-end: calc(var(--spacing) * 0.5);
}
[aria-busy="true"]:not(input, select, textarea):empty {
  text-align: center;
}

button[aria-busy="true"],
input[type="submit"][aria-busy="true"],
input[type="button"][aria-busy="true"],
input[type="reset"][aria-busy="true"],
a[aria-busy="true"] {
  pointer-events: none;
}

@keyframes spinner {
  to {
    transform: rotate(360deg);
  }
}
/**
 * Tooltip ([data-tooltip])
 */
[data-tooltip] {
  position: relative;
}
[data-tooltip]:not(a, button, input) {
  border-bottom: 1px dotted;
  text-decoration: none;
  cursor: help;
}
[data-tooltip][data-placement="top"]::before,
[data-tooltip][data-placement="top"]::after,
[data-tooltip]::before,
[data-tooltip]::after {
  display: block;
  z-index: 99;
  position: absolute;
  bottom: 100%;
  left: 50%;
  padding: 0.25rem 0.5rem;
  overflow: hidden;
  transform: translate(-50%, -0.25rem);
  border-radius: var(--border-radius);
  background: var(--tooltip-background-color);
  content: attr(data-tooltip);
  color: var(--tooltip-color);
  font-style: normal;
  font-weight: var(--font-weight);
  font-size: 0.875rem;
  text-decoration: none;
  text-overflow: ellipsis;
  white-space: nowrap;
  opacity: 0;
  pointer-events: none;
}
[data-tooltip][data-placement="top"]::after,
[data-tooltip]::after {
  padding: 0;
  transform: translate(-50%, 0rem);
  border-top: 0.3rem solid;
  border-right: 0.3rem solid transparent;
  border-left: 0.3rem solid transparent;
  border-radius: 0;
  background-color: transparent;
  content: "";
  color: var(--tooltip-background-color);
}
[data-tooltip][data-placement="bottom"]::before,
[data-tooltip][data-placement="bottom"]::after {
  top: 100%;
  bottom: auto;
  transform: translate(-50%, 0.25rem);
}
[data-tooltip][data-placement="bottom"]:after {
  transform: translate(-50%, -0.3rem);
  border: 0.3rem solid transparent;
  border-bottom: 0.3rem solid;
}
[data-tooltip][data-placement="left"]::before,
[data-tooltip][data-placement="left"]::after {
  top: 50%;
  right: 100%;
  bottom: auto;
  left: auto;
  transform: translate(-0.25rem, -50%);
}
[data-tooltip][data-placement="left"]:after {
  transform: translate(0.3rem, -50%);
  border: 0.3rem solid transparent;
  border-left: 0.3rem solid;
}
[data-tooltip][data-placement="right"]::before,
[data-tooltip][data-placement="right"]::after {
  top: 50%;
  right: auto;
  bottom: auto;
  left: 100%;
  transform: translate(0.25rem, -50%);
}
[data-tooltip][data-placement="right"]:after {
  transform: translate(-0.3rem, -50%);
  border: 0.3rem solid transparent;
  border-right: 0.3rem solid;
}
[data-tooltip]:focus::before,
[data-tooltip]:focus::after,
[data-tooltip]:hover::before,
[data-tooltip]:hover::after {
  opacity: 1;
}
@media (hover: hover) and (pointer: fine) {
  [data-tooltip][data-placement="bottom"]:focus::before,
  [data-tooltip][data-placement="bottom"]:focus::after,
  [data-tooltip][data-placement="bottom"]:hover [data-tooltip]:focus::before,
  [data-tooltip][data-placement="bottom"]:hover [data-tooltip]:focus::after,
  [data-tooltip]:hover::before,
  [data-tooltip]:hover::after {
    animation-duration: 0.2s;
    animation-name: tooltip-slide-top;
  }
  [data-tooltip][data-placement="bottom"]:focus::after,
  [data-tooltip][data-placement="bottom"]:hover [data-tooltip]:focus::after,
  [data-tooltip]:hover::after {
    animation-name: tooltip-caret-slide-top;
  }
  [data-tooltip][data-placement="bottom"]:focus::before,
  [data-tooltip][data-placement="bottom"]:focus::after,
  [data-tooltip][data-placement="bottom"]:hover::before,
  [data-tooltip][data-placement="bottom"]:hover::after {
    animation-duration: 0.2s;
    animation-name: tooltip-slide-bottom;
  }
  [data-tooltip][data-placement="bottom"]:focus::after,
  [data-tooltip][data-placement="bottom"]:hover::after {
    animation-name: tooltip-caret-slide-bottom;
  }
  [data-tooltip][data-placement="left"]:focus::before,
  [data-tooltip][data-placement="left"]:focus::after,
  [data-tooltip][data-placement="left"]:hover::before,
  [data-tooltip][data-placement="left"]:hover::after {
    animation-duration: 0.2s;
    animation-name: tooltip-slide-left;
  }
  [data-tooltip][data-placement="left"]:focus::after,
  [data-tooltip][data-placement="left"]:hover::after {
    animation-name: tooltip-caret-slide-left;
  }
  [data-tooltip][data-placement="right"]:focus::before,
  [data-tooltip][data-placement="right"]:focus::after,
  [data-tooltip][data-placement="right"]:hover::before,
  [data-tooltip][data-placement="right"]:hover::after {
    animation-duration: 0.2s;
    animation-name: tooltip-slide-right;
  }
  [data-tooltip][data-placement="right"]:focus::after,
  [data-tooltip][data-placement="right"]:hover::after {
    animation-name: tooltip-caret-slide-right;
  }
}
@keyframes tooltip-slide-top {
  from {
    transform: translate(-50%, 0.75rem);
    opacity: 0;
  }
  to {
    transform: translate(-50%, -0.25rem);
    opacity: 1;
  }
}
@keyframes tooltip-caret-slide-top {
  from {
    opacity: 0;
  }
  50% {
    transform: translate(-50%, -0.25rem);
    opacity: 0;
  }
  to {
    transform: translate(-50%, 0rem);
    opacity: 1;
  }
}
@keyframes tooltip-slide-bottom {
  from {
    transform: translate(-50%, -0.75rem);
    opacity: 0;
  }
  to {
    transform: translate(-50%, 0.25rem);
    opacity: 1;
  }
}
@keyframes tooltip-caret-slide-bottom {
  from {
    opacity: 0;
  }
  50% {
    transform: translate(-50%, -0.5rem);
    opacity: 0;
  }
  to {
    transform: translate(-50%, -0.3rem);
    opacity: 1;
  }
}
@keyframes tooltip-slide-left {
  from {
    transform: translate(0.75rem, -50%);
    opacity: 0;
  }
  to {
    transform: translate(-0.25rem, -50%);
    opacity: 1;
  }
}
@keyframes tooltip-caret-slide-left {
  from {
    opacity: 0;
  }
  50% {
    transform: translate(0.05rem, -50%);
    opacity: 0;
  }
  to {
    transform: translate(0.3rem, -50%);
    opacity: 1;
  }
}
@keyframes tooltip-slide-right {
  from {
    transform: translate(-0.75rem, -50%);
    opacity: 0;
  }
  to {
    transform: translate(0.25rem, -50%);
    opacity: 1;
  }
}
@keyframes tooltip-caret-slide-right {
  from {
    opacity: 0;
  }
  50% {
    transform: translate(-0.05rem, -50%);
    opacity: 0;
  }
  to {
    transform: translate(-0.3rem, -50%);
    opacity: 1;
  }
}

/**
 * Accessibility & User interaction
 */
[aria-controls] {
  cursor: pointer;
}

[aria-disabled="true"],
[disabled] {
  cursor: not-allowed;
}

[aria-hidden="false"][hidden] {
  display: initial;
}

[aria-hidden="false"][hidden]:not(:focus) {
  clip: rect(0, 0, 0, 0);
  position: absolute;
}

a,
area,
button,
input,
label,
select,
summary,
textarea,
[tabindex] {
  -ms-touch-action: manipulation;
}

[dir="rtl"] {
  direction: rtl;
}

/**
* Reduce Motion Features
*/
@media (prefers-reduced-motion: reduce) {
  *:not([aria-busy="true"]),
  :not([aria-busy="true"])::before,
  :not([aria-busy="true"])::after {
    background-attachment: initial !important;
    animation-duration: 1ms !important;
    animation-delay: -1ms !important;
    animation-iteration-count: 1 !important;
    scroll-behavior: auto !important;
    transition-delay: 0s !important;
    transition-duration: 0s !important;
  }
}

#mount#mount {
  /* --primary: rgb(227, 59, 126); */
  --primary: #ea4c89;
  --primary-hover: #f082ac;
  --icon-xia: url("data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTYiIGhlaWdodD0iMTYiIHZpZXdCb3g9IjAgMCAxNiAxNiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGcgaWQ9IkZyYW1lIj4KPHBhdGggaWQ9IlZlY3RvciIgZD0iTTguMDAyOTEgOS42Nzk4M0wzLjgzMzM5IDUuNTEyMjFMMy4wMjUzOSA2LjMxOTgzTDguMDAzMjkgMTEuMjk1MUwxMi45NzYyIDYuMzE5ODNMMTIuMTY3OSA1LjUxMjIxTDguMDAyOTEgOS42Nzk4M1oiIGZpbGw9IiM4MzgzODMiLz4KPC9nPgo8L3N2Zz4K");
  --switch-checked-background-color: var(--primary);
}

li.select-link.select-link:hover > ul {
  display: none;
}
li.select-link.select-link > ul {
  display: none;
}
li.select-link.select-link a:focus ~ ul {
  display: none;
}

li.select-link.select-link a:active ~ ul {
  display: none;
}
li.select-link-active.select-link-active > ul {
  display: flex;
}
li.select-link-active.select-link-active:hover > ul {
  display: flex;
}

li.select-link-active.select-link-active a:focus ~ ul {
  display: flex;
}

li.select-link-active.select-link-active a:active ~ ul {
  display: flex;
}
ul.select-link-ul.select-link-ul {
  right: 0px;
  left: auto;
}

a.select-link-selected {
  background-color: var(--primary-focus);
}
.immersive-translate-no-select {
  -webkit-touch-callout: none; /* iOS Safari */
  -webkit-user-select: none; /* Safari */
  -khtml-user-select: none; /* Konqueror HTML */
  -moz-user-select: none; /* Old versions of Firefox */
  -ms-user-select: none; /* Internet Explorer/Edge */
  user-select: none;
}

/* li[role="list"].no-arrow > a::after { */
/*   background-image: none; */
/*   width: 0; */
/*   color: var(--color); */
/* } */
li[role="list"].no-arrow {
  margin-left: 8px;
  padding-right: 0;
}
li[role="list"] > a::after {
  -webkit-margin-start: 0.2rem;
  margin-inline-start: 0.2rem;
}

li[role="list"].no-arrow > a,
li[role="list"].no-arrow > a:link,
li[role="list"].no-arrow > a:visited {
  color: var(--secondary);
}

select.min-select {
  --form-element-spacing-horizontal: 0;
  margin-bottom: 4px;
  max-width: 128px;
  overflow: hidden;
  color: var(--primary);
  font-size: 13px;
  border: none;
  padding: 0;
  padding-right: 20px;
  padding-left: 8px;
  text-overflow: ellipsis;
  color: var(--color);

}
select.min-select-secondary {
  color: var(--color);
}
select.min-select:focus {
  outline: none;
  border: none;
  --box-shadow: none;
}
select.min-select-no-arrow {
  background-image: none;
  padding-right: 0;
}

select.min-select-left {
  padding-right: 0px;
  /* padding-left: 24px; */
  /* background-position: center left 0; */
  text-overflow: ellipsis;
  text-align: left;
}

.muted {
  color: var(--muted-color);
}

.select.button-select {
  --background-color: var(--secondary-hover);
  --border-color: var(--secondary-hover);
  --color: var(--secondary-inverse);
  cursor: pointer;
  --box-shadow: var(--button-box-shadow, 0 0 0 rgba(0, 0, 0, 0));
  padding: var(--form-element-spacing-vertical)
    var(--form-element-spacing-horizontal);
  border: var(--border-width) solid var(--border-color);
  border-radius: var(--border-radius);
  outline: none;
  background-color: var(--background-color);
  box-shadow: var(--box-shadow);
  color: var(--color);
  font-weight: var(--font-weight);
  font-size: 16px;
  line-height: var(--line-height);
  text-align: center;
  cursor: pointer;
  transition: background-color var(--transition), border-color var(--transition),
    color var(--transition), box-shadow var(--transition);
  -webkit-appearance: button;
  margin: 0;
  margin-bottom: 0px;
  overflow: visible;
  font-family: inherit;
  text-transform: none;
}

html {
  font-size: 16px;
  --font-size: 16px;
}

body {
  padding: 0;
  margin: 0 auto;
  min-width: 268px;
  border-radius: 10px;
}

.popup-container {
  color: #666;
  background-color: var(--popup-footer-background-color);
  width: 316px;
  min-width: 316px;
}

.popup-content {
  background-color: var(--popup-content-background-color);
  border-radius: 0px 0px 12px 12px;
  padding: 16px 20px;
}

.immersive-translate-popup-overlay {
  position: fixed;
  top: 0;
  left: 0;
  height: 100%;
  width: 100%;
  touch-action: none;
}

.immersive-translate-popup-wrapper {
  background: var(--background-color);
  border-radius: 10px;
  border: 1px solid var(--muted-border-color);
}

#mount#mount {
  --font-family: system-ui, -apple-system, "Segoe UI", "Roboto", "Ubuntu",
    "Cantarell", "Noto Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji",
    "Segoe UI Symbol", "Noto Color Emoji";
  --line-height: 1.5;
  --font-weight: 400;
  --font-size: 16px;
  --border-radius: 4px;
  --border-width: 1px;
  --outline-width: 3px;
  --spacing: 16px;
  --typography-spacing-vertical: 24px;
  --block-spacing-vertical: calc(var(--spacing) * 2);
  --block-spacing-horizontal: var(--spacing);
  --grid-spacing-vertical: 0;
  --grid-spacing-horizontal: var(--spacing);
  --form-element-spacing-vertical: 12px;
  --form-element-spacing-horizontal: 16px;
  --nav-element-spacing-vertical: 16px;
  --nav-element-spacing-horizontal: 8px;
  --nav-link-spacing-vertical: 8px;
  --nav-link-spacing-horizontal: 8px;
  --form-label-font-weight: var(--font-weight);
  --transition: 0.2s ease-in-out;
  --modal-overlay-backdrop-filter: blur(4px);
}

[data-theme="light"],
#mount:not([data-theme="dark"]) {
  --popup-footer-background-color: #e8eaeb;
  --popup-content-background-color: #ffffff;
  --popup-item-background-color: #f3f5f6;
  --popup-item-hover-background-color: #eaeced;
  --popup-trial-pro-background-color: #F9FBFC;
  --text-black-2: #222222;
  --text-gray-2: #222222;
  --text-gray-6: #666666;
  --text-gray-9: #999999;
  --text-gray-c2: #c2c2c2;
  --service-select-content-shadow: 0px 2px 12px 0px rgba(75, 76, 77, 0.20);
  --service-select-border-color: #FAFAFA;
  --service-select-selected-background-color: #F3F5F6;
}

@media only screen and (prefers-color-scheme: dark) {
  #mount:not([data-theme="light"]) {
    --popup-footer-background-color: #0d0d0d;
    --popup-content-background-color: #191919;
    --popup-item-background-color: #272727;
    --popup-item-hover-background-color: #333333;
    --popup-trial-pro-background-color: #222222;
    --text-black-2: #ffffff;
    --text-gray-2: #dbdbdb;
    --text-gray-6: #b3b3b3;
    --text-gray-9: #777777;
    --text-gray-c2: #5b5b5b;
    --service-select-content-shadow: 0px 2px 12px 0px rgba(0, 0, 0, 0.90);
    --service-select-border-color: #2C2C2C;
    --service-select-selected-background-color: #333333;
  }
}

[data-theme="dark"] {
  --popup-footer-background-color: #0d0d0d;
  --popup-content-background-color: #191919;
  --popup-item-background-color: #272727;
  --popup-item-hover-background-color: #333333;
  --popup-trial-pro-background-color: #222222;
  --text-black-2: #ffffff;
  --text-gray-2: #dbdbdb;
  --text-gray-6: #b3b3b3;
  --text-gray-9: #777777;
  --text-gray-c2: #5b5b5b;
  --service-select-content-shadow: 0px 2px 12px 0px rgba(0, 0, 0, 0.90);
  --service-select-border-color: #2C2C2C;
  --service-select-selected-background-color: #333333;
}

.text-balck {
  color: var(--text-black-2);
}

.text-gray-2 {
  color: var(--text-gray-2);
}

.text-gray-6 {
  color: var(--text-gray-6);
}

.text-gray-9 {
  color: var(--text-gray-9);
}

.text-gray-c2 {
  color: var(--text-gray-c2);
}

#mount {
  min-width: 268px;
}

.main-button {
  font-size: 15px;
  vertical-align: middle;
  border-radius: 12px;
  padding: unset;
  height: 44px;
  line-height: 44px;
}

.pt-4 {
  padding-top: 16px;
}

.p-2 {
  padding: 8px;
}

.pl-5 {
  padding-left: 48px;
}

.p-0 {
  padding: 0;
}

.pl-2 {
  padding-left: 8px;
}

.pl-4 {
  padding-left: 24px;
}

.pt-2 {
  padding-top: 8px;
}

.pb-2 {
  padding-bottom: 8px;
}

.pb-4 {
  padding-bottom: 16px;
}

.pb-5 {
  padding-bottom: 20px;
}

.pr-5 {
  padding-right: 48px;
}

.text-sm {
  font-size: 13px;
}

.text-base {
  font-size: 16px;
}

.w-full {
  width: 100%;
}

.flex {
  display: flex;
}

.flex-row {
  flex-direction: row;
}

.flex-wrap {
  flex-wrap: wrap;
}

.flex-end {
  justify-content: flex-end;
}

.flex-grow {
  flex-grow: 1;
}

.justify-between {
  justify-content: space-between;
}

.mb-0 {
  margin-bottom: 0px;
}

.mb-2 {
  margin-bottom: 8px;
}

.mb-4 {
  margin-bottom: 16px;
}

.mb-3 {
  margin-bottom: 12px;
}

.inline-block {
  display: inline-block;
}

.py-2 {
  padding-top: 8px;
  padding-bottom: 8px;
}

.py-2-5 {
  padding-top: 6px;
  padding-bottom: 6px;
}

.mt-0 {
  margin-top: 0;
}

.mt-2 {
  margin-top: 8px;
}

.mt-3 {
  margin-top: 12px;
}

.mt-4 {
  margin-top: 16px;
}

.mt-5 {
  margin-top: 20px;
}

.mt-6 {
  margin-top: 24px;
}

.mb-1 {
  margin-bottom: 4px;
}

.ml-4 {
  margin-left: 24px;
}

.ml-3 {
  margin-left: 16px;
}

.ml-2 {
  margin-left: 8px;
}

.ml-1 {
  margin-left: 4px;
}

.mr-1 {
  margin-right: 4px;
}

.mr-2 {
  margin-right: 8px;
}

.mr-3 {
  margin-right: 16px;
}

.mx-2 {
  margin-left: 8px;
  margin-right: 8px;
}

.pl-3 {
  padding-left: 12px;
}

.pr-3 {
  padding-right: 12px;
}

.p-3 {
  padding: 12px;
}

.px-1 {
  padding-left: 4px;
  padding-right: 4px;
}

.px-3 {
  padding-left: 12px;
  padding-right: 12px;
}

.pt-3 {
  padding-top: 12px;
}

.px-6 {
  padding-left: 18px;
  padding-right: 18px;
}

.px-4 {
  padding-left: 16px;
  padding-right: 16px;
}

.pt-6 {
  padding-top: 20px;
}

.py-3 {
  padding-top: 12px;
  padding-bottom: 12px;
}

.py-0 {
  padding-top: 0;
  padding-bottom: 0;
}

.left-auto {
  left: auto !important;
}

.max-h-28 {
  max-height: 112px;
}

.max-h-30 {
  max-height: 120px;
}

.overflow-y-scroll {
  overflow-y: scroll;
}

.text-xs {
  font-size: 12px;
}

.flex-1 {
  flex: 1;
}

.flex-3 {
  flex: 3;
}

.flex-4 {
  flex: 4;
}

.flex-2 {
  flex: 2;
}

.items-center {
  align-items: center;
}

.max-content {
  width: max-content;
}

.justify-center {
  justify-content: center;
}

.items-end {
  align-items: flex-end;
}

.items-baseline {
  align-items: baseline;
}

.my-5 {
  margin-top: 48px;
  margin-bottom: 48px;
}

.my-4 {
  margin-top: 24px;
  margin-bottom: 24px;
}

.my-3 {
  margin-top: 16px;
  margin-bottom: 16px;
}

.pt-3 {
  padding-top: 12px;
}

.px-3 {
  padding-left: 12px;
  padding-right: 12px;
}

.pt-2 {
  padding-top: 8px;
}

.px-2 {
  padding-left: 8px;
  padding-right: 8px;
}

.pt-1 {
  padding-top: 4px;
}

.px-1 {
  padding-left: 4px;
  padding-right: 4px;
}

.pb-2 {
  padding-bottom: 8px;
}

.justify-end {
  justify-content: flex-end;
}

.w-auto {
  width: auto;
}

.shrink-0 {
  flex-shrink: 0;
}

select.language-select,
select.translate-service,
select.min-select {
  --form-element-spacing-horizontal: 0;
  margin-bottom: 0px;
  max-width: unset;
  flex: 1;
  overflow: hidden;
  font-size: 13px;
  border: none;
  border-radius: 8px;
  padding-right: 30px;
  padding-left: 0px;
  background-position: center right 12px;
  background-size: 16px auto;
  background-image: var(--icon-xia);
  text-overflow: ellipsis;
  color: var(--text-gray-2);
  background-color: transparent;
  box-shadow: unset !important;
  cursor: pointer;
}

select.more {
  background-position: center right;
  padding-right: 20px;
}

select.transform-padding-left {
  padding-left: 12px;
  transform: translateX(-12px);
  background-position: center right 0px;
}

select.translate-service {
  color: var(--text-black-2);
}

/* dark use black, for windows */
@media (prefers-color-scheme: dark) {

  select.language-select option,
  select.translate-service option,
  select.min-select option {
    background-color: #666666;
  }
}

.text-overflow-ellipsis {
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
}

.max-w-20 {
  max-width: 180px;
  white-space: nowrap;
}

select.min-select-secondary {
  color: var(--color);
}

select.min-select:focus {
  outline: none;
  border: none;
  --box-shadow: none;
}

select.min-select-no-arrow {
  background-image: none;
  padding-right: 0;
}

select.min-select-left {
  padding-right: 0px;
  /* padding-left: 24px; */
  /* background-position: center left 0; */
  text-overflow: ellipsis;
  text-align: left;
}

.popup-footer {
  background-color: var(--popup-footer-background-color);
  height: 40px;
}

.text-right {
  text-align: right;
}

.clickable {
  cursor: pointer;
}

.close {
  cursor: pointer;
  width: 16px;
  height: 16px;
  background-image: var(--icon-close);
  background-position: center;
  background-size: auto 1rem;
  background-repeat: no-repeat;
  opacity: 0.5;
  transition: opacity var(--transition);
}

.padding-two-column {
  padding-left: 40px;
  padding-right: 40px;
}

.muted {
  color: #999;
}

.text-label {
  color: #666;
}

.display-none {
  display: none;
}

/* dark use #18232c */
@media (prefers-color-scheme: dark) {
  .text-label {
    color: #9ca3af;
  }
}

.text-decoration-none {
  text-decoration: none;
}

.text-decoration-none:is([aria-current], :hover, :active, :focus),
[role="link"]:is([aria-current], :hover, :active, :focus) {
  --text-decoration: none !important;
  background-color: transparent !important;
}

.language-select-container {
  position: relative;
  width: 100%;
  background-color: var(--popup-item-background-color);
  height: 55px;
  border-radius: 12px;
}

select.language-select {
  color: var(--text-black-2);
  font-size: 14px;
  padding: 8px 24px 24px 16px;
  position: absolute;
  border-radius: 12px;
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
}

select.text-gray-6 {
  color: var(--text-gray-6);
}

.language-select-container label {
  position: absolute;
  bottom: 10px;
  left: 16px;
  font-size: 12px;
  color: var(--text-gray-9);
  line-height: 12px;
  margin: 0;
}

.translation-service-container {
  background-color: var(--popup-item-background-color);
  border-radius: 12px;
}

.min-select-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 44px;
  background-color: var(--popup-item-background-color);
  padding-left: 16px;
}

.min-select-container:first-child {
  border-top-left-radius: 10px;
  border-top-right-radius: 10px;
}

.min-select-container:last-child {
  border-bottom-left-radius: 10px;
  border-bottom-right-radius: 10px;
}

.min-select-container:only-child {
  border-radius: 10px;
}

.translate-mode {
  width: 44px;
  height: 44px;
  border-radius: 22px;
  background-color: var(--popup-item-background-color);
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  cursor: pointer;
}

.translate-mode svg {
  fill: var(--text-gray-2);
}

.widgets-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.widgets-container> :not(:last-child) {
  margin-right: 8px;
}

.widget-item {
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--popup-item-background-color);
  font-size: 12px;
  height: 44px;
  border-radius: 8px;
  cursor: pointer;
  flex: 1;
}

.widget-item svg {
  fill: var(--text-gray-2);
}

.setting svg {
  fill: var(--text-gray-6);
}

.share-button-container {
  display: flex;
  align-items: center;
  cursor: pointer;
  padding: 2px 3px 0 8px;
}

.share-button-container svg {
  fill: var(--text-gray-9);
}

.min-select-container:hover,
.language-select-container:hover,
.widget-item:hover,
.translate-mode:hover {
  background-color: var(--popup-item-hover-background-color);
}

.main-button:hover {
  background-color: #f5508f;
}

.share-button-container:hover {
  background-color: var(--popup-item-background-color);
  border-radius: 6px;
}

.error-boundary {
  background: #fff2f0;
  border: 1px solid #ffccc7;
  display: flex;
  padding: 12px;
  font-size: 14px;
  color: rgba(0, 0, 0, 0.88);
  word-break: break-all;
  margin: 12px;
  border-radius: 12px;
  flex-direction: column;
}


.upgrade-pro {
  border-radius: 11px;
  background: linear-gradient(57deg, #272727 19.8%, #696969 82.2%);
  padding: 2px 8px;
  transform: scale(0.85);
}

.upgrade-pro span {
  background: linear-gradient(180deg, #FFEAB4 17.65%, #F8C235 85.29%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  font-size: 12px;
  margin-left: 4px;
}


.upgrade-pro svg {
  margin-top: -2px;
}

.upgrade-pro:hover {
  background: linear-gradient(57deg, #3D3D3D 19.8%, #949494 82.2%);
}

.border-bottom-radius-0 {
  border-bottom-left-radius: 0 !important;
  border-bottom-right-radius: 0 !important;
}

.trial-pro-container {
  border-radius: 0px 0px 12px 12px;
  background: var(--popup-trial-pro-background-color);
  display: flex;
  align-items: center;
  height: 44px;
  padding-left: 16px;
  padding-right: 12px;
  font-size: 12px;
}

.trial-pro-container label {
  line-height: 13px;
  color: var(--text-black-2);
}

.trial-pro-container img {
  margin-left: 5px;
}

.cursor-pointer {
  cursor: pointer;
}

.upgrade-pro-discount-act {
  height: 25px;
  display: flex;
  padding: 0 4px;
  align-items: center;
  border-radius: 15px;
  background: linear-gradient(90deg, #CEFBFA 11.33%, #D7F56F 63.75%, #FCCD5E 100%);
  transform: scale(0.9);
  box-shadow: 0px 1.8px 3.6px 0px rgba(0, 0, 0, 0.10);
  cursor: pointer;
}

.upgrade-pro-discount-act span {
  font-size: 12px;
  font-weight: 700;
  margin-left: 4px;
  color: #222222;
}

.upgrade-pro-discount-act:hover {
  text-decoration: unset;
  background: linear-gradient(90deg, #E2FFFE 11.33%, #E6FF91 63.75%, #FFDF93 100%);
}


.custom-select-container {
  width: 200px;
  position: relative;
  flex: 1;
}

.custom-select-content {
  border-radius: 12px;
  background: var(--popup-content-background-color);
  box-shadow: var(--service-select-content-shadow);
  border: 1px solid var(--service-select-border-color);
  padding: 4px 5px;
  position: absolute;
  left: 0;
  right: 0;
  z-index: 100;
  overflow-y: auto;
}

.custom-select-item {
  font-size: 13px;
  padding: 5px 6px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  cursor: pointer;
  color: var(--text-black-2);
  width: 100%;
  overflow: hidden;
  height: 30px;
  line-height: 30px;
}

.custom-select-item-img {
  width: 20px;
  height: 20px;
  margin-right: 4px;
}

@media (prefers-color-scheme: dark) {
  .custom-select-item-img {
    margin-right: 6px;
  }
}


.custom-select-content .custom-select-item.selected, .custom-select-content .custom-select-item:hover {
  background: var(--service-select-selected-background-color);
}

.custom-select-item > span {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.custom-select-item-pro {
  font-size: 12px;
  margin-left: 6px;
}

.custom-select-item-pro img {
  margin:  0 3px;
  width: 20px;
}


html {
  font-size: 17px;
}

@media print {
  .imt-fb-container {
    display: none !important;
  }
}

#mount#mount {
  position: absolute;
  display: none;
  min-width: 250px;
  height: auto;
  --font-size: 17px;
  font-size: 17px;
}

/* float-ball */
.imt-fb-container {
  position: fixed;
  padding: 0;
  z-index: 2147483647;
  top: 335px;
  width: 56px;
  display: flex;
  flex-direction: column;
  display: none;
}

.imt-fb-container.left {
  align-items: flex-start;
  left: 0;
}

.imt-fb-container.right {
  align-items: flex-end;
  right: 0;
}

.imt-fb-btn {
  cursor: pointer;
  background: linear-gradient(320.9deg, #db3b7b 26.47%, #ffcee2 88.86%);
  height: 36px;
  width: 56px;
  box-shadow: 2px 6px 10px 0px #0e121629;
}

.imt-fb-btn.left {
  border-top-right-radius: 36px;
  border-bottom-right-radius: 36px;
}

.imt-fb-btn.right {
  border-top-left-radius: 36px;
  border-bottom-left-radius: 36px;
}

.imt-fb-btn div {
  background: linear-gradient(140.91deg, #ff87b7 12.61%, #ec4c8c 76.89%);
  height: 34px;
  width: 54px;
  margin: 1px;
  display: flex;
  align-items: center;
}

.imt-fb-btn.left div {
  border-top-right-radius: 34px;
  border-bottom-right-radius: 34px;
  justify-content: flex-end;
}

.imt-fb-btn.right div {
  border-top-left-radius: 34px;
  border-bottom-left-radius: 34px;
}

.imt-fb-logo-img {
  width: 20px;
  height: 20px;
  margin: 0 10px;
}

.imt-float-ball-translated {
  position: absolute;
  width: 11px;
  height: 11px;
  bottom: 4px;
  right: 20px;
}

.btn-animate {
  -webkit-transform: translate3d(0, 0, 0);
  transform: translate3d(0, 0, 0);
  -webkit-transition: -webkit-transform ease-out 250ms;
  transition: -webkit-transform ease-out 250ms;
  transition: transform ease-out 250ms;
  transition: transform ease-out 250ms, -webkit-transform ease-out 250ms;
}

.imt-fb-setting-btn {
  margin-right: 18px;
  width: 28px;
  height: 28px;
}

.immersive-translate-popup-wrapper {
  background: var(--background-color);
  border-radius: 20px;
  box-shadow: 2px 10px 24px 0px #0e121614;
  border: none;
}

.popup-container  {
  border-radius: 20px;
}

.popup-content {
  border-radius: 20px 20px 12px 12px;
}
.popup-footer {
  border-radius: 20px;
}

.imt-fb-close-content {
  padding: 22px;
  width: 320px;
}

.imt-fb-close-title {
  font-weight: 500;
  color: var(--h2-color);
}

.imt-fb-close-radio-content {
  background-color: var(--background-light-green);
  padding: 8px 20px;
}

.imt-fb-radio-sel,
.imt-fb-radio-nor {
  width: 16px;
  height: 16px;
  border-radius: 8px;
  flex-shrink: 0;
}

.imt-fb-radio-sel {
  border: 2px solid var(--primary);
  display: flex;
  align-items: center;
  justify-content: center;
}

.imt-fb-radio-sel div {
  width: 8px;
  height: 8px;
  border-radius: 4px;
  background-color: var(--primary);
}

.imt-fb-radio-nor {
  border: 2px solid #d3d4d6;
}

.imt-fb-primary-btn {
  background-color: var(--primary);
  width: 72px;
  height: 32px;
  color: white;
  border-radius: 8px;
  text-align: center;
  line-height: 32px;
  font-size: 16px;
  cursor: pointer;
}

.imt-fb-default-btn {
  border: 1px solid var(--primary);
  width: 72px;
  height: 32px;
  border-radius: 8px;
  color: var(--primary);
  line-height: 32px;
  text-align: center;
  font-size: 16px;
}

.imt-fb-guide-container {
  width: 312px;
  transform: translateY(-50%);
}

.imt-fb-guide-bg {
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  z-index: -1;
  height: 100%;
  width: 100%;
}

.imt-fb-guide-bg.left {
  transform: scaleX(-1);
}

.imt-fb-guide-content {
  margin: 16px 32px 60px 21px;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.imt-fb-guide-content.left {
  margin: 16px 21px 60px 32px;
}

.imt-fb-guide-img {
  width: 235px;
  height: 171px;
  margin-top: 16px;
}

.imt-fb-guide-message {
  font-size: 16px;
  line-height: 28px;
  color: #333333;
  white-space: pre-wrap;
  text-align: center;
  font-weight: 700;
  margin-top: 10px;
}

.imt-fb-guide-button {
  margin-top: 16px;
  line-height: 40px;
  height: 40px;
  padding: 0 20px;
  width: unset;
}

.imt-fb-more-buttons {
  box-shadow: 0px 2px 10px 0px #00000014;
  border: 1px solid var(--float-ball-more-button-border-color);
  background: var(--float-ball-more-button-background-color);
  width: 36px;
  display: flex;
  flex-direction: column;
  border-radius: 18px;
  margin-right: 8px;
}

.imt-fb-more-button {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}


/* Sheet.css */
.immersive-translate-sheet {
  position: fixed;
  transform: translateY(100%);
  /* Start off screen */
  left: 0;
  right: 0;
  background-color: white;
  transition: transform 0.3s ease-out;
  /* Smooth slide transition */
  box-shadow: 0px -2px 10px rgba(0, 0, 0, 0.1);
  /* Ensure it's above other content */
  bottom: 0;
  border-top-left-radius: 16px;
  border-top-right-radius: 16px;
  overflow: hidden;
}

.immersive-translate-sheet.visible {
  transform: translateY(0);
}

.immersive-translate-sheet-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  opacity: 0;
  transition: opacity 0.3s ease-out;
}

.immersive-translate-sheet-backdrop.visible {
  opacity: 1;
}

.popup-container-sheet {
  max-width: 100vw;
  width: 100vw;
}

.imt-no-events svg * {
  pointer-events: none !important;
}

.imt-manga-button {
  width: 36px;
  display: flex;
  flex-direction: column;
  position: relative;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  pointer-events: all;
  margin: 12px 0 0 0;
  background-color: white;
  border-radius: 18px;
  filter: drop-shadow(0px 2px 10px rgba(0, 0, 0, 0.08));
  opacity: 0.5;
  right: 8px;
}

.imt-manga-feedback {
  cursor: pointer;
  margin: 10px 9px 12px 9px;
}

.imt-manga-button:hover {
  opacity: 1;
}

.imt-manga-translated {
  position: absolute;
  left: 24px;
  top: 20px;
}

.imt-float-ball-loading {
  animation: imt-loading-animation 0.6s infinite linear !important;
}

.imt-manga-guide-bg {
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  z-index: -1;
  width: 372px;
  transform: translateY(-50%);
}
.imt-manga-guide-content {
  position: absolute;
  top: 15px;
  left: 0;
  right: 0;
  margin: 0 40px 0;
}

.img-manga-guide-button {
  width: fit-content;
  margin: 16px auto;
}

.img-manga-close {
  position: absolute;
  bottom: -200px;
  width: 32px;
  height: 32px;
  left: 0;
  right: 0;
  margin: auto;
  cursor: pointer;
}

@-webkit-keyframes imt-loading-animation {
  from {
    -webkit-transform: rotate(0deg);
  }

  to {
    -webkit-transform: rotate(359deg);
  }
}

@keyframes imt-loading-animation {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(359deg);
  }
}
</style><div id="mount" style="display: block;"></div></template></div></html>