```powershell
# 创建一个基础的
nest g controller <控制器名称>

# 如：nest g controller cats , 生成结构如下
src
- cats
  - cats.controller.spec.ts
  - cats.controller.ts
```

## 1. 路由(Routor)

```typescript
import { Controller, Get, Res } from '@nestjs/common';

// 这里定义了整个控制器的路由前缀
@Controller('cats')
export class CatsController {
  // 这个装饰器告诉 Nest，这个方法是处理 GET 请求的
  // 这个方法的路径是 /cats/breed
  @Get('breed')
  findAll(): any {
    // 通常情况下，这个方式会自动序列化为一个 JSON 对象
    return {
      breed: 'Persian',
      origin: 'Iran',
    };
  }

  @Get('express')
  findExpressRes(@Res() response): string {
    // 也可以直接使用 express 的 response 对象，返回特定的状态
    response.status(404).send('This action returns all cats');
    return response;
  }
  
  // @Get('breeds?') 将匹配 /cats/breed 和 /cats/breeds
  @Get('breeds?')
  findOptional(@Req() request: Request): string {
    return 'This action returns breed or breeds';
  }

  // @Get('breed+') 将匹配 /cats/breed、/cats/breeds、/cats/breedd 等
  @Get('breed+')
  findMultiple(@Req() request: Request): string {
    return 'This action returns breed with multiple characters';
  }

  // @Get('breed*') 将匹配 /cats/breed 及其所有子路径，例如 /cats/breeds、/cats/breed123 等
  @Get('breed*')
  findAll(@Req() request: Request): string {
    return 'This action returns all breeds';
  }

  // @Get('breed(s)?') 将匹配 /cats/breed 和 /cats/breeds
  @Get('breed(s)?')
  findGroup(@Req() request: Request): string {
    return 'This action returns breed or breeds';
  }

  // @Get('breed.info') 将匹配 /cats/breed.info
  @Get('breed-info')
  findInfo(@Req() request: Request): string {
    return 'This action returns breed info';
  }

  // @Get('breed.info') 将匹配 /cats/breed.info
  @Get('breed.info')
  findDotInfo(@Req() request: Request): string {
    return 'This action returns breed info with dot';
  }
}
```

> 【注】：Nest 为所有标准的 HTTP 方法提供装饰器：**@Get()、@Post()、@Put()、@Delete()、@Patch()、@Options() **和 **@Head()**。此外，**@All()** 定义了一个端点来处理所有这些
> 
> ==仅 express 支持路由中间的通配符==。

<br/>

## 2. 请求对象（Request）

### 2.1. 一些定义好的装饰器

|装饰器|普通平台特定对象|说明|
|--|--|--|
|@Request(), @Req()|req|请求参数|
|@Response(), @Res()*|res|响应参数|
|@Next()|next|用于中间件中跳转到下一个处理过程|
|@Session()|req.session|获取 Session|
|@Param(key?: string)|req.params / req.params[key]|获取路径参数|
|@Body(key?: string)|req.body / req.body[key]|获取请求体|
|@Query(key?: string)|req.query / req.query[key]|获取 Query 参数|
|@Headers(name?: string)|req.headers / req.headers[name]|获取请求头|
|@Ip()|req.ip|获取请求的 IP 地址|
|@HostParam()|req.hosts|获取请求的主机名的参数|

```typescript
/* @Req() 装饰器的使用 */
import { Controller, Get, Req } from '@nestjs/common';
import { Request } from 'express';

@Controller('cats')
export class CatsController {
  @Get('breed')
  findAll(@Req() request: Request): string {
    // 返回请求的 Url
    return request.url;
  }
}

```

### 2.1. 路由参数

```typescript
import { Controller, Get, Param } from '@nestjs/common';

@Controller('cats')
export class CatsController {
  @Get(':id')
  findOne(@Param() params: any): string {
    // 可以获取所有的路径参数
    console.log(params.id);
    return `This action returns a #${params.id} cat`;
  }

  @Get(':id')
  findOne1(@Param('id') id: string): string {
    // 也可以获取单独的路径参数
    return `This action returns a #${id} cat`;
  }
}

```

<br/>

## 3. 状态码、头文件和重定向

```typescript
import {
  Controller,
  Get,
  Header,
  HttpCode,
  Post,
  Query,
  Redirect,
} from '@nestjs/common';

@Controller('cats')
export class CatsController {
  @Post()
  @HttpCode(204)
  @Header('Cache-Control', 'none')
  create() {
    // 设置返回的请求头
    return 'This action adds a new cat';
  }

  @Get('docs')
  @Redirect('https://nest.nodejs.cn', 302)
  getDocs(@Query('version') version) {
    // 这里会讲这里操作的值返回给 https://nest.nodejs.cn
    if (version && version === '5') {
      return { url: 'https://nest.nodejs.cn/v5/' };
    }
  }
}

```

<br/>

## 4. 前缀限制

```typescript
// 这里 host 的作用是限制这个控制器只能，通过 admin.example.com 域名才能调用
@Controller({ host: 'admin.example.com' })
export class AdminController {
  @Get()
  index(): string {
    return 'Admin page';
  }
}

// 同样也可以通过路径参数 来获取路径参数 account 的相关信息
@Controller({ host: ':account.example.com' })
export class AccountController {
  @Get()
  getInfo(@HostParam('account') account: string) {
    return account;
  }
}
```

<br/>

## 5. 全局单例（重要！！！）

### 5.1. 共享资源

在 NestJS 中，许多资源和服务是共享的，例如：

- **数据库连接池**：多个请求可以共享同一个数据库连接池，以提高性能和资源利用率。
- **单例服务**：服务通常是单例的，这意味着它们在应用程序的整个生命周期内只会被实例化一次，并且在多个请求之间共享

<br/>

### 5.2. Node.js 的单线程模型

Node.js 使用的是单线程事件驱动模型，而不是传统的多线程模型。这意味着：

- **单线程**：所有的请求都是在同一个线程中处理的，而不是为每个请求创建一个新的线程
- **事件驱动**：Node.js 使用事件循环来处理异步操作，这使得它能够高效地处理大量并发请求

<br/>

### 5.3. 安全性

由于 Node.js 的单线程模型，使用单例实例是安全的：

- **单例实例**：在传统的多线程模型中，每个请求由一个独立的线程处理，使用全局状态或单例实例可能会导致线程安全问题。然而，在 Node.js 中，由于所有请求在同一个线程中处理，不存在线程安全问题。
- **全局状态**：可以安全地在单例服务中维护全局状态，因为不会有多个线程同时修改同一个状态

<br/>

### 5.4. 例子与结论

```typescript
import { Injectable } from '@nestjs/common';

// 由于是单例，因此所有的请求共享这两个服务，不存在线程安全问题

@Injectable()
export class DatabaseService {
  // 定义了一个数据库连接服务
  
  private connectionPool: any;

  constructor() {
    this.connectionPool = createConnectionPool();
  }

  getConnection() {
    return this.connectionPool.getConnection();
  }
}

@Injectable()
export class CounterService {
  // 定义一个共享的计时器
  private counter = 0;

  increment() {
    this.counter++;
  }

  getCounter() {
    return this.counter;
  }
}
```

> 【结论】：在 NestJS 中，由于 Node.js 的单线程事件驱动模型，使用单例服务和共享资源是安全且高效的。这与传统的多线程模型不同，在多线程模型中，每个请求由一个独立的线程处理，使用全局状态或单例实例可能会导致线程安全问题。

<br/>

## 6. 异步性

在 NestJS 中，`Promise` 和 `Observable` 都可以用于处理异步操作，但它们有不同的特性和使用场景。以下是它们的主要区别和使用示例：

### 6.1. Promise

`Promise` 是一种处理单次异步操作的机制。它表示一个异步操作的最终完成（或失败）及其结果值。

#### 特性

- **单次执行**：`Promise` 只会执行一次，并且只能有一个结果（成功或失败）。
- **简洁**：对于简单的异步操作，`Promise` 更加简洁和易于理解。

#### 示例

```typescript
import { Controller, Get } from '@nestjs/common';

@Controller('cats')
export class CatsController {
  @Get('promise')
  async findAll(): Promise<string> {
    const result = await this.someAsyncOperation();
    return `This action returns all cats: ${result}`;
  }

  private async someAsyncOperation(): Promise<string> {
    // 模拟异步操作
    return new Promise((resolve) => {
      setTimeout(() => resolve('cats data'), 1000);
    });
  }
}
```

<br/>

### 6.2. Observable （RxJS 的可观察流）

`Observable` 是一种处理多次异步操作的机制。它可以发出多个值，并且可以被取消。

#### 特性

- **多次执行**：`Observable` 可以发出多个值，并且可以被订阅多次。
- **可取消**：`Observable` 可以被取消，这在处理长时间运行的异步操作时非常有用。
- **更强的功能**：`Observable` 提供了丰富的操作符，可以进行复杂的异步操作和流处理。

<br/>

### 示例：使用 `Observable` 处理实时数据流

假设我们有一个实时数据流（例如 WebSocket 连接），我们希望在 NestJS 控制器中处理这个数据流。

<br/>

#### 1. 多次执行

`Observable` 可以发出多个值，这在处理实时数据流时非常有用。

<br/>

#### 2. 可取消性

`Observable` 可以被取消，这在处理长时间运行的异步操作时非常有用。

<br/>

#### 3. 强大的操作符

`Observable` 提供了丰富的操作符，可以进行复杂的异步操作和流处理。

<br/>

### 示例代码

```typescript
import { Controller, Get, OnModuleDestroy } from '@nestjs/common';
import { Observable, interval, Subscription } from 'rxjs';
import { map, takeWhile } from 'rxjs/operators';

@Controller('cats')
export class CatsController implements OnModuleDestroy {
  private subscription: Subscription;

  @Get('realtime')
  getRealTimeData(): Observable<string> {
    // 创建一个每秒发出一个值的 Observable
    const source$ = interval(1000).pipe(
      map((value) => `Real-time data #${value}`),
      takeWhile((value, index) => index < 10) // 只取前 10 个值
    );

    // 订阅 Observable 并保存订阅对象，以便在需要时取消订阅
    this.subscription = source$.subscribe((data) => console.log(data));

    return source$;
  }

  // 实现 OnModuleDestroy 接口，以便在模块销毁时取消订阅
  onModuleDestroy() {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }
}
```

<br/>

### 解释

1. **多次执行**：
   - `interval(1000)` 创建一个每秒发出一个值的 `Observable`。
   - `map((value) => `Real-time data #${value}`)` 将每个值映射为一个字符串。
   - `takeWhile((value, index) => index < 10)` 只取前 10 个值。
2. **可取消性**：
   - `this.subscription = source$.subscribe((data) => console.log(data))` 订阅 `Observable` 并保存订阅对象。
   - 在 `onModuleDestroy` 方法中，调用 `this.subscription.unsubscribe()` 取消订阅，以便在模块销毁时停止接收数据。
3. **强大的操作符**：
   - 使用 `map` 操作符将每个值映射为一个字符串。
   - 使用 `takeWhile` 操作符只取前 10 个值。

> 通过这个示例，你可以看到 `Observable` 如何在 NestJS 中处理实时数据流，并展示了它的多次执行、可取消性和强大的操作符特性。

<br/>

### 选择使用哪一个

- **简单异步操作**：如果你的异步操作只需要处理单个结果，`Promise` 是一个更简单的选择。
- **复杂异步操作**：如果你需要处理多个结果、流操作、或需要取消异步操作，`Observable` 提供了更强大的功能。

<br/>

> 【总结】：
> 
> - Promise**：适用于简单的、一次性的异步操作。
> - **Observable**：适用于复杂的、多次的异步操作，特别是在需要取消操作或处理数据流时。

<br/>

## 7. 数据库结构（DTO）

```typescript
/* create-cat.dto.ts */
// 定义数据结构
export class CreateCatDto {
  name: string;
  age: number;
  breed: string;
}

/* cats.controller.ts */
// 使用数据库结构，方便代码提示与类型限制
// 在接受请求体的时候，会自动过滤掉不在请求提中的字段
@Post()
async create(@Body() createCatDto: CreateCatDto) {
  return 'This action adds a new cat';
}

```

<br/>

## 8. 一键生成完整模块

```powershell
# 生成模块 (nest g mo) 以保持代码井井有条并建立清晰的边界（对相关组件进行分组）
nest g mo <模块名称>

# 生成控制器 (nest g co) 来定义 CRUD 路由（或 GraphQL 应用的查询/变更）
nest g co <控制器名称>

# 生成服务 (nest g s) 以实现和隔离业务逻辑
nest g s <服务名称>

# 生成一个实体类(DTO)/接口来表示资源数据形状
# 生成数据传输对象（或 GraphQL 应用的输入）以定义数据将如何通过网络发送


# 创建一个上述所有的代码
# 不仅生成所有 NestJS 构建块（模块、服务、控制器类），还生成实体类、DTO 类以及测试 (.spec) 文件。
nest g resource <资源的名称>
```

<br/>

### 8.1. 关于传输层的选择

在使用 `nest g resource` 命令生成资源时，NestJS CLI 会询问你关于传输层（Transport Layer）的选择。不同的传输层选项对应不同的通信协议和使用场景。以下是各个选项的详细解释：

<br/>

### 选项解释

1. **REST API**:
   - **描述**: 使用 HTTP 协议，通过 RESTful 风格的 API 进行通信。
   - **使用场景**: 适用于传统的 Web 应用程序和需要通过 HTTP 请求进行通信的场景。
   - **示例**: `GET /cats`, `POST /cats`, `PUT /cats/:id`, `DELETE /cats/:id`
2. **GraphQL (code first)**:
   - **描述**: 使用 GraphQL 协议，通过代码优先的方式定义 GraphQL 架构。
   - **使用场景**: 适用于需要灵活查询和操作数据的场景，特别是前端需要精确控制数据获取的应用。
   - **示例**: `query { cats { id name } }`, `mutation { createCat(name: "Tom") { id name } }`
3. **GraphQL (schema first)**:
   - **描述**: 使用 GraphQL 协议，通过模式优先的方式定义 GraphQL 架构。
   - **使用场景**: 适用于需要灵活查询和操作数据的场景，特别是前端需要精确控制数据获取的应用。与代码优先不同，模式优先是先定义 GraphQL 模式文件。
   - **示例**: `query { cats { id name } }`, `mutation { createCat(name: "Tom") { id name } }`
4. **Microservice (non-HTTP)**:
   - **描述**: 使用非HTTP服务（Redis、TCP等）进行微服务间的通信。
   - **使用场景**: 适用于需要高性能、低延迟的微服务架构，服务之间通过 非HTTP服务进行通信。
   - **示例**: 服务 A 通过 TCP 发送请求到服务 B，服务 B 处理请求并返回结果。
5. **WebSockets**:
   - **描述**: 使用 WebSocket 协议进行实时双向通信。
   - **使用场景**: 适用于需要实时更新和双向通信的应用，例如聊天应用、实时通知等。
   - **示例**: 客户端和服务器之间通过 WebSocket 连接进行实时数据传输。

<br/>

## 9. 启动并运行

```typescript
import { Module } from '@nestjs/common';
import { CatsController } from './cats/cats.controller';

@Module({
  // 将 CatsController 注入到 Module 中让这个控制器生效
  controllers: [CatsController],
})
export class AppModule {}
```
